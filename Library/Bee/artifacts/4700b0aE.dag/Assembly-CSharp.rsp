-target:library
-out:"Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2022_3_55
-define:UNITY_2022_3
-define:UNITY_2022
-define:TUAN<PERSON>IE_1_5_3
-define:TUANJIE_1_5
-define:TUAN<PERSON>IE_1
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:TUANJIE_2022_3_OR_NEWER
-define:TUANJIE_1_0_OR_NEWER
-define:TUANJIE_1_1_OR_NEWER
-define:TUANJIE_1_2_OR_NEWER
-define:TUANJIE_1_3_OR_NEWER
-define:TUANJIE_1_4_OR_NEWER
-define:TUANJIE_1_5_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AUDIO
-define:ENABLE_CLOTH
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_INSTANTASSET_EDITOR
-define:ENABLE_TEXTURE_MANAGER
-define:ENABLE_UNITYEVENTS
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_WEBGL
-define:PLATFORM_WEIXINMINIGAME
-define:PLATFORM_MINIGAME
-define:TEXTCORE_1_0_OR_NEWER
-define:WEIXINMINIGAME
-define:UNITY_WEBGL
-define:UNITY_WEBGL_API
-define:UNITY_WEIXINMINIGAME
-define:UNITY_WEIXINMINIGAME_API
-define:TUANJIE_WEIXINMINIGAME
-define:TUANJIE_WEIXINMINIGAME_API
-define:UNITY_MINIGAME
-define:UNITY_MINIGAME_API
-define:TUANJIE_MINIGAME
-define:TUANJIE_MINIGAME_API
-define:UNITY_DISABLE_WEB_VERIFICATION
-define:UNITY_GFX_USE_PLATFORM_VSYNC
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:DOTWEEN
-define:MINIGAME_SUBPLATFORM_WEIXIN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/AndroidPlayer/Unity.Android.GradleProject.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/WeixinMiniGameSupport/Managed/UnityEngine.WeixinMiniGameModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.HMISimulatorModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AndroidAppViewModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.InfinityModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.OpenHarmonyJSModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"Assets/DOTween/DOTween.dll"
-r:"Library/PackageCache/com.qq.weixin.minigame@4443e5a9bc/Runtime/Plugins/LitJson.dll"
-r:"Library/PackageCache/com.qq.weixin.minigame@4443e5a9bc/Runtime/Plugins/Unity.FontABTool.dll"
-r:"Library/PackageCache/com.qq.weixin.minigame@4443e5a9bc/Runtime/Plugins/wx-runtime-editor.dll"
-r:"Library/PackageCache/com.unity.collections@1.2.4/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/Wx.ref.dll"
-r:"Library/Bee/artifacts/4700b0aE.dag/WxEditor.ref.dll"
-analyzer:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/DOTween/Modules/DOTweenModuleAudio.cs"
"Assets/DOTween/Modules/DOTweenModuleEPOOutline.cs"
"Assets/DOTween/Modules/DOTweenModulePhysics.cs"
"Assets/DOTween/Modules/DOTweenModulePhysics2D.cs"
"Assets/DOTween/Modules/DOTweenModuleSprite.cs"
"Assets/DOTween/Modules/DOTweenModuleUI.cs"
"Assets/DOTween/Modules/DOTweenModuleUnityVersion.cs"
"Assets/DOTween/Modules/DOTweenModuleUtils.cs"
"Assets/Layer Lab/GUI Pro-CasualGame/Extensions/UIParticle/UIParticleSystem.cs"
"Assets/Layer Lab/GUI Pro-CasualGame/Scripts/PanelCasualGame.cs"
"Assets/Layer Lab/GUI Pro-CasualGame/Scripts/PanelControlCasualGame.cs"
"Assets/Scripts/Engine/Extensions/TransformExtensions.cs"
"Assets/Scripts/Engine/GameLauncher.cs"
"Assets/Scripts/Engine/Localization/CSVLocalizationLoader.cs"
"Assets/Scripts/Engine/Localization/LanguageType.cs"
"Assets/Scripts/Engine/Localization/LocalizationData.cs"
"Assets/Scripts/Engine/Localization/LocalizationHelper.cs"
"Assets/Scripts/Engine/Localization/LocalizationManager.cs"
"Assets/Scripts/Engine/Localization/LocalizedText.cs"
"Assets/Scripts/Engine/Localization/TextID.cs"
"Assets/Scripts/Engine/System/AdSystem.cs"
"Assets/Scripts/Engine/System/AnalyticsSystem.cs"
"Assets/Scripts/Engine/System/AudioManager.cs"
"Assets/Scripts/Engine/System/FontManager.cs"
"Assets/Scripts/Engine/System/GuideSystem.cs"
"Assets/Scripts/Engine/System/IAPSystem.cs"
"Assets/Scripts/Engine/System/MailSystem.cs"
"Assets/Scripts/Engine/System/SaveSystem.cs"
"Assets/Scripts/Engine/System/UISystem.cs"
"Assets/Scripts/Engine/UI/GuideSystem.cs"
"Assets/Scripts/Engine/UI/IGuideAnchorProvider.cs"
"Assets/Scripts/Engine/UI/IParticleAnchor.cs"
"Assets/Scripts/Engine/UI/MessageManager.cs"
"Assets/Scripts/Engine/UI/ParticleSystemManager.cs"
"Assets/Scripts/Engine/UI/UIAnamation.cs"
"Assets/Scripts/Engine/UI/UIBase.cs"
"Assets/Scripts/Engine/UI/UIConfig.cs"
"Assets/Scripts/Engine/UI/UIInfo.cs"
"Assets/Scripts/Engine/UI/UILayer.cs"
"Assets/Scripts/Engine/UI/UIName.cs"
"Assets/Scripts/Game/BlindBoxData.cs"
"Assets/Scripts/Game/CompilationTest.cs"
"Assets/Scripts/Game/ComprehensiveTest.cs"
"Assets/Scripts/Game/Core/GameConditionProcessor.cs"
"Assets/Scripts/Game/Core/GameEffectProcessor.cs"
"Assets/Scripts/Game/Core/GameEffectSystem.cs"
"Assets/Scripts/Game/Database/AchievementDatabase.cs"
"Assets/Scripts/Game/Database/BlindBoxDatabase.cs"
"Assets/Scripts/Game/DataLoader/AchievementDataLoader.cs"
"Assets/Scripts/Game/DataLoader/BlindBoxDataLoader.cs"
"Assets/Scripts/Game/DataLoader/GlobalSettingsDataLoader.cs"
"Assets/Scripts/Game/DataLoader/TalentDataLoader.cs"
"Assets/Scripts/Game/GameEnums.cs"
"Assets/Scripts/Game/GameFlowController.cs"
"Assets/Scripts/Game/GameLogic.cs"
"Assets/Scripts/Game/GameStatistics.cs"
"Assets/Scripts/Game/MetaGameData.cs"
"Assets/Scripts/Game/MetaGameTester.cs"
"Assets/Scripts/Game/PlayerData.cs"
"Assets/Scripts/Game/ResourceManager.cs"
"Assets/Scripts/Game/System/AchievementSystem.cs"
"Assets/Scripts/Game/System/BlackMarketSystem.cs"
"Assets/Scripts/Game/System/BlindBoxSystem.cs"
"Assets/Scripts/Game/System/EventSystem.cs"
"Assets/Scripts/Game/System/GlobalSettings.cs"
"Assets/Scripts/Game/System/MetaGameSystem.cs"
"Assets/Scripts/Game/System/PrivilegeSystem.cs"
"Assets/Scripts/Game/System/SocialClassSystem.cs"
"Assets/Scripts/Game/System/TalentSystem.cs"
"Assets/Scripts/Game/System/WorkSystem.cs"
"Assets/Scripts/UI/AchievementPanel.cs"
"Assets/Scripts/UI/Alert.cs"
"Assets/Scripts/UI/BlackMarketPanel.cs"
"Assets/Scripts/UI/BlindBoxResultPopup.cs"
"Assets/Scripts/UI/EventDialog.cs"
"Assets/Scripts/UI/GameOverPanel.cs"
"Assets/Scripts/UI/GamePanel.cs"
"Assets/Scripts/UI/LoadingPanel.cs"
"Assets/Scripts/UI/MetaGamePanel.cs"
"Assets/Scripts/UI/PausePopup.cs"
"Assets/Scripts/UI/PrivilegePanel.cs"
"Assets/Scripts/UI/PromotionPanel.cs"
"Assets/Scripts/UI/RankingItem.cs"
"Assets/Scripts/UI/RankingPanel.cs"
"Assets/Scripts/UI/SettingsPanel.cs"
"Assets/Scripts/UI/SplashPanel.cs"
"Assets/Scripts/UI/TruthDiscoveryPopup.cs"
"Assets/Scripts/UI/UIManager.cs"
"Assets/Scripts/UI/WorkPanel.cs"
"Assets/Scripts/UI/WorkResultPopup.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"