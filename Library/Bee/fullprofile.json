{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 1443, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 1443, "ts": 1752765114179197, "dur": 2686, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 1443, "ts": 1752765114186904, "dur": 704, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752765111811340, "dur": 8303, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752765111819649, "dur": 65125, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752765111884782, "dur": 44699, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 1443, "ts": 1752765114187613, "dur": 109, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111809433, "dur": 11906, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111821342, "dur": 2313656, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111822399, "dur": 6448, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111828854, "dur": 977, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111829834, "dur": 4171, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834011, "dur": 369, "ph": "X", "name": "ProcessMessages 5546", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834382, "dur": 43, "ph": "X", "name": "ReadAsync 5546", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834427, "dur": 2, "ph": "X", "name": "ProcessMessages 7545", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834430, "dur": 38, "ph": "X", "name": "ReadAsync 7545", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834471, "dur": 31, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834505, "dur": 45, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834553, "dur": 1, "ph": "X", "name": "ProcessMessages 1330", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834555, "dur": 357, "ph": "X", "name": "ReadAsync 1330", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834914, "dur": 3, "ph": "X", "name": "ProcessMessages 8119", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834919, "dur": 58, "ph": "X", "name": "ReadAsync 8119", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834979, "dur": 1, "ph": "X", "name": "ProcessMessages 1708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111834982, "dur": 188, "ph": "X", "name": "ReadAsync 1708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835172, "dur": 1, "ph": "X", "name": "ProcessMessages 1749", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835174, "dur": 34, "ph": "X", "name": "ReadAsync 1749", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835210, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835212, "dur": 46, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835260, "dur": 39, "ph": "X", "name": "ReadAsync 1404", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835301, "dur": 42, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835346, "dur": 253, "ph": "X", "name": "ReadAsync 1211", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835614, "dur": 2, "ph": "X", "name": "ProcessMessages 5195", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835617, "dur": 341, "ph": "X", "name": "ReadAsync 5195", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835961, "dur": 1, "ph": "X", "name": "ProcessMessages 1719", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111835963, "dur": 38, "ph": "X", "name": "ReadAsync 1719", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836003, "dur": 39, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836044, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836046, "dur": 80, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836127, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836130, "dur": 33, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836164, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836166, "dur": 216, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836384, "dur": 1, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836386, "dur": 37, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836424, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836426, "dur": 331, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836759, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111836761, "dur": 336, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837098, "dur": 3, "ph": "X", "name": "ProcessMessages 3768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837102, "dur": 44, "ph": "X", "name": "ReadAsync 3768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837148, "dur": 1, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837150, "dur": 45, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837199, "dur": 36, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837236, "dur": 39, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837278, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837280, "dur": 35, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837316, "dur": 1, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837318, "dur": 54, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837375, "dur": 34, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837412, "dur": 32, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837454, "dur": 39, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837495, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837496, "dur": 82, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837580, "dur": 1, "ph": "X", "name": "ProcessMessages 1723", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837582, "dur": 46, "ph": "X", "name": "ReadAsync 1723", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837630, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837631, "dur": 165, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837799, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837800, "dur": 44, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837846, "dur": 42, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837891, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837892, "dur": 38, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837932, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837944, "dur": 39, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111837987, "dur": 32, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838020, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838022, "dur": 339, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838363, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838365, "dur": 36, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838403, "dur": 41, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838446, "dur": 40, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838488, "dur": 40, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838531, "dur": 38, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838571, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838578, "dur": 47, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838628, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838630, "dur": 337, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838969, "dur": 1, "ph": "X", "name": "ProcessMessages 1483", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111838971, "dur": 48, "ph": "X", "name": "ReadAsync 1483", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839021, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839022, "dur": 44, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839069, "dur": 40, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839110, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839112, "dur": 100, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839213, "dur": 1, "ph": "X", "name": "ProcessMessages 1382", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839215, "dur": 36, "ph": "X", "name": "ReadAsync 1382", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839253, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839255, "dur": 58, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839315, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839317, "dur": 57, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839376, "dur": 1, "ph": "X", "name": "ProcessMessages 1247", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839378, "dur": 62, "ph": "X", "name": "ReadAsync 1247", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839442, "dur": 1, "ph": "X", "name": "ProcessMessages 1505", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839445, "dur": 37, "ph": "X", "name": "ReadAsync 1505", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839483, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839485, "dur": 311, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839798, "dur": 1, "ph": "X", "name": "ProcessMessages 1463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839800, "dur": 40, "ph": "X", "name": "ReadAsync 1463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839841, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839843, "dur": 146, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839992, "dur": 1, "ph": "X", "name": "ProcessMessages 1414", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111839994, "dur": 53, "ph": "X", "name": "ReadAsync 1414", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840048, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840050, "dur": 42, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840094, "dur": 40, "ph": "X", "name": "ReadAsync 1098", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840136, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840138, "dur": 43, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840196, "dur": 52, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840259, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840261, "dur": 226, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840488, "dur": 1, "ph": "X", "name": "ProcessMessages 1631", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840490, "dur": 43, "ph": "X", "name": "ReadAsync 1631", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840534, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840536, "dur": 165, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840702, "dur": 1, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840704, "dur": 38, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840744, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840746, "dur": 86, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840834, "dur": 1, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840836, "dur": 40, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840878, "dur": 102, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840982, "dur": 1, "ph": "X", "name": "ProcessMessages 1469", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111840984, "dur": 62, "ph": "X", "name": "ReadAsync 1469", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841049, "dur": 341, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841391, "dur": 1, "ph": "X", "name": "ProcessMessages 1949", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841394, "dur": 165, "ph": "X", "name": "ReadAsync 1949", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841561, "dur": 6, "ph": "X", "name": "ProcessMessages 3110", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841568, "dur": 128, "ph": "X", "name": "ReadAsync 3110", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841698, "dur": 2, "ph": "X", "name": "ProcessMessages 3136", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841702, "dur": 92, "ph": "X", "name": "ReadAsync 3136", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841796, "dur": 1, "ph": "X", "name": "ProcessMessages 2138", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111841797, "dur": 217, "ph": "X", "name": "ReadAsync 2138", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842016, "dur": 2, "ph": "X", "name": "ProcessMessages 3380", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842019, "dur": 58, "ph": "X", "name": "ReadAsync 3380", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842080, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842091, "dur": 71, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842163, "dur": 1, "ph": "X", "name": "ProcessMessages 1578", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842165, "dur": 74, "ph": "X", "name": "ReadAsync 1578", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842241, "dur": 1, "ph": "X", "name": "ProcessMessages 1536", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842242, "dur": 48, "ph": "X", "name": "ReadAsync 1536", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842294, "dur": 263, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842558, "dur": 1, "ph": "X", "name": "ProcessMessages 1928", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111842560, "dur": 481, "ph": "X", "name": "ReadAsync 1928", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843043, "dur": 4, "ph": "X", "name": "ProcessMessages 8027", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843047, "dur": 219, "ph": "X", "name": "ReadAsync 8027", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843268, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843270, "dur": 56, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843328, "dur": 1, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843330, "dur": 34, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843365, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843367, "dur": 32, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843400, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843402, "dur": 32, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843436, "dur": 102, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843896, "dur": 20, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843917, "dur": 3, "ph": "X", "name": "ProcessMessages 5898", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111843921, "dur": 159, "ph": "X", "name": "ReadAsync 5898", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111844082, "dur": 528, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111844613, "dur": 220, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111844836, "dur": 447, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111845292, "dur": 127, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111845422, "dur": 331, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111845755, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111845757, "dur": 18003, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111863847, "dur": 6, "ph": "X", "name": "ProcessMessages 8130", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111863856, "dur": 413, "ph": "X", "name": "ReadAsync 8130", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111864273, "dur": 3, "ph": "X", "name": "ProcessMessages 1442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111864277, "dur": 1353, "ph": "X", "name": "ReadAsync 1442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111865644, "dur": 9, "ph": "X", "name": "ProcessMessages 2626", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111865661, "dur": 221, "ph": "X", "name": "ReadAsync 2626", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111865885, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111865887, "dur": 152, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111866041, "dur": 5150, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111871194, "dur": 5, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111873587, "dur": 363, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111873952, "dur": 4, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111873957, "dur": 59, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111874032, "dur": 1, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111874039, "dur": 23, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111874065, "dur": 21, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111874087, "dur": 447, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111874535, "dur": 2, "ph": "X", "name": "ProcessMessages 2735", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111874538, "dur": 6767, "ph": "X", "name": "ReadAsync 2735", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881311, "dur": 5, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881317, "dur": 67, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881386, "dur": 2, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881389, "dur": 59, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881450, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881452, "dur": 516, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881970, "dur": 1, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111881974, "dur": 38, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882013, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882015, "dur": 33, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882050, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882051, "dur": 614, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882668, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882671, "dur": 269, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111882942, "dur": 158, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883102, "dur": 251, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883356, "dur": 86, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883445, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883447, "dur": 89, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883538, "dur": 99, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883642, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883679, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111883719, "dur": 286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884006, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884008, "dur": 52, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884063, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884101, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884224, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884283, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884369, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884443, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884563, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884565, "dur": 114, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884682, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884749, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884751, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884792, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884793, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111884828, "dur": 359, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885190, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885573, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885575, "dur": 120, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885697, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885699, "dur": 145, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885846, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885847, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885886, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111885887, "dur": 462, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886351, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886355, "dur": 108, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886466, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886499, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886539, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886802, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886804, "dur": 59, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886866, "dur": 78, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886947, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886985, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111886986, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887028, "dur": 221, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887259, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887341, "dur": 47, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887391, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887428, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887430, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887510, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887511, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887588, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887631, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887633, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887707, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887764, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887814, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887945, "dur": 45, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111887993, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888038, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888123, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888124, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888156, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888193, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888252, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888254, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888306, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888339, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888362, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888483, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888516, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888567, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888613, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888656, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888657, "dur": 86, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888746, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888816, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888852, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888853, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888900, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888935, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111888974, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889010, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889082, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889120, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889176, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889261, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889263, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889328, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889367, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889461, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889502, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889614, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889663, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889665, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889700, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889740, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889784, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889825, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889919, "dur": 11, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889932, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889977, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111889979, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111890020, "dur": 9397, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111899616, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111899618, "dur": 418, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111900055, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111900308, "dur": 517, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111900827, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111900829, "dur": 3069, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111903900, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111903902, "dur": 20393, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111924342, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111924346, "dur": 3251, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111927602, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111927605, "dur": 154, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111927763, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111927764, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111927838, "dur": 86, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111927926, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928022, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928159, "dur": 472, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928634, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928757, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928760, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928821, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928894, "dur": 24, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111928920, "dur": 339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111929262, "dur": 207, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111929473, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111929512, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111929568, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111929921, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111929971, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930064, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930240, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930389, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930431, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930546, "dur": 338, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930887, "dur": 62, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111930952, "dur": 153, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111931107, "dur": 457, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111931567, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111931607, "dur": 13, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111931622, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111931668, "dur": 440, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111932136, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111932269, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111932417, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111932822, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111932992, "dur": 390, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111933384, "dur": 260, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111933645, "dur": 166, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111933815, "dur": 222, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934039, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934040, "dur": 528, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934569, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934571, "dur": 155, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934729, "dur": 123, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934854, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934855, "dur": 75, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934935, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934936, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111934991, "dur": 55, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111935049, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111935114, "dur": 203, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111935320, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111935443, "dur": 333, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111935781, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111935884, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111936222, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111937488, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111937525, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111937526, "dur": 122, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111937650, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111937739, "dur": 346, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111938087, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765111938089, "dur": 116792, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112054888, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112054891, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112054948, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112054996, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112055035, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112055090, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112055146, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112055176, "dur": 1069, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112056247, "dur": 2969, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112059220, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112059222, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112059333, "dur": 354, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112059689, "dur": 427, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112060120, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112060123, "dur": 474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112060600, "dur": 561, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112061164, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112061165, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112061303, "dur": 808, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112062119, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112062124, "dur": 1228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112063355, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112063520, "dur": 488, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112064015, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112064018, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112064214, "dur": 849, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112065066, "dur": 1035, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112066104, "dur": 1125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112067238, "dur": 384, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112067625, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112067865, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112068152, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112068247, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112068389, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112068499, "dur": 3069, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112071571, "dur": 332, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112071907, "dur": 448, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112072358, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112072359, "dur": 250, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112072612, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112072720, "dur": 2126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112074849, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112074924, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075002, "dur": 322, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075327, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075544, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075577, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075626, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075674, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075720, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075784, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112075921, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076021, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076068, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076116, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076262, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076353, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076354, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076393, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076426, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076465, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076557, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076632, "dur": 80, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076724, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076834, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076877, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112076958, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077056, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077129, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077168, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077217, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077273, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077326, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077376, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077418, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077509, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077547, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077631, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077691, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077757, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077798, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077883, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077885, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077935, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077980, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112077982, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112078042, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112078074, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112078106, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112078386, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112078467, "dur": 981, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112079450, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112079470, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112079579, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765112079600, "dur": 2040237, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765114119868, "dur": 9, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765114119882, "dur": 581, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765114120470, "dur": 221, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765114120695, "dur": 7215, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765114127919, "dur": 796, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752765114128719, "dur": 6151, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 1443, "ts": 1752765114187723, "dur": 1235, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 8589934592, "ts": 1752765111806633, "dur": 122960, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752765111929595, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752765111929601, "dur": 2477, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 1443, "ts": 1752765114188960, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 4294967296, "ts": 1752765111756272, "dur": 2381210, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752765111762609, "dur": 37601, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752765114148901, "dur": 4548, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752765114151750, "dur": 359, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752765114153508, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 1443, "ts": 1752765114188969, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752765111815761, "dur": 3201, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765111819002, "dur": 14892, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765111833947, "dur": 57, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752765111834004, "dur": 117, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765111835459, "dur": 143, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752765111850526, "dur": 13732, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752765111868778, "dur": 2908, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752765111874082, "dur": 381, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752765111881662, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752765111834128, "dur": 48977, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765111883112, "dur": 2243931, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765114127124, "dur": 64, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765114127260, "dur": 72, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765114127393, "dur": 66, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765114127895, "dur": 120, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765114128100, "dur": 1419, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752765111834062, "dur": 49061, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111883151, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752765111883363, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111883750, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111883857, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111883966, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111884156, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111884240, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111884380, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111884482, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111884732, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111884846, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111885025, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111885142, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111885275, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111885388, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111885541, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111885640, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111885813, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111885920, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111886021, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111886136, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111886279, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111886413, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111886476, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111886603, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752765111886695, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752765111886859, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111886926, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752765111887366, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111887470, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752765111888108, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888197, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752765111888247, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888361, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888438, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888518, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888650, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888776, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111888851, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752765111889260, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111889362, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111889451, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111889545, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111889638, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111889754, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752765111889905, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111890015, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111890120, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111890229, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111890347, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111890431, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111891747, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111892932, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111894027, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111895329, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111896833, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111898391, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111899801, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111901314, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111902573, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111904097, "dur": 1830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111905927, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111907195, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111908469, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111909663, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111910983, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111912476, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111913960, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111915479, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111917443, "dur": 2021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111919464, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111920857, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111922360, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111922916, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111923510, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111924016, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111924278, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111924415, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111924672, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111925233, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111925513, "dur": 5544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752765111931057, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111931238, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111931563, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111931619, "dur": 3863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752765111935482, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765111935831, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752765111936013, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752765111936970, "dur": 119857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112056828, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752765112059772, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112059847, "dur": 3799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752765112063647, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112063750, "dur": 3805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752765112067556, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112067626, "dur": 4372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752765112071999, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112072080, "dur": 3754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752765112075835, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112076592, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752765112076653, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112076828, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077148, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077354, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077504, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077562, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077633, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077837, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112077923, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078018, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078099, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078164, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078219, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752765112078275, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078386, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078504, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112078565, "dur": 1344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752765112079928, "dur": 2047209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111834064, "dur": 49071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111883153, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752765111883367, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111883761, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111883893, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111884056, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111884208, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111884277, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111884575, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111884699, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111884878, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111885013, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111885157, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111885272, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111885436, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111885564, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111885832, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111885937, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111886025, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111886114, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111886226, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111886335, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111886444, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111886520, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111886618, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111886810, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111886877, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111887162, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111887271, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111887345, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111887763, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111887849, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111888416, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111888500, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111888649, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111888731, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111888819, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111888913, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111888964, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889066, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889126, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889225, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889309, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889402, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889488, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889568, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889670, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111889858, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111889963, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111890080, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752765111890232, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111890358, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111890467, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111891764, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111892946, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111894063, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111895168, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111896572, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111898239, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111899598, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111901172, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111901257, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111902569, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111904082, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111905906, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111907186, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111908426, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111909645, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111911003, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111912471, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111913947, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111915487, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111917416, "dur": 2001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111919418, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111920853, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111922349, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111922933, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111923734, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111924100, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111924285, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111924356, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111924424, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111924686, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111925275, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111926013, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752765111926565, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111926789, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752765111929159, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111929423, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752765111929629, "dur": 1933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752765111931562, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111931843, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752765111932832, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111932946, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752765111933839, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765111933952, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752765111936647, "dur": 120209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765112056857, "dur": 3667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752765112060525, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765112060626, "dur": 3710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752765112064336, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765112064410, "dur": 4432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752765112068842, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765112068926, "dur": 4560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752765112073486, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752765112073665, "dur": 6225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752765112079945, "dur": 2047214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111834072, "dur": 49072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111883153, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111883707, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111883929, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111884061, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111884202, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111884261, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111884464, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111884748, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111884867, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111885101, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111885212, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111885357, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111885479, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111885614, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111885758, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111885908, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111885999, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752765111886267, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111886363, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111886665, "dur": 13382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752765111900048, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111900470, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111900562, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111900651, "dur": 3582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111904233, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111904329, "dur": 20134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752765111924466, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111924673, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111924784, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752765111925061, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111925232, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111925478, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111925542, "dur": 1626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752765111927168, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111927571, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_B00737B65E8631B3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111927630, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111927704, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752765111928083, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111928154, "dur": 816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752765111928971, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111929252, "dur": 3153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1752765111932457, "dur": 928, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765112055222, "dur": 430, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765111933801, "dur": 121868, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1752765112056815, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752765112059710, "dur": 4545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752765112064255, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765112064331, "dur": 3947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752765112068278, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765112068374, "dur": 4693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752765112073067, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765112073151, "dur": 5707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752765112078918, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752765112080087, "dur": 2047196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111834080, "dur": 49071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111883154, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111883788, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111883898, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111884098, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111884216, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111884456, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111884615, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111884768, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111884912, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111885071, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111885174, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111885318, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111885424, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111885595, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111885717, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111885880, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111885967, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752765111886181, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111886302, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111886439, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111886494, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752765111886769, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111887475, "dur": 11539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111899015, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111899516, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111899603, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111899721, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111901222, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111902548, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111904069, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111905912, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111907181, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111908030, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111909187, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111910648, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111912257, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111913626, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111915163, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111916911, "dur": 2122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111919033, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111920662, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111921938, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111923366, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111924011, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111924247, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111924432, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111924712, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111925278, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111925914, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111927291, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111927652, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_BB27E5BA9B36011C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111927759, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111928617, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111928677, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111930156, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111930291, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_905C1C40FD7BA469.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111930393, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111930487, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111930881, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111931657, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111933458, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111933626, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111935188, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111935480, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111935573, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111935632, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111935845, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111936251, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111937179, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111937339, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111937459, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111937873, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765111938101, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752765111938170, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752765114119832, "dur": 569, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752765111938549, "dur": 2182276, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752765111834087, "dur": 49071, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111883161, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111883768, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111883988, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111884122, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111884462, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111884656, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111884763, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111884971, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111885066, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111885210, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111885280, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111885438, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111885544, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111885705, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111885823, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111885964, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111886037, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111886130, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111886275, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111886370, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111886472, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111886583, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752765111886706, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752765111886810, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111886884, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752765111887221, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111887290, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111887394, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752765111887791, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111887880, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888002, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888056, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888121, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888232, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888329, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888402, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752765111888459, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888561, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888666, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888763, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111888843, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752765111888960, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889062, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889122, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889220, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889323, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889434, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889521, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889611, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889724, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752765111889885, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111889987, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111890107, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111890195, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111890332, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111890443, "dur": 2355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111892798, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111893917, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111895202, "dur": 1422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111896624, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111898259, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111899609, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111901187, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111901272, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111902582, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111904091, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111905916, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111907183, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111908447, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111909681, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111911051, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111912509, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111914155, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111915728, "dur": 1977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111917705, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111919664, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111921012, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111922557, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111923137, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111924266, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111924415, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111924698, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111925253, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111925960, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752765111927149, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111927547, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_39AF4F01BD1B7546.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111927598, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111927678, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111928011, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111928072, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111928421, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752765111929682, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111930065, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111930334, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111930455, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111930845, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111930920, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752765111932573, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111932796, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752765111933979, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111934069, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752765111934572, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111934656, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752765111936256, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765111936463, "dur": 120352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112056822, "dur": 4125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752765112060948, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112061166, "dur": 3495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752765112064661, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112064741, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752765112068680, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112068754, "dur": 4001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752765112072755, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112072835, "dur": 3731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752765112076566, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112076770, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752765112076965, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077092, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077246, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077467, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077530, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752765112077583, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077665, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077810, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112077900, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112078011, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112078113, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112078226, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112078436, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112078523, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752765112078941, "dur": 2048403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111834094, "dur": 49082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111883180, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111883741, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111883828, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111883929, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111884203, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111884268, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111884402, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111884497, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111884695, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111884802, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111884974, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111885080, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111885213, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111885332, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111885478, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111885574, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111885771, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111885880, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111886102, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111886189, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111886340, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111886430, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111886564, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752765111886772, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752765111887129, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111887188, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111887259, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111887329, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111887429, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752765111887737, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111887822, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111887941, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752765111888124, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111888219, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752765111888472, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111888568, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111888712, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111888784, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752765111888852, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111888946, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111889049, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752765111889217, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111889303, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111889416, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111889497, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111889576, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111889738, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752765111889982, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111890094, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111890178, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111890305, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111890417, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111891737, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111892929, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111894022, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111895292, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111896776, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111898361, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111899789, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111901277, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111902577, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111904078, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111905930, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111907207, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111908447, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111909651, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111910996, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111912463, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111913917, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111915438, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111917280, "dur": 2051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111919332, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111920799, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111922261, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111923551, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111923952, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111924239, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111924324, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111924393, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111924678, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111925235, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111925714, "dur": 1096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752765111926810, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111927070, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111927336, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752765111928928, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111929180, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_EF3566CB9F379C74.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111929308, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111929672, "dur": 2195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752765111931868, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111932114, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111932171, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111932356, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752765111933397, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111933602, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752765111934502, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111934637, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_3BDB24518B22122E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111934707, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111934865, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111935094, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752765111935432, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752765111936496, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765111936549, "dur": 120308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765112056857, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752765112060098, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765112060177, "dur": 3472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752765112063650, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765112063728, "dur": 3754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752765112067482, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765112067649, "dur": 3998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752765112071647, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765112071823, "dur": 3618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752765112075441, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752765112075525, "dur": 4476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752765112080054, "dur": 2047187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111834101, "dur": 49084, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111883187, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111883767, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111883892, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111884000, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111884203, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111884265, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111884395, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111884510, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111884654, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111884744, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111884921, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111885027, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111885181, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111885278, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111885437, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111885527, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111885696, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111885814, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111885955, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111886022, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111886148, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111886301, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111886438, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111886507, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752765111886775, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752765111887273, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111887371, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752765111887902, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111887980, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888041, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752765111888229, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888337, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888413, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888481, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888581, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888703, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888793, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888911, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111888970, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889080, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889161, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889236, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889337, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889431, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889511, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889606, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111889800, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752765111889954, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111890059, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111890149, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111890253, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111890375, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111890478, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111891773, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111892969, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111894095, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111895389, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111896925, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111898481, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111899935, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111901430, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111902714, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111904313, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111906091, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111907284, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111908463, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111909639, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111910989, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111912461, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111913928, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111915426, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111917321, "dur": 2033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111919354, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111920808, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111922297, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111923555, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111923951, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111924394, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111924694, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111925485, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111926060, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752765111926804, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111926941, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_AEDA0D01FA0B3E02.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111927009, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111927099, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111927314, "dur": 3280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752765111930595, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111930817, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111931342, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111931400, "dur": 2866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752765111934266, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111934442, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111934519, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_BEADB10929D2C8CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111934605, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111934779, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_DAC173EE5EC432FE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111934848, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111934957, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111935260, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752765111936248, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111936419, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765111937343, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752765111937444, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752765111937750, "dur": 119085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765112056877, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752765112059306, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765112059402, "dur": 4489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752765112063891, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765112063969, "dur": 4115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752765112068084, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765112068207, "dur": 4103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752765112072310, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765112072394, "dur": 6304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752765112078746, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752765112080046, "dur": 2047144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111834108, "dur": 49084, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111883195, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111883755, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111883892, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111883998, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111884238, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111884320, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111884558, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111884807, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111884955, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111885097, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111885195, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111885353, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111885458, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111885614, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111885733, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111885894, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111885978, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752765111886266, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111886342, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111886513, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111886755, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752765111886929, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111887175, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111887252, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111887311, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111887454, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111887680, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111888470, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111888558, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111888658, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111888770, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111888826, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111888930, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111889017, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111889146, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111889282, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111889382, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111889466, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111889740, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111889858, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752765111889955, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111890069, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111890163, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111890282, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111890400, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111891733, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111892927, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111894016, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111895289, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111896788, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111898381, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111899813, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111901371, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111902645, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111904199, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111905996, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111907243, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111908420, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111909664, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111911023, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111912479, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111913974, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111915472, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111917368, "dur": 1997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111919366, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111920823, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111922319, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111923332, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111924237, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111924347, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111924423, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111924684, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111925255, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111925869, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111926191, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752765111927260, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111927617, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111928211, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111928266, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111928522, "dur": 1734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752765111930256, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111930431, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111931038, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752765111932413, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111932699, "dur": 3483, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111936182, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752765111936663, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752765111937083, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765111937229, "dur": 119612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765112056841, "dur": 5488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752765112062330, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765112062503, "dur": 5177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752765112067680, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765112067757, "dur": 4024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752765112071781, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765112071871, "dur": 3391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752765112075262, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752765112075359, "dur": 4635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752765112080033, "dur": 2047269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111834116, "dur": 49082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111883202, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111883700, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111883781, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111883939, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111884072, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111884213, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111884288, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111884542, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111884709, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111884867, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111884988, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111885148, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111885246, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111885402, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111885504, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111885688, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111885800, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111885953, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111886077, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111886148, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111886297, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111886429, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111886487, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111886636, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752765111886811, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111886870, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752765111887127, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111887202, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111887274, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111887382, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111887486, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752765111887846, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111887958, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888082, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888155, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888251, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888365, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752765111888421, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888493, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888595, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888714, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111888798, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752765111888919, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889001, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889118, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889211, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889315, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889399, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889483, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889564, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111889684, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752765111889901, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752765111890056, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111890157, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111890262, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111890382, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111891712, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111892909, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111894013, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111895286, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111896721, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111898299, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111899740, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111901204, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111902532, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111904045, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111905886, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111907166, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111908020, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111909181, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111910447, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111911997, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111913385, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111914899, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111916471, "dur": 2112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111918583, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111920307, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111921541, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111923074, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111924233, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111924337, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111924420, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111924680, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111925241, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111925473, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111925570, "dur": 1661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752765111927231, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111927520, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_82D6D4EB628A8BAE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111927633, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111928327, "dur": 1395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752765111929723, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111929879, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_C193AA5F93FF728B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111929975, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111930094, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111930576, "dur": 763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111931373, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111931993, "dur": 2933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752765111934926, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111935190, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752765111936257, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111936440, "dur": 1665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765111938105, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752765111938183, "dur": 118641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112056829, "dur": 3499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752765112060328, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112060406, "dur": 4160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752765112064566, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112064640, "dur": 3919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752765112068560, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112068647, "dur": 4025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752765112072672, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112072746, "dur": 3932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752765112076678, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112076822, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112076888, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112076951, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077011, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077082, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077142, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077242, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077388, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077510, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077629, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077738, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112077840, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078067, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078170, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078303, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078373, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078430, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078495, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112078750, "dur": 1275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752765112080057, "dur": 2047266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111834122, "dur": 49083, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111883205, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111883786, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111883870, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111884089, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111884177, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111884289, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111884394, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111884530, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111884662, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111884819, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111884968, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111885103, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111885197, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111885356, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111885464, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111885658, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111885789, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111885951, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111886011, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752765111886504, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111886599, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752765111886803, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111886889, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111886967, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752765111887140, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111887228, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752765111887794, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111887895, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111887975, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888029, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888098, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888198, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888279, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888387, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888466, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888548, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888650, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888754, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111888835, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752765111889012, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889106, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889195, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889293, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889391, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889482, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889561, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889665, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111889769, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752765111889903, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111890021, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111890132, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111890236, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111890353, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111890452, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111891753, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111892939, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111894050, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111895346, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111896842, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111898378, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111899798, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111901290, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111902610, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111904168, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111905970, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111907232, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111908452, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111909631, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111910972, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111912468, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111913940, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111915446, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111917334, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111919343, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111920828, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111922309, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111923595, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111924100, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111924299, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111924421, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111924681, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111925239, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111925889, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111925984, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752765111927376, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111927592, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_4D635C4BE46FE4CD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111927651, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111927726, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111928117, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752765111930497, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111930877, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111930969, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111931449, "dur": 1703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752765111933152, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111933430, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111933760, "dur": 2197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752765111935958, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111936147, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752765111936573, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752765111937036, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765111937213, "dur": 119652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765112056866, "dur": 4759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752765112061625, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765112061697, "dur": 4410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752765112066108, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765112066316, "dur": 5432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752765112071748, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765112071827, "dur": 3530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752765112075358, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752765112075432, "dur": 4618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752765112080080, "dur": 2047186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752765114132349, "dur": 1699, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 1443, "ts": 1752765114190013, "dur": 3408, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 1443, "ts": 1752765114193545, "dur": 1451, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 1443, "ts": 1752765114185740, "dur": 9928, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}