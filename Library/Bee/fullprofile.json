{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 1296, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 1296, "ts": 1752746259445413, "dur": 573, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259450042, "dur": 1072, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254794534, "dur": 9744, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254804280, "dur": 4632142, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254804328, "dur": 63, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254804483, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254804484, "dur": 125005, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254929499, "dur": 47, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254929548, "dur": 932, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930493, "dur": 2, "ph": "X", "name": "ProcessMessages 2137", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930496, "dur": 35, "ph": "X", "name": "ReadAsync 2137", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930533, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930537, "dur": 60, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930679, "dur": 2, "ph": "X", "name": "ProcessMessages 1802", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930682, "dur": 42, "ph": "X", "name": "ReadAsync 1802", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930734, "dur": 1, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930736, "dur": 53, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930793, "dur": 1, "ph": "X", "name": "ProcessMessages 2122", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930795, "dur": 41, "ph": "X", "name": "ReadAsync 2122", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930838, "dur": 24, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930864, "dur": 18, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930884, "dur": 88, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930982, "dur": 1, "ph": "X", "name": "ProcessMessages 1480", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254930984, "dur": 31, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931016, "dur": 2, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931019, "dur": 22, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931043, "dur": 41, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931086, "dur": 19, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931107, "dur": 27, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931136, "dur": 32, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931170, "dur": 20, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931193, "dur": 37, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931232, "dur": 25, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931277, "dur": 31, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931310, "dur": 33, "ph": "X", "name": "ReadAsync 1543", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931345, "dur": 23, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931370, "dur": 24, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931396, "dur": 30, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931428, "dur": 33, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931463, "dur": 23, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931489, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931509, "dur": 20, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931531, "dur": 18, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931551, "dur": 35, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931588, "dur": 38, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931628, "dur": 20, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931650, "dur": 23, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931675, "dur": 33, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931710, "dur": 54, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254931767, "dur": 371, "ph": "X", "name": "ReadAsync 1652", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932140, "dur": 3, "ph": "X", "name": "ProcessMessages 8106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932144, "dur": 57, "ph": "X", "name": "ReadAsync 8106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932203, "dur": 6, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932210, "dur": 32, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932244, "dur": 37, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932289, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932291, "dur": 42, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932336, "dur": 48, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932385, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932387, "dur": 21, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932410, "dur": 66, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932478, "dur": 1, "ph": "X", "name": "ProcessMessages 1251", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932480, "dur": 28, "ph": "X", "name": "ReadAsync 1251", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932510, "dur": 32, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932544, "dur": 21, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932568, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932606, "dur": 31, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932639, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932676, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932677, "dur": 39, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932718, "dur": 2, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932721, "dur": 49, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932787, "dur": 1, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932789, "dur": 24, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932816, "dur": 25, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932844, "dur": 40, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932886, "dur": 42, "ph": "X", "name": "ReadAsync 1210", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932931, "dur": 30, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932964, "dur": 26, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254932992, "dur": 36, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933031, "dur": 78, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933111, "dur": 2, "ph": "X", "name": "ProcessMessages 1654", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933113, "dur": 38, "ph": "X", "name": "ReadAsync 1654", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933153, "dur": 1, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933155, "dur": 42, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933200, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933201, "dur": 24, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933227, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933252, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933273, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933311, "dur": 19, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933332, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933360, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933383, "dur": 28, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933414, "dur": 61, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933476, "dur": 1, "ph": "X", "name": "ProcessMessages 1289", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933477, "dur": 43, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933523, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933550, "dur": 30, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933583, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933605, "dur": 36, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933644, "dur": 22, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933668, "dur": 22, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933692, "dur": 30, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933725, "dur": 35, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933762, "dur": 31, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933795, "dur": 36, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933833, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933835, "dur": 42, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933879, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933880, "dur": 27, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933910, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933933, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933951, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933971, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254933993, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934011, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934050, "dur": 17, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934069, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934090, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934112, "dur": 21, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934135, "dur": 27, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934163, "dur": 37, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934203, "dur": 20, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934225, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934247, "dur": 15, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934264, "dur": 43, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934310, "dur": 20, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934332, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934352, "dur": 24, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934378, "dur": 27, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934407, "dur": 68, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934477, "dur": 23, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934502, "dur": 20, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934524, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934543, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934563, "dur": 28, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934593, "dur": 18, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934613, "dur": 27, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934642, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934661, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934681, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934704, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934723, "dur": 45, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934785, "dur": 22, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934809, "dur": 32, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934842, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934844, "dur": 40, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934887, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934908, "dur": 31, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934940, "dur": 26, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934969, "dur": 27, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254934998, "dur": 25, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254935025, "dur": 1242, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936269, "dur": 3, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936273, "dur": 36, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936311, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936313, "dur": 36, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936351, "dur": 1, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936352, "dur": 32, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936386, "dur": 1, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936388, "dur": 34, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936424, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936426, "dur": 26, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936455, "dur": 30, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936487, "dur": 35, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936523, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936525, "dur": 29, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936555, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936556, "dur": 22, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936581, "dur": 27, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936609, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936611, "dur": 33, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936645, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936647, "dur": 27, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936676, "dur": 30, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936708, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936710, "dur": 33, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936744, "dur": 1, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936746, "dur": 32, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936779, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936780, "dur": 39, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936821, "dur": 35, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936858, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936860, "dur": 34, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936896, "dur": 1, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936898, "dur": 26, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936926, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936928, "dur": 35, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254936965, "dur": 33, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937001, "dur": 48, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937052, "dur": 42, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937096, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937106, "dur": 33, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937141, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937143, "dur": 22, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937168, "dur": 29, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937199, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937235, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937237, "dur": 22, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937261, "dur": 7, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937268, "dur": 37, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937308, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937309, "dur": 37, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937349, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937351, "dur": 40, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937394, "dur": 35, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937431, "dur": 1, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937432, "dur": 38, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937472, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937473, "dur": 36, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937512, "dur": 1, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937514, "dur": 36, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937551, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254937553, "dur": 40, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938342, "dur": 37, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938380, "dur": 4, "ph": "X", "name": "ProcessMessages 8142", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938387, "dur": 31, "ph": "X", "name": "ReadAsync 8142", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938421, "dur": 68, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938492, "dur": 62, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938556, "dur": 54, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938612, "dur": 53, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938668, "dur": 51, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938721, "dur": 58, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938781, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938783, "dur": 60, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938843, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938845, "dur": 52, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938899, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938901, "dur": 51, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938954, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254938955, "dur": 62, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939019, "dur": 356, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939377, "dur": 3, "ph": "X", "name": "ProcessMessages 5373", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939381, "dur": 29, "ph": "X", "name": "ReadAsync 5373", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939412, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939439, "dur": 23, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939464, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939493, "dur": 23, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939519, "dur": 54, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939575, "dur": 32, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939623, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939624, "dur": 29, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939654, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939656, "dur": 58, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939717, "dur": 41, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939760, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939762, "dur": 31, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939795, "dur": 29, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939826, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939828, "dur": 30, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939859, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939860, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939894, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939913, "dur": 22, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254939938, "dur": 60, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940000, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940001, "dur": 41, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940044, "dur": 26, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940073, "dur": 87, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940162, "dur": 2, "ph": "X", "name": "ProcessMessages 2059", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940164, "dur": 27, "ph": "X", "name": "ReadAsync 2059", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940195, "dur": 44, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940241, "dur": 368, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940611, "dur": 3, "ph": "X", "name": "ProcessMessages 6753", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940616, "dur": 34, "ph": "X", "name": "ReadAsync 6753", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940653, "dur": 24, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940679, "dur": 28, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940709, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940711, "dur": 34, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940748, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940776, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940777, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940809, "dur": 38, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940849, "dur": 32, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940883, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940884, "dur": 31, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940918, "dur": 21, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940942, "dur": 41, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254940986, "dur": 32, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941019, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941040, "dur": 25, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941084, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941101, "dur": 69, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941172, "dur": 84, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941258, "dur": 1, "ph": "X", "name": "ProcessMessages 1571", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941275, "dur": 16, "ph": "X", "name": "ReadAsync 1571", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941293, "dur": 46, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941341, "dur": 32, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941375, "dur": 27, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941405, "dur": 24, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941431, "dur": 25, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941457, "dur": 139, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941598, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941599, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941691, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941839, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941841, "dur": 70, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254941914, "dur": 196, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942111, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942113, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942154, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942276, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942363, "dur": 265, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942631, "dur": 195, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942828, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942829, "dur": 118, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942950, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254942981, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943016, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943117, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943153, "dur": 299, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943454, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943455, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943492, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943537, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943570, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943651, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943708, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943743, "dur": 153, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943899, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943932, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254943967, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944002, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944004, "dur": 229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944234, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944236, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944273, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944313, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944357, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944444, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944446, "dur": 284, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944732, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944734, "dur": 49, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944785, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944788, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944831, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944898, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254944974, "dur": 105, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945083, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945119, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945169, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945170, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945210, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945251, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945252, "dur": 45, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945300, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945341, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945380, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945382, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945433, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945475, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945520, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945521, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945576, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945577, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945615, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945694, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945695, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945732, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945734, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254945765, "dur": 254, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946021, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946064, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946066, "dur": 36, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946104, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946105, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946139, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946175, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946211, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946248, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946251, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946291, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946293, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946332, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946333, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946391, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946393, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946466, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946501, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946550, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946594, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946636, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946669, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946745, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946746, "dur": 121, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946871, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946912, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254946961, "dur": 342, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254947305, "dur": 452, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254947758, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254947802, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254948129, "dur": 1364, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254949495, "dur": 43, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254949543, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254949544, "dur": 14181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254963733, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254963736, "dur": 415, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254964153, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254964304, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254964472, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254964475, "dur": 2881, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254967360, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254967362, "dur": 8049, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254975417, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254975420, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254975450, "dur": 483, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254975942, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254975947, "dur": 341, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254976293, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254976295, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254976330, "dur": 288, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254976621, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254976673, "dur": 794, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254977470, "dur": 251, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254977725, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254977727, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254977765, "dur": 210, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254977979, "dur": 173, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254978155, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254978255, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254978293, "dur": 398, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254978695, "dur": 20, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254978717, "dur": 250, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254978970, "dur": 72, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979045, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979304, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979428, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979552, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979586, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979746, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979851, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254979853, "dur": 277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254980133, "dur": 316, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254980452, "dur": 220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254980675, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254980877, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254981098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254981099, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254981271, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254981273, "dur": 305, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254981581, "dur": 299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254981883, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982027, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982067, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982251, "dur": 236, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982491, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982683, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982884, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254982974, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254983081, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254983083, "dur": 121, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254983207, "dur": 215, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254983424, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254983569, "dur": 469, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254984041, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254984291, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254984529, "dur": 264, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254984795, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254984916, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254984957, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985053, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985269, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985270, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985368, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985391, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985477, "dur": 178, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254985657, "dur": 2325, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254987985, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254987988, "dur": 280, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254988272, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254988402, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254988488, "dur": 258, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254988748, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254988791, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254988862, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254989062, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254989202, "dur": 428, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254989633, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746254989636, "dur": 102237, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255091882, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255091885, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255091935, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255091997, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255092061, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255092063, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255092127, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255092172, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255092232, "dur": 16, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255092250, "dur": 5246, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255097501, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255097504, "dur": 637, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255098152, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255098155, "dur": 547, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255098705, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255098962, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255099229, "dur": 1140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255100371, "dur": 591, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255100966, "dur": 407, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255101385, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255101388, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255101443, "dur": 1026, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255102472, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255102557, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255102801, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255102890, "dur": 827, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255103719, "dur": 438, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255104161, "dur": 762, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255104926, "dur": 1180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255106110, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255106394, "dur": 1206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255107604, "dur": 369, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255107976, "dur": 603, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255108581, "dur": 963, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255109547, "dur": 485, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255110036, "dur": 885, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255110923, "dur": 447, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255111373, "dur": 585, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255111960, "dur": 720, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255112683, "dur": 943, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255113629, "dur": 643, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255114275, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255114461, "dur": 12, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255114474, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255114713, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255114764, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255114917, "dur": 114, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115034, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115127, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115160, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115162, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115223, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115225, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115271, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115313, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115360, "dur": 16, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115377, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115428, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115538, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115581, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115691, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115738, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115771, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115774, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115842, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255115966, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116049, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116101, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116212, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116246, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116294, "dur": 74, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116370, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116414, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116452, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116495, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116539, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116578, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116616, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116696, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116739, "dur": 10, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116750, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116788, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116910, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116946, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255116991, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117029, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117071, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117110, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117171, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117228, "dur": 43, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117273, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117361, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117400, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117445, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117491, "dur": 45, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117538, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117631, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117673, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117776, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117832, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117907, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255117998, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255118024, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255118166, "dur": 422, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255118591, "dur": 319, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255118912, "dur": 417, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255119332, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746255119423, "dur": 3942257, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259061712, "dur": 64, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259061780, "dur": 15346, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259077139, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259077143, "dur": 1944, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259079093, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259079097, "dur": 205661, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259284767, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259284769, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259285098, "dur": 31, "ph": "X", "name": "ReadAsync 5586", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259285132, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259285196, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259285199, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259285247, "dur": 22, "ph": "X", "name": "ProcessMessages 4659", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259285270, "dur": 3082, "ph": "X", "name": "ReadAsync 4659", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259288356, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259288359, "dur": 288, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259288650, "dur": 26, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259288676, "dur": 42435, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259331119, "dur": 23, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259331144, "dur": 2646, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259333792, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259333794, "dur": 96294, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430096, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430098, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430156, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430208, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430257, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430282, "dur": 21, "ph": "X", "name": "ProcessMessages 6646", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259430304, "dur": 2953, "ph": "X", "name": "ReadAsync 6646", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259433259, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259433261, "dur": 442, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259433720, "dur": 17, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259433738, "dur": 588, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752746259434330, "dur": 2083, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259451118, "dur": 1248, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 17179869184, "ts": 1752746254793209, "dur": 223, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 17179869184, "ts": 1752746254793432, "dur": 10846, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 17179869184, "ts": 1752746254804280, "dur": 78, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259452368, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752746253590318, "dur": 29621, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752746253619945, "dur": 93828, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752746253713782, "dur": 141703, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259452380, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253587925, "dur": 36313, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253624242, "dur": 245216, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253626134, "dur": 18304, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253644448, "dur": 1809, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253646262, "dur": 12313, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253658595, "dur": 735, "ph": "X", "name": "ProcessMessages 5348", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659336, "dur": 204, "ph": "X", "name": "ReadAsync 5348", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659542, "dur": 3, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659546, "dur": 95, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659643, "dur": 69, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659716, "dur": 40, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659760, "dur": 79, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253659842, "dur": 352, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253660196, "dur": 4, "ph": "X", "name": "ProcessMessages 3868", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253660202, "dur": 4634, "ph": "X", "name": "ReadAsync 3868", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253664881, "dur": 1165, "ph": "X", "name": "ProcessMessages 8179", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253666054, "dur": 118, "ph": "X", "name": "ReadAsync 8179", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253666177, "dur": 5, "ph": "X", "name": "ProcessMessages 8114", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253666183, "dur": 89, "ph": "X", "name": "ReadAsync 8114", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253666282, "dur": 4, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253666288, "dur": 12076, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253678375, "dur": 16, "ph": "X", "name": "ProcessMessages 8187", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253678399, "dur": 124, "ph": "X", "name": "ReadAsync 8187", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253678527, "dur": 2, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253678531, "dur": 607, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679142, "dur": 10, "ph": "X", "name": "ProcessMessages 4849", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679154, "dur": 115, "ph": "X", "name": "ReadAsync 4849", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679272, "dur": 5, "ph": "X", "name": "ProcessMessages 2464", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679279, "dur": 499, "ph": "X", "name": "ReadAsync 2464", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679784, "dur": 4, "ph": "X", "name": "ProcessMessages 3388", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679789, "dur": 36, "ph": "X", "name": "ReadAsync 3388", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679829, "dur": 28, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253679859, "dur": 4808, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253706284, "dur": 9, "ph": "X", "name": "ProcessMessages 8120", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253706295, "dur": 8417, "ph": "X", "name": "ReadAsync 8120", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253714781, "dur": 21, "ph": "X", "name": "ProcessMessages 8189", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253714804, "dur": 237, "ph": "X", "name": "ReadAsync 8189", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253715043, "dur": 17, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253715080, "dur": 88, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253715176, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253715178, "dur": 11048, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726232, "dur": 6, "ph": "X", "name": "ProcessMessages 8157", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726240, "dur": 83, "ph": "X", "name": "ReadAsync 8157", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726324, "dur": 4, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726329, "dur": 255, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726589, "dur": 3, "ph": "X", "name": "ProcessMessages 2814", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726593, "dur": 56, "ph": "X", "name": "ReadAsync 2814", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253726652, "dur": 1353, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253728014, "dur": 5, "ph": "X", "name": "ProcessMessages 8127", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253728021, "dur": 613, "ph": "X", "name": "ReadAsync 8127", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253728637, "dur": 3, "ph": "X", "name": "ProcessMessages 4260", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253728641, "dur": 388, "ph": "X", "name": "ReadAsync 4260", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253729033, "dur": 3, "ph": "X", "name": "ProcessMessages 2138", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253729038, "dur": 3248, "ph": "X", "name": "ReadAsync 2138", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253732291, "dur": 7, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253732298, "dur": 36613, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253768920, "dur": 7, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253768928, "dur": 3502, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253772442, "dur": 16, "ph": "X", "name": "ProcessMessages 6369", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253787884, "dur": 479, "ph": "X", "name": "ReadAsync 6369", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253788366, "dur": 6, "ph": "X", "name": "ProcessMessages 8142", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253788373, "dur": 137, "ph": "X", "name": "ReadAsync 8142", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253788520, "dur": 4, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253788526, "dur": 291, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253788828, "dur": 2, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253788832, "dur": 756, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253789593, "dur": 2, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253789597, "dur": 411, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253790010, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253790012, "dur": 373, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253790388, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253800426, "dur": 6, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253800438, "dur": 169, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253800612, "dur": 7, "ph": "X", "name": "ProcessMessages 7438", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253800620, "dur": 1115, "ph": "X", "name": "ReadAsync 7438", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253801739, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253801741, "dur": 319, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253802074, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253802076, "dur": 31335, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253833418, "dur": 8, "ph": "X", "name": "ProcessMessages 8160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253833428, "dur": 57, "ph": "X", "name": "ReadAsync 8160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253833489, "dur": 462, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253833964, "dur": 2, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253833967, "dur": 649, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253834619, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253834635, "dur": 225, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253834863, "dur": 1760, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253836628, "dur": 4, "ph": "X", "name": "ProcessMessages 1605", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253836633, "dur": 418, "ph": "X", "name": "ReadAsync 1605", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253837054, "dur": 1616, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253838674, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253838677, "dur": 2248, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253840930, "dur": 2, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253840933, "dur": 27, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253840962, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253840964, "dur": 37, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253841003, "dur": 51, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253841069, "dur": 230, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253841303, "dur": 193, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253841499, "dur": 599, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253842100, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253842102, "dur": 138, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253842243, "dur": 4631, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253846883, "dur": 5, "ph": "X", "name": "ProcessMessages 4454", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253846889, "dur": 1528, "ph": "X", "name": "ReadAsync 4454", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253848426, "dur": 8, "ph": "X", "name": "ProcessMessages 2735", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253848436, "dur": 1190, "ph": "X", "name": "ReadAsync 2735", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253849630, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253849634, "dur": 303, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253849940, "dur": 114, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253850056, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253850058, "dur": 204, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253850265, "dur": 1009, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253851277, "dur": 2, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253851280, "dur": 3408, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253854693, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253854695, "dur": 119, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253854817, "dur": 488, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253855308, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253855312, "dur": 99, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253855413, "dur": 999, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253856416, "dur": 6, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253856424, "dur": 540, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253856966, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253856968, "dur": 5099, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253862072, "dur": 7, "ph": "X", "name": "ProcessMessages 5602", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253862080, "dur": 217, "ph": "X", "name": "ReadAsync 5602", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253862300, "dur": 253, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253862556, "dur": 676, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253863234, "dur": 478, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253863727, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253863732, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253863818, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253863819, "dur": 177, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253863998, "dur": 12, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253864014, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253864086, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253864170, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253864591, "dur": 178, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752746253864771, "dur": 4648, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259452390, "dur": 447, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 8589934592, "ts": 1752746253581778, "dur": 273732, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752746253855512, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752746253855518, "dur": 2706, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259452838, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746253482270, "dur": 391407, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746253501311, "dur": 63097, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746253873788, "dur": 904164, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746254778159, "dur": 4658605, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746254779470, "dur": 13541, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746259437107, "dur": 5902, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746259441105, "dur": 345, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752746259443018, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259452846, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752746254804186, "dur": 125916, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746254930113, "dur": 144, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746254930324, "dur": 103, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746254932095, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746254935405, "dur": 937, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752746254938132, "dur": 323, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752746254930434, "dur": 11079, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746254941519, "dur": 4492249, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746259433943, "dur": 62, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746259434050, "dur": 562, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752746254930380, "dur": 11168, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254941611, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254941701, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254941802, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254941865, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254942010, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254942139, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254942292, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254942405, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254942604, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254942716, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254942795, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254942912, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254942994, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254943132, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254943263, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254943315, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254943444, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254943514, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254943679, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254943780, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254943930, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944044, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254944152, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944245, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944344, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944450, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944549, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254944659, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944741, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944847, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254944956, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945065, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945168, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945272, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945362, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945467, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945588, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945675, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945783, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945879, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254945964, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946052, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946164, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946267, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946387, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946498, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946599, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946693, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946798, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254946920, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254948253, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254949598, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254950727, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254951572, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254952565, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254953731, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254954895, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254956074, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254957119, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254958367, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254959351, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254960826, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254961854, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254963018, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254964360, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254965884, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254966835, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254967701, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254968772, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254969951, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254971038, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254971938, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254972779, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254973842, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254974705, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254974846, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254974914, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254974974, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254975409, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254975908, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254976533, "dur": 4417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752746254980950, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254981196, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254981418, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254981565, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752746254983220, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254983359, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254983541, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254983600, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752746254984979, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254985161, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746254985224, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254985389, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752746254987270, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746254987369, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752746254987764, "dur": 106473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255094237, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752746255097298, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255097397, "dur": 5490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752746255102887, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255102981, "dur": 3444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752746255106425, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255106508, "dur": 4451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752746255110960, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255111020, "dur": 3648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752746255114669, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255115878, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255117225, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255117388, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752746255117445, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255117534, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255117861, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255118074, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746255119378, "dur": 4314380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254930370, "dur": 11157, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254941537, "dur": 2225, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254943762, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254943914, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944007, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254944128, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944227, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944325, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944437, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944531, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254944703, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944827, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254944923, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945037, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945127, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945229, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945323, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945426, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945535, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945635, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945755, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945858, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254945940, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946029, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946133, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946241, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946361, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946462, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946573, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946678, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946782, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254946908, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254948263, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254949617, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254950741, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254951589, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254952585, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254953768, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254954958, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254956119, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254957156, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254958401, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254959404, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254960880, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254961900, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254963062, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254964471, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254966011, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254966893, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254967803, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254968905, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254970090, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254971158, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254972047, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254972887, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254973675, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254974584, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254974751, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254974853, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254974916, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254974972, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254975434, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254976209, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254976799, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752746254977392, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254977690, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752746254979587, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254979772, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254980111, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254980192, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254980392, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254980530, "dur": 2181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752746254982711, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254982828, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752746254984019, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254984119, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254984327, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752746254985674, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254985826, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254985997, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752746254986899, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254987000, "dur": 2110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746254989111, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746254989240, "dur": 104162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255093411, "dur": 2172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255095583, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255095660, "dur": 3596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255099256, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255099339, "dur": 3248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255102587, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255102677, "dur": 3268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255105946, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255106030, "dur": 4030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255110060, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255110148, "dur": 3507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255113656, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746255113740, "dur": 5177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752746255118942, "dur": 4314847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254930380, "dur": 11159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254941647, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254941802, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254941868, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254942006, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254942125, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254942291, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254942383, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254942589, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254942701, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254942761, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254942892, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254942975, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254943130, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254943270, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254943341, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254943443, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254943520, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254943713, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254943795, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254943933, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944050, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254944211, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944290, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944387, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944477, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944561, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944684, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944769, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944876, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254944989, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945083, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945185, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945282, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945372, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945484, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945601, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945705, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945806, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945899, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254945991, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946081, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946196, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946296, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946414, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946524, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946631, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946732, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946839, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254946960, "dur": 2538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254949499, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254950668, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254951499, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254952487, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254953688, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254954841, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254956023, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254957081, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254958337, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254959330, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254960821, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254961848, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254963006, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254964354, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752746254964405, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254964479, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254965996, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254966880, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254967787, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254968856, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254970048, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254971123, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254972007, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254972846, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254973883, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254974739, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254974850, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254974973, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254975449, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254975909, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254976609, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254976691, "dur": 2380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254979071, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254979236, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254980031, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254980907, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254981127, "dur": 1790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254982917, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254983081, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254985056, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254985438, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_BEADB10929D2C8CB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254985527, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254985598, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254985834, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254985981, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254986799, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254986866, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254987948, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254988013, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254988434, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254988497, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254988834, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254988889, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746254989106, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746254989217, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746259060948, "dur": 51, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746254989657, "dur": 4071384, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746259066431, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752746259065177, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746259067105, "dur": 152, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746259067286, "dur": 263819, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752746259331867, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752746259331848, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752746259333600, "dur": 188, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746259430045, "dur": 326, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746259333800, "dur": 96580, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752746259433240, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752746259433233, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752746259433303, "dur": 437, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752746254930381, "dur": 11176, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254941607, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254941689, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254941787, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254941887, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254942036, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254942163, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254942291, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254942380, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254942543, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254942598, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254942716, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254942790, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254942911, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254942984, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254943108, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254943180, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254943334, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254943445, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254943535, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254943693, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254943760, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254943886, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254944022, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254944189, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254944270, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254944399, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254944493, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254944615, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254944678, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254944764, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254944867, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945008, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945107, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945203, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945300, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945384, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945495, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945595, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945692, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945790, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945888, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254945981, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946065, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946181, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946286, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946395, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946512, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946616, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946719, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946832, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254946943, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254948306, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254949692, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254950793, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254951488, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254952467, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254953663, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254954802, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254956001, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254957045, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254958301, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254959287, "dur": 1477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254960764, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254961812, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254962991, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254964392, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254964469, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254965989, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254966875, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254967781, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254968868, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254970059, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254971140, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254972033, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254972881, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254973940, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254974790, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254974957, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254975415, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254976082, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254976662, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752746254978699, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254979037, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254979369, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254979818, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254979902, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254980792, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752746254981856, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254981976, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254982386, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254982921, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254982997, "dur": 3899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752746254986896, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254986994, "dur": 1446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746254988440, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746254988501, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752746254988782, "dur": 106460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746255095243, "dur": 3702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752746255098946, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746255099034, "dur": 3427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752746255102461, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746255102544, "dur": 4121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752746255106667, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746255106794, "dur": 4786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752746255111581, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746255111652, "dur": 6464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752746255118188, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746255119481, "dur": 4314306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254930387, "dur": 11181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254941637, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254941849, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254942012, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254942147, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254942296, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254942398, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254942544, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254942610, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254942721, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254942809, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254942921, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254943006, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254943152, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254943281, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254943355, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254943464, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254943550, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254943626, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254943717, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254943791, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254943951, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254944114, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944189, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944279, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944364, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944483, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944562, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944703, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944793, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944875, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254944997, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945102, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945200, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945292, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945410, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945521, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945623, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945736, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945843, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254945928, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946020, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946109, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946222, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946323, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946440, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946547, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946648, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946747, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946868, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254946973, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254948313, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254949658, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254950772, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254951605, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254952609, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254953788, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254954975, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254956133, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254957178, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254958450, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254959451, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254960904, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254961918, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254963078, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254964499, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254966031, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254966896, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254967808, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254968909, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254970098, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254971176, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254972060, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254972899, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254973327, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254974008, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254974878, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254974964, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254975422, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254975917, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254976245, "dur": 1244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752746254977489, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254977788, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_82D6D4EB628A8BAE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254978030, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254978111, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254978515, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752746254979886, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254980229, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254980434, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254980591, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752746254982869, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254983023, "dur": 1527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752746254984550, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254984679, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254984897, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_9D36C2C8B69750E1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254984996, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254985104, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_3BDB24518B22122E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254985190, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_43307AC03DF2DC88.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254985392, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254985582, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746254986535, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752746254987103, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746254987938, "dur": 105459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746255093410, "dur": 7970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752746255101381, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746255101524, "dur": 6441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752746255107965, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746255108049, "dur": 4453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752746255112503, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746255112753, "dur": 5830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752746255118612, "dur": 4169671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746259288292, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752746259288284, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752746259288371, "dur": 358, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752746259288730, "dur": 145046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254930401, "dur": 11178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254941630, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254941874, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254942005, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254942123, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254942292, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254942394, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254942591, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254942707, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254942775, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254942910, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254942979, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254943111, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254943192, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254943309, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254943377, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254943525, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254943615, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254943737, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254943812, "dur": 3878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254947733, "dur": 16380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752746254964113, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254964327, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254964392, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254964478, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254967343, "dur": 8019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752746254975416, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254975482, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752746254975722, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254975906, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254976297, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254976392, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752746254977333, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254977879, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746254978412, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752746254979403, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254979568, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1752746254981929, "dur": 1184, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255091775, "dur": 474, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746254983151, "dur": 109108, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1752746255093392, "dur": 4135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746255097527, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255097608, "dur": 3374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746255100982, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255101054, "dur": 3135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746255104189, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255104272, "dur": 4339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746255108611, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255108675, "dur": 8391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746255117066, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255117331, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255117635, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255117726, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255117819, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255117877, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746255118198, "dur": 3949301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746259067536, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752746259067502, "dur": 6423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746259078493, "dur": 560, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746259284743, "dur": 553, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746259079077, "dur": 206231, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752746259288263, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752746259288258, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752746259288432, "dur": 280, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752746259288714, "dur": 145077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254930404, "dur": 11244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254941657, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254941848, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254942029, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254942156, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254942314, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254942430, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254942548, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254942635, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254942742, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254942820, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254942936, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254943025, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254943141, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254943266, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254943327, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254943465, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254943561, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254943665, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254943748, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254943878, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254943977, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254944119, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944201, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944259, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944387, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944485, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944572, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944637, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944717, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944818, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254944909, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945038, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945143, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945247, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945337, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945440, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945556, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945654, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945763, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945862, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254945946, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946036, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946137, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946230, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946339, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946458, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946558, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946665, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946763, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254946892, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254947005, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254948302, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254949647, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254950751, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254951598, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254952604, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254953797, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254954979, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254956145, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254957194, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254958461, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254959471, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254960929, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254961952, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254963106, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254964568, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254966059, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254966903, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254967823, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254968950, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254970111, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254971180, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254972066, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254972911, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254973349, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254974341, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254975160, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254975427, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254975935, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254976298, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254976371, "dur": 1508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752746254977880, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254978381, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746254978622, "dur": 4424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752746254983047, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254983182, "dur": 2861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752746254986043, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746254986173, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752746254987078, "dur": 106312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746255093395, "dur": 6822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752746255100217, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746255100442, "dur": 3270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752746255103713, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746255103794, "dur": 7574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752746255111369, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746255111427, "dur": 6512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752746255117940, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746255118044, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746255118948, "dur": 4314825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254930418, "dur": 11211, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254941632, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254941836, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254942012, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254942184, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254942328, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254942453, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254942574, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254942653, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254942751, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254942848, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254942969, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254943056, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254943165, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254943249, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254943402, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254943538, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254943614, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254943721, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254943799, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254943956, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254944171, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254944256, "dur": 4140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254948397, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254948454, "dur": 14853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752746254963308, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254963674, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254963804, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254963882, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254965454, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254966565, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254967360, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254968396, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254969596, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254970771, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254971671, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254972527, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254973530, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254974486, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/TuanjieReferenceAssemblies/tuanjie-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752746254974486, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254975117, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254975436, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254975907, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254976331, "dur": 5655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752746254981987, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254982105, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_F3F1807631F79CD9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254982212, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254982295, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746254982563, "dur": 4016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752746254986579, "dur": 1352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746254988002, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752746254988291, "dur": 106288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746255094582, "dur": 4147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752746255098730, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746255098817, "dur": 7282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752746255106099, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746255106178, "dur": 3864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752746255110043, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746255110219, "dur": 4054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752746255114274, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746255114365, "dur": 5083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752746255119475, "dur": 4314308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254930420, "dur": 11237, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254941661, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254941825, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254941921, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254942074, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254942207, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254942367, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254942490, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254942617, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254942729, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254942800, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254942915, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254942998, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254943116, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254943205, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254943319, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254943445, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254943510, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254943695, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254943789, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254943962, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254944115, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944206, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944294, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944403, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944498, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254944617, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944691, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944839, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254944938, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945057, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945158, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945255, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945351, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945457, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945571, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945670, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945773, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945874, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254945952, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946040, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946150, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946256, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946370, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946483, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946596, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946706, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946806, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254946931, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254948293, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254949669, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254950788, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254951614, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254952658, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254953855, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254955029, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254956180, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254957224, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254958483, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254959503, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254960951, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254961983, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254963119, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254964625, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254966118, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254966946, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254967855, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254968960, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254970143, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254971206, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254972082, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254972937, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254973299, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254973746, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254974632, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254974963, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254975424, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254975933, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254976298, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254976366, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752746254979265, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254979627, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_39AF4F01BD1B7546.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254979707, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254979809, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254980743, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752746254981834, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254982172, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254982424, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752746254984294, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254984475, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254984761, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752746254986303, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254986516, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752746254986680, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746254987273, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746254987376, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752746254987826, "dur": 105590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746255093417, "dur": 4400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752746255097818, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746255098041, "dur": 4758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752746255102799, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746255102869, "dur": 4761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752746255107630, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746255107709, "dur": 4266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752746255112028, "dur": 5640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752746255117668, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746255117911, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746255118634, "dur": 4314624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746259433307, "dur": 389, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752746259433699, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254930439, "dur": 11226, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254941668, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254941823, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254941943, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254942131, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254942263, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254942405, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254942512, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254942655, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254942744, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254942867, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254942966, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254943089, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254943162, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254943337, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254943451, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254943545, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254943657, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254943738, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254943822, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254943969, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254944122, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944217, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944306, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944416, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944519, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254944630, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944705, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944780, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254944884, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945016, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945110, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945216, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945307, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945408, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945510, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945610, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945717, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945821, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254945916, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254946007, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254946093, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254946212, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254946314, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254946419, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254946542, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254947620, "dur": 523, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752746254948143, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254949486, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254950656, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254951477, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254952458, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254953641, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254954809, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254956015, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254957068, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254958322, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254959307, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254960782, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254961818, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254962988, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254964316, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254965807, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254966771, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254967626, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254968707, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254969853, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254970964, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254971872, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254972695, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254973732, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254974610, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254974827, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254974970, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254975430, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254975945, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254976233, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254976751, "dur": 1165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752746254977916, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254978206, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254978955, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752746254981301, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254981443, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254981609, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254981664, "dur": 1791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752746254983455, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254983636, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746254983826, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752746254984827, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254984985, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254985040, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752746254986291, "dur": 1653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746254987955, "dur": 105439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746255093395, "dur": 4772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752746255098167, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746255098250, "dur": 3125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752746255101375, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746255101454, "dur": 3461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752746255104915, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746255104991, "dur": 4539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752746255109530, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746255109621, "dur": 4831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752746255114452, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746255114529, "dur": 4817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752746255119371, "dur": 4314400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746259435721, "dur": 451, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1752746254227125, "dur": 534407, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752746254227881, "dur": 118000, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752746254684039, "dur": 3344, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752746254687385, "dur": 74132, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752746254690222, "dur": 45039, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752746254766852, "dur": 1007, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752746254766465, "dur": 1581, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752746253608208, "dur": 8318, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253616607, "dur": 29034, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253645743, "dur": 154, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253646199, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253646707, "dur": 12877, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253659666, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253660231, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253660778, "dur": 2053, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253663168, "dur": 3085, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253668198, "dur": 10228, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752746253679441, "dur": 419, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752746253680533, "dur": 2719, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253683858, "dur": 30917, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752746253715155, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752746253716358, "dur": 9023, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752746253725996, "dur": 397, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752746253727610, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752746253727894, "dur": 245, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752746253729686, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752746253730041, "dur": 1985, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752746253734608, "dur": 34359, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752746253769609, "dur": 2082, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752746253773610, "dur": 14839, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752746253801608, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752746253811918, "dur": 21585, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752746253860866, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752746253645904, "dur": 216364, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253862275, "dur": 1575, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253863851, "dur": 301, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253864283, "dur": 1031, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752746253645867, "dur": 216442, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746253862330, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752746253862526, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746253863583, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752746253863728, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752746253863999, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746253645873, "dur": 216476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746253862355, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752746253863571, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752746253863647, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752746253645867, "dur": 216417, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752746253862289, "dur": 1537, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746253645869, "dur": 216463, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746253862338, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752746253862540, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752746253863586, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752746253863774, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746253645870, "dur": 216511, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746253862387, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746253863588, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752746253863812, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752746253864007, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746253645875, "dur": 216563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752746253862446, "dur": 1125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746253863592, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752746253863945, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746253645876, "dur": 216576, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746253862455, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746253863587, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752746253863754, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752746253864003, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746253645877, "dur": 216594, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752746253862491, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752746253863591, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746253645899, "dur": 216595, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752746253862497, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746253863590, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752746253863938, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746253645901, "dur": 216628, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752746253862530, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752746253863592, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752746253867476, "dur": 1505, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 1296, "ts": 1752746259454767, "dur": 4064, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 39945, "tid": 1296, "ts": 1752746259461481, "dur": 32, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 39945, "tid": 1296, "ts": 1752746259461884, "dur": 16, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 1296, "ts": 1752746259459476, "dur": 2000, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259461554, "dur": 329, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259461952, "dur": 331, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 1296, "ts": 1752746259448429, "dur": 15385, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}