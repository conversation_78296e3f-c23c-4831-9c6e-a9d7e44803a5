{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 1408, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 1408, "ts": 1752759917685784, "dur": 27, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 1408, "ts": 1752759917685867, "dur": 7, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752759916838849, "dur": 2745, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752759916841596, "dur": 25073, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752759916866670, "dur": 32375, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 1408, "ts": 1752759917685876, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 85899345920, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916838783, "dur": 12476, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916851261, "dur": 833582, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916851303, "dur": 46, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916851352, "dur": 332, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916851696, "dur": 62, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916851759, "dur": 14135, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916865909, "dur": 2, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916865923, "dur": 360, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866284, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866288, "dur": 27, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866318, "dur": 51, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866374, "dur": 55, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866435, "dur": 2, "ph": "X", "name": "ProcessMessages 1559", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866438, "dur": 46, "ph": "X", "name": "ReadAsync 1559", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866485, "dur": 1, "ph": "X", "name": "ProcessMessages 1650", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866489, "dur": 45, "ph": "X", "name": "ReadAsync 1650", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866541, "dur": 1, "ph": "X", "name": "ProcessMessages 1478", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866542, "dur": 18, "ph": "X", "name": "ReadAsync 1478", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866563, "dur": 34, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866600, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866602, "dur": 35, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866640, "dur": 49, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866693, "dur": 1, "ph": "X", "name": "ProcessMessages 1490", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866695, "dur": 54, "ph": "X", "name": "ReadAsync 1490", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866750, "dur": 1, "ph": "X", "name": "ProcessMessages 1735", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866752, "dur": 32, "ph": "X", "name": "ReadAsync 1735", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866787, "dur": 33, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866824, "dur": 1, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866825, "dur": 32, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866864, "dur": 28, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866893, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916866895, "dur": 179, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867077, "dur": 32, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867112, "dur": 44, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867158, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867160, "dur": 48, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867209, "dur": 1, "ph": "X", "name": "ProcessMessages 1728", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867211, "dur": 21, "ph": "X", "name": "ReadAsync 1728", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867236, "dur": 23, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867262, "dur": 18, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867283, "dur": 37, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867321, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867322, "dur": 657, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916867983, "dur": 60, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868043, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868048, "dur": 37, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868087, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868089, "dur": 37, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868128, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868130, "dur": 45, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868177, "dur": 331, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868510, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868512, "dur": 42, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868556, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868572, "dur": 40, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868613, "dur": 1, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868615, "dur": 46, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868662, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868678, "dur": 39, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868719, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868721, "dur": 33, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868755, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868757, "dur": 35, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868794, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868796, "dur": 36, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868833, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916868835, "dur": 52, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870114, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870116, "dur": 57, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870175, "dur": 4, "ph": "X", "name": "ProcessMessages 8165", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870179, "dur": 25, "ph": "X", "name": "ReadAsync 8165", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870206, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870208, "dur": 33, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870245, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870246, "dur": 41, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870288, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870291, "dur": 37, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870330, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870332, "dur": 356, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870689, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870691, "dur": 34, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870726, "dur": 3, "ph": "X", "name": "ProcessMessages 4717", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870730, "dur": 23, "ph": "X", "name": "ReadAsync 4717", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870754, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870783, "dur": 42, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916870827, "dur": 327, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871157, "dur": 39, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871213, "dur": 2, "ph": "X", "name": "ProcessMessages 5376", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871217, "dur": 30, "ph": "X", "name": "ReadAsync 5376", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871251, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871253, "dur": 31, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871305, "dur": 39, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871347, "dur": 47, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871397, "dur": 1, "ph": "X", "name": "ProcessMessages 1328", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871398, "dur": 30, "ph": "X", "name": "ReadAsync 1328", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871430, "dur": 382, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871813, "dur": 2, "ph": "X", "name": "ProcessMessages 4785", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871816, "dur": 28, "ph": "X", "name": "ReadAsync 4785", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871846, "dur": 20, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871868, "dur": 31, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916871914, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916872258, "dur": 45, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916872305, "dur": 2, "ph": "X", "name": "ProcessMessages 5891", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916872314, "dur": 20, "ph": "X", "name": "ReadAsync 5891", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916872336, "dur": 31, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916872369, "dur": 29, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916872400, "dur": 640, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873041, "dur": 3, "ph": "X", "name": "ProcessMessages 8188", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873045, "dur": 45, "ph": "X", "name": "ReadAsync 8188", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873092, "dur": 26, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873120, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873121, "dur": 31, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873167, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873169, "dur": 336, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873507, "dur": 3, "ph": "X", "name": "ProcessMessages 8140", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873512, "dur": 23, "ph": "X", "name": "ReadAsync 8140", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873537, "dur": 25, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873564, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873566, "dur": 75, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873642, "dur": 1, "ph": "X", "name": "ProcessMessages 1858", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873645, "dur": 25, "ph": "X", "name": "ReadAsync 1858", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873677, "dur": 34, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873713, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873715, "dur": 31, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873748, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873750, "dur": 41, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873794, "dur": 22, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873838, "dur": 24, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873865, "dur": 25, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873892, "dur": 24, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873918, "dur": 33, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873952, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873954, "dur": 29, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873984, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916873986, "dur": 29, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874017, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874018, "dur": 43, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874063, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874064, "dur": 40, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874106, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874107, "dur": 34, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874143, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874145, "dur": 34, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874181, "dur": 351, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874534, "dur": 2, "ph": "X", "name": "ProcessMessages 2826", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874787, "dur": 22, "ph": "X", "name": "ReadAsync 2826", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874811, "dur": 3, "ph": "X", "name": "ProcessMessages 6138", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874825, "dur": 120, "ph": "X", "name": "ReadAsync 6138", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874948, "dur": 25, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916874975, "dur": 476, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916875453, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916875455, "dur": 550, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916876007, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916876008, "dur": 601, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916876614, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916876615, "dur": 527, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916877149, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916877150, "dur": 413, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916877567, "dur": 352, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916877921, "dur": 27, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916877951, "dur": 462, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916878415, "dur": 128, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916878545, "dur": 421, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916878969, "dur": 421, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916879392, "dur": 423, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916879817, "dur": 27, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916879847, "dur": 422, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916880271, "dur": 156, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916880430, "dur": 16, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916880459, "dur": 509, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916880979, "dur": 2, "ph": "X", "name": "ProcessMessages 1730", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916880982, "dur": 33, "ph": "X", "name": "ReadAsync 1730", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916881018, "dur": 472, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916881494, "dur": 179, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916881676, "dur": 415, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916882092, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916882095, "dur": 433, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916882529, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916882531, "dur": 471, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883007, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883009, "dur": 156, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883168, "dur": 166, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883343, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883345, "dur": 523, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883870, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916883872, "dur": 761, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916884638, "dur": 602, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916885243, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916885245, "dur": 958, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916886205, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916886216, "dur": 420, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916887280, "dur": 1, "ph": "X", "name": "ProcessMessages 2014", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916887282, "dur": 311, "ph": "X", "name": "ReadAsync 2014", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916887616, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916887618, "dur": 388, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888009, "dur": 184, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888196, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888197, "dur": 33, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888233, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888234, "dur": 247, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888483, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888485, "dur": 31, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888517, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888519, "dur": 95, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888615, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888617, "dur": 191, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888811, "dur": 31, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888843, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916888847, "dur": 204, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889053, "dur": 175, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889230, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889232, "dur": 67, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889310, "dur": 23, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889334, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889336, "dur": 38, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889375, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889377, "dur": 27, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889407, "dur": 320, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889729, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916889731, "dur": 404, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916890137, "dur": 390, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916890529, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916890531, "dur": 33, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916890571, "dur": 402, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916890976, "dur": 581, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916891561, "dur": 1, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916891737, "dur": 33, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916891773, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916891775, "dur": 524, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892315, "dur": 1, "ph": "X", "name": "ProcessMessages 1417", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892317, "dur": 24, "ph": "X", "name": "ReadAsync 1417", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892348, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892358, "dur": 38, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892400, "dur": 267, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892677, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892679, "dur": 206, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892912, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916892914, "dur": 197, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893124, "dur": 54, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893181, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893192, "dur": 236, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893433, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893434, "dur": 295, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893731, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916893733, "dur": 93, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916894436, "dur": 53, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916894491, "dur": 1, "ph": "X", "name": "ProcessMessages 1572", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916894495, "dur": 450, "ph": "X", "name": "ReadAsync 1572", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916894971, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916894972, "dur": 311, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916895294, "dur": 260, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916895555, "dur": 1, "ph": "X", "name": "ProcessMessages 1825", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916895638, "dur": 290, "ph": "X", "name": "ReadAsync 1825", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916895950, "dur": 1, "ph": "X", "name": "ProcessMessages 2353", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916895952, "dur": 116, "ph": "X", "name": "ReadAsync 2353", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916896074, "dur": 376, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916896453, "dur": 231, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916896687, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916896724, "dur": 365, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897091, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897093, "dur": 190, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897284, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897287, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897323, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897327, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897357, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897359, "dur": 168, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897529, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897572, "dur": 52, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897626, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897737, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897739, "dur": 82, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897823, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916897941, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898030, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898078, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898161, "dur": 42, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898207, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898243, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898319, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898405, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898509, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898554, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898654, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898689, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898753, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898818, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916898847, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899043, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899508, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899510, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899569, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899571, "dur": 83, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899658, "dur": 97, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899757, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899759, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899912, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899914, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899968, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916899998, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916900029, "dur": 591, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916900622, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916900623, "dur": 285, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916900911, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916900998, "dur": 469, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901481, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901483, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901596, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901597, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901665, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901714, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901784, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901883, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916901990, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902063, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902118, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902154, "dur": 159, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902315, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902327, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902419, "dur": 133, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916902558, "dur": 1107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916903668, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916903672, "dur": 2754, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916906430, "dur": 3, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916906434, "dur": 551, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907054, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907056, "dur": 28, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907087, "dur": 201, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907290, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907292, "dur": 65, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907360, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907404, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907454, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907471, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907570, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907573, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907632, "dur": 333, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907967, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916907970, "dur": 81, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908054, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908073, "dur": 47, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908133, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908167, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908221, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908266, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908314, "dur": 89, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908404, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908406, "dur": 126, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908535, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908588, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908600, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916908644, "dur": 2406, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916911091, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916911094, "dur": 1899, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916913027, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916913029, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916913183, "dur": 771, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916913979, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916913981, "dur": 3145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916917130, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916917132, "dur": 28329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916945467, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916945471, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916945507, "dur": 590, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946100, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946247, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946304, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946307, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946429, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946534, "dur": 169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916946707, "dur": 559, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916947270, "dur": 308, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916947580, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916947582, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916947685, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916947788, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916947960, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916948111, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916948261, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916948266, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916948388, "dur": 352, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916948743, "dur": 276, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949022, "dur": 124, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949148, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949150, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949350, "dur": 261, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949615, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949618, "dur": 366, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916949987, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950018, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950250, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950519, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950686, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950688, "dur": 171, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950862, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916950966, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916951138, "dur": 339, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916951481, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916951555, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916951735, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916951900, "dur": 264, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916952166, "dur": 322, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916952491, "dur": 288, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916952792, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916952798, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916952990, "dur": 569, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916953561, "dur": 383, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916953946, "dur": 339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916954289, "dur": 310, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916954603, "dur": 6, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916954610, "dur": 611, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916955224, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916955226, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916955381, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916955630, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916955774, "dur": 256, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916956032, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916956124, "dur": 509, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916956634, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916956636, "dur": 202, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916956841, "dur": 196, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916957040, "dur": 300, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916957344, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916957386, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916957565, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916957834, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916957935, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958026, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958134, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958181, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958254, "dur": 328, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958585, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958691, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916958806, "dur": 395, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916959203, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916959357, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916959360, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916959638, "dur": 333, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916959975, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960134, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960140, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960277, "dur": 12, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960292, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960381, "dur": 272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960656, "dur": 284, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960943, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916960945, "dur": 443, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916961392, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916961426, "dur": 298, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916961727, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916961784, "dur": 278, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916962064, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759916962066, "dur": 134003, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096076, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096080, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096136, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096164, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096186, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096190, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096213, "dur": 17, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096232, "dur": 19, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096254, "dur": 39, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917096294, "dur": 4126, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917100434, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917100436, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917100494, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917100496, "dur": 1063, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917101562, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917101869, "dur": 2086, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917103958, "dur": 336, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917104300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917104302, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917104484, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917104605, "dur": 466, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917105074, "dur": 1912, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917106989, "dur": 915, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917107907, "dur": 851, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917108760, "dur": 650, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917109412, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917109497, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917109518, "dur": 542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917110064, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917110066, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917110457, "dur": 714, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917111176, "dur": 1196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917112374, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917112376, "dur": 358, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917112737, "dur": 846, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917113585, "dur": 1234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917114822, "dur": 462, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917115286, "dur": 744, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917116033, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917116159, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917116419, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917116421, "dur": 1128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917117551, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917117648, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917117649, "dur": 1654, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917119308, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917119441, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917119548, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917119643, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917119697, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917119847, "dur": 162, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120012, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120066, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120115, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120228, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120416, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120417, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120466, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120558, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120703, "dur": 119, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120824, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917120915, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121042, "dur": 64, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121110, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121221, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121266, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121269, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121335, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121442, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121489, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121529, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121566, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121613, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121663, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121860, "dur": 95, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917121958, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122040, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122161, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122196, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122313, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122417, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122454, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122503, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122560, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122598, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122600, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122705, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122767, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122825, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122859, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122898, "dur": 40, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917122940, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917123181, "dur": 154, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917123337, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917123378, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917123421, "dur": 224, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917123648, "dur": 414, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917124064, "dur": 233147, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917357217, "dur": 16, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917357234, "dur": 1409, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917358645, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917358648, "dur": 1686, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917360341, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917360345, "dur": 183940, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917544294, "dur": 22, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917544317, "dur": 2623, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917546945, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917546948, "dur": 20526, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567483, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567487, "dur": 85, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567632, "dur": 35, "ph": "X", "name": "ReadAsync 5225", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567679, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567729, "dur": 11, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567745, "dur": 51, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567799, "dur": 22, "ph": "X", "name": "ProcessMessages 5020", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917567822, "dur": 3539, "ph": "X", "name": "ReadAsync 5020", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917571367, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917571371, "dur": 432, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917571807, "dur": 32, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917571840, "dur": 105277, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917677125, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917677129, "dur": 859, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917677993, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917677995, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917678062, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917678103, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917678106, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917678160, "dur": 27, "ph": "X", "name": "ProcessMessages 6644", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917678188, "dur": 3078, "ph": "X", "name": "ReadAsync 6644", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917681271, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917681274, "dur": 456, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917681734, "dur": 30, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917681766, "dur": 347, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917682116, "dur": 3, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 85899345920, "ts": 1752759917682174, "dur": 2666, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 1408, "ts": 1752759917685885, "dur": 1111, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 81604378624, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 81604378624, "ts": 1752759916838372, "dur": 60689, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 81604378624, "ts": 1752759916899063, "dur": 380, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 1408, "ts": 1752759917686998, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 77309411328, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 77309411328, "ts": 1752759916826196, "dur": 859014, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 77309411328, "ts": 1752759916826619, "dur": 11498, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 77309411328, "ts": 1752759917685221, "dur": 175, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 77309411328, "ts": 1752759917685249, "dur": 81, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 1408, "ts": 1752759917687004, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752759916851369, "dur": 1999, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759916853402, "dur": 12598, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759916866102, "dur": 120, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759916866590, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752759916867325, "dur": 175, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752759916869694, "dur": 939, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752759916873122, "dur": 362, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752759916866228, "dur": 30638, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759916896874, "dur": 785348, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759917682395, "dur": 65, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759917682476, "dur": 602, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752759916866159, "dur": 30725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916896917, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752759916897083, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916897471, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916897593, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916897694, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916897841, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916897930, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916898105, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916898244, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916898481, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916898586, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916898736, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916898825, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916898974, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916899086, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916899259, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916899385, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916899537, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916899648, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752759916900155, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916900273, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752759916900965, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916901263, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916901318, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752759916901964, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752759916902103, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916902161, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916902251, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916902425, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916902550, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916902777, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916902902, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752759916903214, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916903367, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752759916903525, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916903738, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916903984, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752759916904551, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916904750, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752759916905956, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916906064, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752759916907190, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907274, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907326, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907402, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907548, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907655, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907824, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916907967, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916908107, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752759916908323, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916908453, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916908586, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916908704, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916908806, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916908908, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916910470, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916912042, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916913615, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916913711, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916915324, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916916960, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916918288, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916919763, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916921308, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916923818, "dur": 725, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.common@8.0.4/Path/Editor/EditorTool/ScriptableData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752759916922917, "dur": 2595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916925513, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916927767, "dur": 2089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916929856, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916931537, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916933058, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916934386, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916935639, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916937307, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916938596, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916939930, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916941606, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916943251, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916945154, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916945366, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916945445, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916945768, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916946249, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916946745, "dur": 10986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752759916957732, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759916957964, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916958198, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752759916960636, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752759916960757, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752759916961045, "dur": 136817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917097865, "dur": 6501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752759917104367, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917104450, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752759917107989, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917108050, "dur": 3497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752759917111548, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917112019, "dur": 4428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752759917116447, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917116643, "dur": 5600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752759917122243, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917122394, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917122540, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917122651, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917122784, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917122894, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917123054, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917123110, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752759917123292, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917123780, "dur": 557777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752759917681588, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752759917681559, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752759917681664, "dur": 465, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752759916866162, "dur": 30735, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916896923, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752759916897131, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916897422, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916897478, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916897610, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916897722, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916897889, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916897991, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916898187, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916898313, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916898480, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916898583, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916898737, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916898821, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916898969, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916899068, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916899293, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916899396, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916899835, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916899906, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916900053, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916900160, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916900261, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916900450, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916901755, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916901829, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916902303, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916902429, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916902553, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916902789, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916902999, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916903223, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916903375, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916903587, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916903800, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916904002, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916904705, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916904797, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916905015, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916905189, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752759916905312, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916905478, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916905532, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916905647, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916905714, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916905797, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916905919, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916906014, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916906925, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916907593, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916907783, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916907928, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916908065, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916908207, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916908328, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916908479, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752759916908531, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916908655, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916908769, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916908898, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916910465, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916912056, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916913595, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916915247, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916916905, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916918213, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916919623, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916921229, "dur": 1583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916923784, "dur": 721, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/Codebase/SetMemberDescriptor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752759916922812, "dur": 2464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916925277, "dur": 2322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916927600, "dur": 2153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916929753, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916931349, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916932937, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916934300, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916935500, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916937180, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916938452, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916939754, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916941453, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916943089, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916945002, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916945585, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916945757, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916946239, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916946927, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916946985, "dur": 2079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916949064, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916949414, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916949793, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916950214, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916950425, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916950482, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916950636, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916952412, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916952771, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916952861, "dur": 2931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916955792, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916955899, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916956339, "dur": 2002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916958341, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916958496, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752759916958547, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916958677, "dur": 1964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759916960641, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916960743, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916961188, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916961292, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916961772, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916961841, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916962095, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752759916962169, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759916962469, "dur": 395096, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759917358735, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752759917358466, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759917359026, "dur": 185477, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752759917545341, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752759917545326, "dur": 1280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752759917547113, "dur": 186, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759917677378, "dur": 1174, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752759917547311, "dur": 131251, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752759917681538, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752759917681529, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752759917681619, "dur": 477, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752759917682099, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916866169, "dur": 30737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916896917, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916897533, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916897664, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916897777, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916897904, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916897982, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916898136, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916898325, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916898410, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916898572, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916898659, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916898794, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916898890, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916899026, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916899131, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916899283, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916899403, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916899577, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916899664, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752759916900191, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916900319, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916900976, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916901962, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916902106, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916902177, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916902229, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916902320, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916902479, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916902557, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916902806, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916902915, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916903184, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916903355, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916903550, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916903745, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916904717, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916904805, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916905032, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916905216, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916905294, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916905453, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916905545, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916905666, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916905746, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916906357, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916907197, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916907372, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916907519, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916907617, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916907805, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916907861, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916907984, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916908123, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916908271, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916908357, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916908438, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916908578, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752759916908707, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916908824, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916908924, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916910497, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916912099, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916913625, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916915227, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916916900, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916918207, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916919639, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916921242, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916923728, "dur": 720, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.spriteshape@9.0.5/Runtime/External/LibTessDotNet/Sweep.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752759916922853, "dur": 2441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916925294, "dur": 2324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916927618, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916929770, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916931393, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916932972, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916934296, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916935472, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916937199, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916938474, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916939779, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916941479, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916943111, "dur": 1928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916945039, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916945778, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916946251, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916946627, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752759916947675, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916947856, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916947983, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916948036, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752759916950442, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916950818, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916951276, "dur": 3236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752759916954513, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916954699, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916954768, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916954966, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916955144, "dur": 1029, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752759916956173, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916956297, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_DAC173EE5EC432FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916956535, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916956592, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_C1F9C6FB58945FCF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916957068, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916957150, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916957261, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916957778, "dur": 1200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752759916958978, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916959096, "dur": 3020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759916962116, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752759916962191, "dur": 137482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917099674, "dur": 4975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752759917104649, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917104745, "dur": 5049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752759917109795, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917109861, "dur": 6388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752759917116250, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917116340, "dur": 6084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752759917122425, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917122581, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917122651, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917122739, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917122827, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917122900, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752759917122978, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752759917123035, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917123187, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917123277, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917123604, "dur": 234889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917358754, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752759917358494, "dur": 1497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752759917360457, "dur": 202, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917567772, "dur": 410, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752759917360671, "dur": 207557, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752759917571424, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752759917571414, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752759917571619, "dur": 588, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752759917572210, "dur": 109974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916866176, "dur": 30737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916896917, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916897443, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916897529, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916897664, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916897783, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916898072, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916898161, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916898372, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916898496, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916898637, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916898737, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916898867, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916898969, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916899114, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916899238, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916899786, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916899863, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916900004, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916900119, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916900272, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916900428, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916901032, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916902033, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916902526, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916902745, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916903082, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916903290, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916904669, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916904783, "dur": 1399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752759916906218, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916906342, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916906943, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916907241, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752759916907666, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916907870, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916908011, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916908140, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916908339, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916908503, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916908623, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916908743, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916908849, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752759916908933, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916909045, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916910508, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916912123, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916913648, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916915048, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916916718, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916918028, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916919419, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916921058, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916923731, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/Nesting/GraphInputAnalyser.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752759916922616, "dur": 2315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916924931, "dur": 2495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916927426, "dur": 2083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916929509, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916931119, "dur": 1696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916932816, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916934172, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916935320, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916936990, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916938313, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916939529, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916941243, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916942856, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916944631, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916945358, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916945591, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916945780, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916946268, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916946596, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916946689, "dur": 1440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752759916948129, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916948339, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916948940, "dur": 1972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752759916950912, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916951044, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916951098, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_4404C737633913B4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916951201, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916951305, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916951591, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916951828, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916952008, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916952139, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916952252, "dur": 5716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752759916957969, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916958095, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752759916958643, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916958728, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752759916960222, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759916960325, "dur": 137548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917097875, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752759917100444, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917100757, "dur": 4128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752759917104885, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917104965, "dur": 4924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752759917109890, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917110004, "dur": 3911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752759917113915, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917113980, "dur": 3942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752759917117922, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917118067, "dur": 5654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752759917123721, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752759917123820, "dur": 558432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916866183, "dur": 30736, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916896923, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916897524, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916897642, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916897726, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916898101, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916898229, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916898374, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916898506, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916898641, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916898747, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916898887, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916898981, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916899149, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916899266, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916899414, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916899490, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916899650, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916899794, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916899873, "dur": 3387, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916903260, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916903389, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916903650, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752759916904559, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916904765, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916904996, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916905197, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916905256, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916905375, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752759916907039, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907271, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907336, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907419, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907543, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907622, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907837, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916907959, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908101, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752759916908157, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908260, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752759916908423, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908564, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908680, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908786, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908891, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916908990, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916910503, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916912174, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916913695, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916915340, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916916970, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916918301, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916919789, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916921344, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916923796, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.common@8.0.4/Editor/VisualElementExtensions.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752759916922966, "dur": 2710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916925676, "dur": 2234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916927910, "dur": 1960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916929870, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916931549, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916933099, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916934414, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916935692, "dur": 1685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916937377, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916938652, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916940001, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916941659, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916943327, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916944880, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916945514, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916945671, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916945797, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916946340, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916946593, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916946657, "dur": 1387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752759916948045, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916948160, "dur": 3040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752759916951201, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916951476, "dur": 5147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916956624, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916956683, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752759916957048, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916957201, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916957284, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916957370, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752759916958166, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916958465, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916958628, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759916959701, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752759916959934, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752759916960523, "dur": 137364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759917097888, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752759917100727, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759917100926, "dur": 3152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752759917104078, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759917104199, "dur": 6652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752759917110851, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759917110942, "dur": 4739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752759917115681, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759917115781, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752759917119383, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752759917119473, "dur": 4984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752759917124484, "dur": 557703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916866192, "dur": 30733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916896928, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916897427, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916897519, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916897644, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916897738, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916897884, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916897961, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916898113, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916898312, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916898404, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916898560, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916898653, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916898804, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916898900, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916899043, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916899155, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916899322, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916899432, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916899611, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916899698, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752759916901014, "dur": 12139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752759916913153, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916913373, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916913463, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916913565, "dur": 3851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916917417, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916917501, "dur": 28089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752759916945591, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916945767, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916945883, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752759916946237, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916946537, "dur": 2175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752759916948712, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916948922, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752759916949755, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752759916950206, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916950429, "dur": 1883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1752759916952312, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916952371, "dur": 2068, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917096336, "dur": 344, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759916954831, "dur": 141868, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1752759917097851, "dur": 3520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752759917101372, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917101478, "dur": 3447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752759917104925, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917105032, "dur": 9208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752759917114241, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917114318, "dur": 7924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752759917122244, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917122452, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752759917122529, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917122659, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917122798, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917122867, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752759917122922, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917123014, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752759917123069, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917123176, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917123245, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917123309, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752759917123827, "dur": 558470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916866200, "dur": 30733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916896936, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916897422, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916897508, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916897587, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916897663, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916897816, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916897904, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916898036, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916898192, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916898365, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916898435, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916898595, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916898662, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916898809, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916898911, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916899057, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916899180, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916899359, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916899453, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916899623, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916899692, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916900261, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916900360, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916900662, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752759916901189, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916901270, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752759916901748, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752759916902032, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916902135, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752759916902361, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916902446, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752759916902565, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916902779, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752759916903119, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916903303, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916903409, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916903701, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916905275, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916905462, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916905575, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916906132, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916906646, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916906992, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916907264, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916907322, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916907492, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916907594, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916907763, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916907931, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916908075, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916908235, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916908392, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752759916908453, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916908570, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916908687, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916908776, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916908894, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916910455, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916912033, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916913570, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916913680, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916915288, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916916949, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916918262, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916919716, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916921267, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916923790, "dur": 728, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.common@8.0.4/Path/Editor/IMGUI/RectSelector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752759916922872, "dur": 2596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916925468, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916927731, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916929810, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916931464, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916933030, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916934357, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916935606, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916937282, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916938558, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916939860, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916941550, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916943197, "dur": 1900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916945097, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916945579, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916945636, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916945729, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916946508, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916946854, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752759916948238, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916948478, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916948601, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752759916948744, "dur": 4550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752759916953294, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916953496, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752759916954239, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916954396, "dur": 4721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752759916959117, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759916959227, "dur": 138625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759917097853, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752759917101911, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759917102015, "dur": 2880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752759917104895, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759917105067, "dur": 3193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752759917108262, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759917108409, "dur": 4688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752759917113097, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759917113205, "dur": 3989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752759917117195, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752759917118012, "dur": 6000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752759917124057, "dur": 558220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916866210, "dur": 30728, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916896941, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916897540, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916897661, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916897763, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916897904, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916897983, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916898126, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916898310, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916898475, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916898613, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916898706, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916898863, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916898956, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916899110, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916899209, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916899836, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916899950, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916900092, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916900243, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916900347, "dur": 1450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916901797, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916901898, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916902370, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916902462, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916902622, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916902823, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916903065, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916903892, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916904109, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752759916904446, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916904561, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752759916905260, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916905355, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916906341, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916907196, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916907277, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916907333, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916907431, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916907558, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916907693, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916907874, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908018, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908147, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752759916908338, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908467, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908583, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908712, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908811, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916908909, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916910479, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916912066, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916913599, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916915223, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916916893, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916918184, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916919653, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916921254, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916923811, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Connections/ValueConnectionWidget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752759916922846, "dur": 2446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916925292, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916927616, "dur": 2146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916929762, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916931379, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916932946, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916934293, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916935461, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916937169, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916938446, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916939731, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916941440, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916943074, "dur": 1946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916945021, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916945363, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916945445, "dur": 144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916945635, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916945801, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916946273, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916946591, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916947157, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752759916949154, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916949334, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_C0EF7B73E8141BD2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916949432, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916949495, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916950241, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916950354, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752759916952085, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916952301, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916952416, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916952489, "dur": 3476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752759916955966, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916956165, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916956536, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916956629, "dur": 2934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752759916959563, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759916959680, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752759916959963, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752759916960646, "dur": 137244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759917097890, "dur": 2837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752759917100728, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759917100879, "dur": 6471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752759917107350, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759917107454, "dur": 3883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752759917111338, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759917111673, "dur": 4702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752759917116376, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759917116493, "dur": 7023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752759917123588, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752759917124480, "dur": 557760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916866216, "dur": 30727, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916896945, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916897532, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916897663, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916897744, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916897887, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916897974, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916898143, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916898394, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916898527, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916898682, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916898789, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916898937, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916899029, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916899195, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916899295, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916899450, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916899553, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916899781, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916899857, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916899954, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916900068, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916900128, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916900255, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916900454, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752759916901229, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916901335, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752759916901961, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752759916902105, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916902168, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916902258, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916902421, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916902499, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916902628, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752759916902727, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916902860, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752759916903050, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916903275, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916903396, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916903685, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916903804, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752759916904457, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916904588, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752759916904643, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916904722, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752759916906168, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752759916906985, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916907255, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916907340, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916907452, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916907575, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916907727, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916907912, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916908052, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916908176, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752759916908448, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916908594, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916908723, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916908835, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916908954, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916910491, "dur": 1619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916912110, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916913662, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916915277, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916916916, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916918217, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916919682, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916921261, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916923740, "dur": 744, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/FlowGraphEditor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752759916922841, "dur": 2542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916925383, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916927708, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916929791, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916931456, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916933033, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916934352, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916935617, "dur": 1671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916937288, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916938573, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916939919, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916941592, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916943236, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916945093, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916945362, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916945784, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916946506, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916946819, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916946883, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752759916948358, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916948518, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_BB27E5BA9B36011C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916948720, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916948821, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916949095, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916949704, "dur": 1342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752759916951046, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916951233, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916951587, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916951644, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916951784, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916951928, "dur": 1226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752759916953154, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916953284, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752759916953875, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916954042, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752759916955478, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916955755, "dur": 3624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752759916959381, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759916959532, "dur": 138322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917097855, "dur": 4384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752759917102239, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917102346, "dur": 3018, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752759917105364, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917105443, "dur": 3671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752759917109115, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917109206, "dur": 3570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752759917112776, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917112869, "dur": 3932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752759917116801, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917117275, "dur": 6402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752759917123761, "dur": 447659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752759917571451, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752759917571423, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752759917571579, "dur": 638, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752759917572218, "dur": 110068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916866223, "dur": 30725, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916896948, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916897455, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916897598, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916897711, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916897905, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916898004, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916898218, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916898346, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916898484, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916898595, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916898727, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916898816, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916898955, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916899040, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916899214, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916899337, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916899455, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916899589, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916899823, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916899972, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916900073, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916900174, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916900291, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752759916901246, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916901362, "dur": 8940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916910302, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916910519, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916910600, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916910769, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916912315, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916913897, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916915541, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916917079, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916918446, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916920020, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916921455, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916923792, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Runtime/SpriteLib/SpriteLibraryAsset.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752759916923136, "dur": 3123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916926259, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916928206, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916929965, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916931766, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916933260, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916934508, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916935832, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916937512, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916938723, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916940111, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916941775, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916943468, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916944332, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916945203, "dur": 131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916945364, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916945682, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916945807, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916946272, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916946916, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916949386, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916949651, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916949963, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916952406, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916952711, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_38895AFAB811F11B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916952877, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916952966, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916953067, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916954913, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916955686, "dur": 1814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916957500, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916957665, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916958846, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916959049, "dur": 2145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759916961194, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916961253, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752759916961313, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752759916961818, "dur": 137462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917099280, "dur": 6202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752759917105482, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917105554, "dur": 4816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752759917110371, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917110512, "dur": 4650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752759917115163, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917115270, "dur": 4352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752759917119622, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917119823, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917120092, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917120350, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917120406, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917120513, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917120571, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917120730, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917121268, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917121666, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917122294, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917122434, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917122587, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917122709, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917122831, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917122996, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752759917123131, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917123258, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917123338, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752759917124067, "dur": 558124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752759917684205, "dur": 518, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 1408, "ts": 1752759917687128, "dur": 40, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 1408, "ts": 1752759917698477, "dur": 1507, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 1408, "ts": 1752759917685818, "dur": 14227, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}