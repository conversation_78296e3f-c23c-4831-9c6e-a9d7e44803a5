{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 1937, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 1937, "ts": 1752803610948823, "dur": 46, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 1937, "ts": 1752803610948974, "dur": 7, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752803610222487, "dur": 5695, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752803610228187, "dur": 35140, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752803610263329, "dur": 31083, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 1937, "ts": 1752803610948987, "dur": 17, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 592705486848, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610222403, "dur": 15420, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610237825, "dur": 709804, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610237867, "dur": 142, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610238013, "dur": 509, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610238525, "dur": 27, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610238553, "dur": 17821, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610256381, "dur": 3, "ph": "X", "name": "ProcessMessages 2976", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610265868, "dur": 88, "ph": "X", "name": "ReadAsync 2976", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610265958, "dur": 4, "ph": "X", "name": "ProcessMessages 8188", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610265963, "dur": 72, "ph": "X", "name": "ReadAsync 8188", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266040, "dur": 2, "ph": "X", "name": "ProcessMessages 2157", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266043, "dur": 72, "ph": "X", "name": "ReadAsync 2157", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266132, "dur": 1, "ph": "X", "name": "ProcessMessages 2511", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266134, "dur": 43, "ph": "X", "name": "ReadAsync 2511", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266179, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266181, "dur": 89, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266272, "dur": 1, "ph": "X", "name": "ProcessMessages 2365", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266274, "dur": 44, "ph": "X", "name": "ReadAsync 2365", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266320, "dur": 2, "ph": "X", "name": "ProcessMessages 1770", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266323, "dur": 57, "ph": "X", "name": "ReadAsync 1770", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266381, "dur": 1, "ph": "X", "name": "ProcessMessages 1923", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266383, "dur": 54, "ph": "X", "name": "ReadAsync 1923", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266441, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266443, "dur": 211, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266657, "dur": 9, "ph": "X", "name": "ProcessMessages 2018", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266667, "dur": 43, "ph": "X", "name": "ReadAsync 2018", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266712, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266713, "dur": 100, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266815, "dur": 1, "ph": "X", "name": "ProcessMessages 1390", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266817, "dur": 39, "ph": "X", "name": "ReadAsync 1390", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266858, "dur": 1, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266859, "dur": 35, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266897, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266907, "dur": 46, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266955, "dur": 1, "ph": "X", "name": "ProcessMessages 1763", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266957, "dur": 35, "ph": "X", "name": "ReadAsync 1763", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266993, "dur": 1, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610266995, "dur": 42, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267040, "dur": 32, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267075, "dur": 39, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267115, "dur": 1, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267117, "dur": 74, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267193, "dur": 1, "ph": "X", "name": "ProcessMessages 1673", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267195, "dur": 490, "ph": "X", "name": "ReadAsync 1673", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267686, "dur": 1, "ph": "X", "name": "ProcessMessages 1511", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267688, "dur": 31, "ph": "X", "name": "ReadAsync 1511", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267721, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267723, "dur": 46, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267771, "dur": 5, "ph": "X", "name": "ProcessMessages 1373", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267776, "dur": 85, "ph": "X", "name": "ReadAsync 1373", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267865, "dur": 2, "ph": "X", "name": "ProcessMessages 1673", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610267867, "dur": 218, "ph": "X", "name": "ReadAsync 1673", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268088, "dur": 33, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268123, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268125, "dur": 437, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268563, "dur": 2, "ph": "X", "name": "ProcessMessages 1413", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268567, "dur": 41, "ph": "X", "name": "ReadAsync 1413", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268609, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268611, "dur": 151, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268764, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268766, "dur": 59, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268826, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268828, "dur": 76, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268911, "dur": 1, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268922, "dur": 63, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268987, "dur": 1, "ph": "X", "name": "ProcessMessages 1698", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610268989, "dur": 69, "ph": "X", "name": "ReadAsync 1698", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269059, "dur": 1, "ph": "X", "name": "ProcessMessages 1370", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269060, "dur": 44, "ph": "X", "name": "ReadAsync 1370", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269107, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269109, "dur": 49, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269159, "dur": 1, "ph": "X", "name": "ProcessMessages 1326", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269161, "dur": 48, "ph": "X", "name": "ReadAsync 1326", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269211, "dur": 39, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269252, "dur": 42, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269297, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269299, "dur": 57, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269358, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269361, "dur": 42, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269404, "dur": 5, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269411, "dur": 53, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269466, "dur": 33, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269501, "dur": 48, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269559, "dur": 39, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269599, "dur": 1, "ph": "X", "name": "ProcessMessages 1282", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269601, "dur": 31, "ph": "X", "name": "ReadAsync 1282", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269634, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269636, "dur": 47, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269685, "dur": 68, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269756, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269758, "dur": 40, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269799, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269801, "dur": 52, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269855, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269857, "dur": 34, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269893, "dur": 39, "ph": "X", "name": "ReadAsync 1173", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269935, "dur": 43, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269979, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610269981, "dur": 78, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270060, "dur": 1, "ph": "X", "name": "ProcessMessages 1880", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270062, "dur": 30, "ph": "X", "name": "ReadAsync 1880", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270094, "dur": 51, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270146, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270148, "dur": 42, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270195, "dur": 1, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270197, "dur": 54, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270252, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270254, "dur": 114, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270369, "dur": 1, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270371, "dur": 45, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270418, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270420, "dur": 69, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270507, "dur": 1, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270509, "dur": 22, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270533, "dur": 32, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270568, "dur": 17, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270587, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270608, "dur": 35, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610270650, "dur": 447, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271138, "dur": 8, "ph": "X", "name": "ProcessMessages 1476", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271156, "dur": 81, "ph": "X", "name": "ReadAsync 1476", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271240, "dur": 1, "ph": "X", "name": "ProcessMessages 1281", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271243, "dur": 43, "ph": "X", "name": "ReadAsync 1281", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271287, "dur": 1, "ph": "X", "name": "ProcessMessages 1435", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271289, "dur": 35, "ph": "X", "name": "ReadAsync 1435", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271331, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271333, "dur": 89, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271423, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271509, "dur": 84, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271595, "dur": 2, "ph": "X", "name": "ProcessMessages 2117", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271597, "dur": 63, "ph": "X", "name": "ReadAsync 2117", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271668, "dur": 2, "ph": "X", "name": "ProcessMessages 1636", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271690, "dur": 62, "ph": "X", "name": "ReadAsync 1636", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271787, "dur": 208, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271997, "dur": 1, "ph": "X", "name": "ProcessMessages 1221", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610271999, "dur": 64, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272064, "dur": 1, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272066, "dur": 115, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272185, "dur": 50, "ph": "X", "name": "ReadAsync 1423", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272237, "dur": 147, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272386, "dur": 2, "ph": "X", "name": "ProcessMessages 2075", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272389, "dur": 343, "ph": "X", "name": "ReadAsync 2075", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272734, "dur": 1, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272736, "dur": 115, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272854, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272856, "dur": 87, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272945, "dur": 1, "ph": "X", "name": "ProcessMessages 1889", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610272947, "dur": 57, "ph": "X", "name": "ReadAsync 1889", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273006, "dur": 1, "ph": "X", "name": "ProcessMessages 1466", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273008, "dur": 42, "ph": "X", "name": "ReadAsync 1466", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273052, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273054, "dur": 128, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273184, "dur": 1, "ph": "X", "name": "ProcessMessages 1495", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273185, "dur": 261, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273448, "dur": 3, "ph": "X", "name": "ProcessMessages 5011", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273453, "dur": 69, "ph": "X", "name": "ReadAsync 5011", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273524, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273526, "dur": 56, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273589, "dur": 2, "ph": "X", "name": "ProcessMessages 1703", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273592, "dur": 106, "ph": "X", "name": "ReadAsync 1703", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273699, "dur": 2, "ph": "X", "name": "ProcessMessages 2172", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273702, "dur": 62, "ph": "X", "name": "ReadAsync 2172", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273766, "dur": 1, "ph": "X", "name": "ProcessMessages 1395", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273768, "dur": 71, "ph": "X", "name": "ReadAsync 1395", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273841, "dur": 1, "ph": "X", "name": "ProcessMessages 1173", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273843, "dur": 35, "ph": "X", "name": "ReadAsync 1173", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273879, "dur": 1, "ph": "X", "name": "ProcessMessages 1016", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273881, "dur": 18, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610273901, "dur": 143, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274046, "dur": 2, "ph": "X", "name": "ProcessMessages 2967", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274048, "dur": 56, "ph": "X", "name": "ReadAsync 2967", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274106, "dur": 1, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274136, "dur": 261, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274398, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274463, "dur": 42, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274507, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274508, "dur": 342, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274852, "dur": 1, "ph": "X", "name": "ProcessMessages 1433", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610274853, "dur": 687, "ph": "X", "name": "ReadAsync 1433", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610275542, "dur": 2, "ph": "X", "name": "ProcessMessages 3512", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610275770, "dur": 33, "ph": "X", "name": "ReadAsync 3512", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610275805, "dur": 3, "ph": "X", "name": "ProcessMessages 4004", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610275808, "dur": 220, "ph": "X", "name": "ReadAsync 4004", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610276030, "dur": 1, "ph": "X", "name": "ProcessMessages 1822", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610276046, "dur": 407, "ph": "X", "name": "ReadAsync 1822", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610276455, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610276456, "dur": 359, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610276842, "dur": 151, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610276996, "dur": 445, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610277443, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610277446, "dur": 314, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610277765, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610277767, "dur": 184, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610277954, "dur": 162, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610278121, "dur": 211, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610278335, "dur": 197, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610278535, "dur": 31, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610278570, "dur": 377, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610278949, "dur": 229, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610279181, "dur": 214, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610279403, "dur": 198, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610279604, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610279606, "dur": 520, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610280129, "dur": 127, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610280260, "dur": 483, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610280744, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610280746, "dur": 147, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610280897, "dur": 517, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610281416, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610281417, "dur": 474, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610281895, "dur": 53, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610281957, "dur": 496, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282456, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282457, "dur": 42, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282502, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282504, "dur": 299, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282806, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282807, "dur": 178, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282987, "dur": 9, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610282998, "dur": 465, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610283466, "dur": 111, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610283579, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610283581, "dur": 83, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610283666, "dur": 489, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610284158, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610284160, "dur": 238, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610284400, "dur": 351, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610284779, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610284781, "dur": 518, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610285308, "dur": 413, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610285723, "dur": 5572, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291299, "dur": 8, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291308, "dur": 39, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291351, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291354, "dur": 211, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291569, "dur": 426, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291996, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610291998, "dur": 32, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610292034, "dur": 210, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610292247, "dur": 29, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610292278, "dur": 435, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610292715, "dur": 160, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610292877, "dur": 666, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610293545, "dur": 1, "ph": "X", "name": "ProcessMessages 1229", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610293547, "dur": 45, "ph": "X", "name": "ReadAsync 1229", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610293594, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610293596, "dur": 38, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610293637, "dur": 559, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610294198, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610294200, "dur": 196, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610294398, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610294400, "dur": 159, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610294562, "dur": 734, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610295311, "dur": 137, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610295488, "dur": 351, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610295842, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610295843, "dur": 143, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610295989, "dur": 379, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610296373, "dur": 163, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610296538, "dur": 1, "ph": "X", "name": "ProcessMessages 1329", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610296540, "dur": 37, "ph": "X", "name": "ReadAsync 1329", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610296579, "dur": 28, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610296609, "dur": 751, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610297362, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610297364, "dur": 184, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610297550, "dur": 15, "ph": "X", "name": "ProcessMessages 1858", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610297567, "dur": 44, "ph": "X", "name": "ReadAsync 1858", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610297619, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610297621, "dur": 694, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610298317, "dur": 154, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610298473, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610298497, "dur": 413, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610298912, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610298914, "dur": 150, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610299068, "dur": 584, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610299654, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610299657, "dur": 420, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610300088, "dur": 116, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610300206, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610300208, "dur": 777, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610300988, "dur": 150, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610301145, "dur": 1108, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610302308, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610302310, "dur": 1543, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610303859, "dur": 647, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610304509, "dur": 201, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610304730, "dur": 2722, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610307455, "dur": 1, "ph": "X", "name": "ProcessMessages 1668", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610307463, "dur": 56, "ph": "X", "name": "ReadAsync 1668", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610307522, "dur": 75, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610307598, "dur": 1, "ph": "X", "name": "ProcessMessages 1183", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610307600, "dur": 206, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610307808, "dur": 606, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610308417, "dur": 165, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610308584, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610308595, "dur": 79, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610308675, "dur": 1, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610308677, "dur": 788, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610309537, "dur": 162, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610309703, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610309706, "dur": 499, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610310206, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610310208, "dur": 385, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610310596, "dur": 341, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610310942, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610310943, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610310998, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311000, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311084, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311185, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311227, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311351, "dur": 384, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311738, "dur": 77, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610311818, "dur": 369, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312190, "dur": 214, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312407, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312512, "dur": 114, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312629, "dur": 35, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312688, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312788, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312791, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610312880, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313003, "dur": 86, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313091, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313148, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313241, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313294, "dur": 27, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313328, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313382, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313487, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610313524, "dur": 567, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314097, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314143, "dur": 9, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314154, "dur": 64, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314225, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314326, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314328, "dur": 134, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314464, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314466, "dur": 92, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314560, "dur": 134, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314696, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314698, "dur": 80, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314781, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314833, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314891, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314894, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610314957, "dur": 51, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315012, "dur": 126, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315139, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315141, "dur": 81, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315224, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315274, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315325, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315328, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315402, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315404, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315458, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315459, "dur": 114, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315575, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315577, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315639, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315687, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315721, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315784, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315853, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315855, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315901, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315906, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315950, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610315999, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316040, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316041, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316095, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316136, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316138, "dur": 113, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316258, "dur": 47, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316317, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316356, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316357, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316399, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316400, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316441, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316443, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316488, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316533, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316583, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316585, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316654, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316660, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316717, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316788, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316789, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316826, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316934, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316936, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316975, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610316977, "dur": 93, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317072, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317126, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317191, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317193, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317244, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317247, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317289, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317291, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317334, "dur": 6, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317341, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317385, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317454, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317455, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317496, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317532, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317654, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610317658, "dur": 21786, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610339450, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610339453, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610339713, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610339716, "dur": 981, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610340703, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610340895, "dur": 757, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610341655, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610341876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610341878, "dur": 3068, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610344951, "dur": 11950, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610356907, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610356911, "dur": 580, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610357495, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610357497, "dur": 521, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358022, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358061, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358063, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358161, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358228, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358395, "dur": 524, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358921, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358923, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610358988, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359244, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359248, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359459, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359583, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359626, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359662, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359791, "dur": 157, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610359952, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610360128, "dur": 249, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610360380, "dur": 337, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610360720, "dur": 458, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361182, "dur": 290, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361478, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361595, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361597, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361728, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361776, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361837, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610361990, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362031, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362071, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362092, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362394, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362436, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362438, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362484, "dur": 210, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610362696, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610363080, "dur": 161, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610363243, "dur": 169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610363416, "dur": 433, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610363851, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610364090, "dur": 321, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610364415, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610364417, "dur": 208, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610364628, "dur": 202, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610364836, "dur": 569, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610365406, "dur": 189, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610365598, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610365748, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610365751, "dur": 265, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366018, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366151, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366183, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366260, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366368, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366415, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366486, "dur": 231, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366720, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610366755, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367016, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367223, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367369, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367444, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367494, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367540, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367587, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367800, "dur": 114, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367924, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610367930, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368131, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368324, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368416, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368505, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368512, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368543, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368617, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368714, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610368715, "dur": 282, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369000, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369095, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369142, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369297, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369412, "dur": 400, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369814, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369816, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369855, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610369884, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610370120, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610370204, "dur": 448, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610370655, "dur": 126724, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497397, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497400, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497448, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497497, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497542, "dur": 37, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497581, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497616, "dur": 45, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497663, "dur": 42, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610497706, "dur": 4066, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610501775, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610501777, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610501854, "dur": 1198, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610503055, "dur": 282, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610503349, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610503404, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610503407, "dur": 1500, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610504910, "dur": 1479, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610506391, "dur": 1056, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610507450, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610507453, "dur": 270, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610507726, "dur": 363, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610508092, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610508500, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610508502, "dur": 817, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610509322, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610509332, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610509393, "dur": 2325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610511723, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610511897, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610512224, "dur": 561, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610512789, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610512791, "dur": 817, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610513611, "dur": 399, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610514014, "dur": 637, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610514665, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610514928, "dur": 422, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610515353, "dur": 440, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610515796, "dur": 903, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610516702, "dur": 1298, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610518004, "dur": 807, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610518829, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610518832, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610518907, "dur": 1149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520059, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520265, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520266, "dur": 106, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520376, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520428, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520429, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520562, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520618, "dur": 50, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520672, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520718, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520847, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520889, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520943, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610520989, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521026, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521078, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521080, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521131, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521292, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521331, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521387, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521505, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521506, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521628, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521686, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521807, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521893, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521900, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610521953, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522013, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522106, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522165, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522215, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522372, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522374, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522414, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522466, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522502, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522532, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522601, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522713, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522800, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522884, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610522984, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610523028, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610523118, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610523187, "dur": 192, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610523387, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610523393, "dur": 719, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524122, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524221, "dur": 292, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524515, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524517, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524600, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524602, "dur": 371, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610524975, "dur": 145241, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610670223, "dur": 16, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610670240, "dur": 1246, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610671488, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610671490, "dur": 1811, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610673303, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610673305, "dur": 156536, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610829850, "dur": 20, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610829872, "dur": 3108, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610832986, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610832990, "dur": 3314, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836310, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836313, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836369, "dur": 1, "ph": "X", "name": "ProcessMessages 5225", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836403, "dur": 53, "ph": "X", "name": "ReadAsync 5225", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836460, "dur": 22, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836485, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836541, "dur": 23, "ph": "X", "name": "ProcessMessages 5421", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610836566, "dur": 3098, "ph": "X", "name": "ReadAsync 5421", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610839684, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610839687, "dur": 397, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610840089, "dur": 35, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610840125, "dur": 99779, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610939912, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610939915, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610939983, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610939985, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610940046, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610940094, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610940142, "dur": 17, "ph": "X", "name": "ProcessMessages 6644", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610940159, "dur": 3143, "ph": "X", "name": "ReadAsync 6644", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610943305, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610943308, "dur": 360, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610943670, "dur": 17, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610943688, "dur": 285, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 592705486848, "ts": 1752803610943976, "dur": 3643, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 1937, "ts": 1752803610949005, "dur": 945, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 588410519552, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 588410519552, "ts": 1752803610221833, "dur": 72585, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 588410519552, "ts": 1752803610294419, "dur": 37, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 1937, "ts": 1752803610949952, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 584115552256, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 584115552256, "ts": 1752803610199183, "dur": 748725, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 584115552256, "ts": 1752803610199950, "dur": 20899, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 584115552256, "ts": 1752803610947922, "dur": 134, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 584115552256, "ts": 1752803610947950, "dur": 59, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 584115552256, "ts": 1752803610948057, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 1937, "ts": 1752803610949957, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752803610237887, "dur": 2491, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752803610240411, "dur": 15662, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752803610256171, "dur": 169, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752803610257261, "dur": 9273, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752803610267222, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752803610290519, "dur": 1345, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752803610304957, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752803610305421, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752803610256348, "dur": 53604, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752803610309960, "dur": 634282, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752803610944380, "dur": 67, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752803610944515, "dur": 833, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752803610256265, "dur": 53703, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610309971, "dur": 3590, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610313561, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610313720, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610313783, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610313958, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610314071, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752803610314230, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610314306, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610314443, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610314528, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610314686, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610314777, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752803610314941, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315005, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752803610315253, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315324, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315421, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315503, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315614, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752803610315761, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315835, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610315970, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316052, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316133, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316233, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316326, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316444, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316562, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316678, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316774, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316881, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610316974, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317072, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317186, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317283, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317400, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317497, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752803610317578, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317691, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317799, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610317947, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610318056, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610319254, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610320869, "dur": 1542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610322411, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610323889, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610325618, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610327256, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610328770, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610330526, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610332040, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610333606, "dur": 1619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610335226, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610336900, "dur": 1834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610338734, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610340916, "dur": 2158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610343074, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610344468, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610346000, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610347548, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610348992, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610350470, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610351963, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610353394, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610354852, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610355620, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610356973, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610357139, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610357395, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610358044, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610358647, "dur": 1331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610359979, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610360332, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_4D635C4BE46FE4CD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610360434, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610360945, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610361912, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610362291, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_C193AA5F93FF728B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610362407, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610362564, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610362900, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752803610363352, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610363440, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610364352, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610364470, "dur": 1533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610366004, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610366099, "dur": 1442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610367541, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610367701, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610368757, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610368867, "dur": 130625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610499499, "dur": 4163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610503663, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610503957, "dur": 4242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610508199, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610508317, "dur": 4427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610512744, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610512862, "dur": 3431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610516293, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610516365, "dur": 4160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610520525, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610521189, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610521357, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610521949, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610522073, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610522437, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610522531, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610522847, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523079, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752803610523130, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523324, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523420, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523557, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523629, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523694, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610523810, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610524763, "dur": 146822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610671867, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752803610671585, "dur": 1484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610673543, "dur": 225, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610836735, "dur": 333, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752803610673781, "dur": 163295, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752803610840015, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752803610840009, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752803610840208, "dur": 412, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752803610840621, "dur": 103591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610256271, "dur": 53705, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610309999, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752803610310217, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610310568, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610310656, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610310900, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610311026, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610311262, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610311361, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610311574, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610311789, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610312076, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610312302, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610312605, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610312729, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610312975, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610313134, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610313339, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610313497, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610313796, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610313914, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610314071, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610314152, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610314268, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610314381, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610314514, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610314633, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610314706, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752803610314859, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610314939, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315026, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752803610315188, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315279, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752803610315441, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315547, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315665, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315744, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315827, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610315939, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316066, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316158, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316268, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316372, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316472, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316589, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752803610316766, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316865, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610316966, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317059, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317168, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317262, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317377, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317481, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317552, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317662, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317754, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317878, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610317998, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610319182, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610320761, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610322271, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610323749, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610325406, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610327098, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610328583, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610330307, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610331960, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610333547, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610335171, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610336822, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610338669, "dur": 2182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610340854, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610342729, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610344293, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610345599, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610347281, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610348664, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610350140, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610351565, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610353069, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610354440, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610355102, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610356553, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610356982, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610357118, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610357393, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610358074, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610358725, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610359700, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610360279, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610360742, "dur": 3194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610363937, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610364066, "dur": 2516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610366583, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610366767, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610367067, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610367286, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752803610368034, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610368871, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610368934, "dur": 130565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610499504, "dur": 2717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610502222, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610502342, "dur": 6210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610508553, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610508661, "dur": 3722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610512384, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610512469, "dur": 3420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610515889, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610515970, "dur": 3555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610519525, "dur": 1085, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752803610520614, "dur": 4839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752803610525498, "dur": 418708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610256281, "dur": 53706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610309999, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752803610310173, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610310591, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610310861, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610311004, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610311216, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610311323, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610311557, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610311757, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610312052, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610312244, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610312550, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610312697, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610312915, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610313049, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610313356, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610313526, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610313740, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610313871, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610314031, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610314137, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610314241, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610314325, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610314463, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610314547, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752803610314734, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610314839, "dur": 3250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610318090, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610318170, "dur": 21153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610339324, "dur": 509, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610339847, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610340045, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610340169, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610341982, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752803610342049, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610342151, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752803610342207, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610342329, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610344099, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610345316, "dur": 1735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610347051, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610348459, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610349923, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610351341, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610352855, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610354284, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610355582, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610357124, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610357397, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610358042, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610358677, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610360976, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610361104, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610361454, "dur": 1312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610362766, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610362885, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_EF3566CB9F379C74.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610362955, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610363015, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610363414, "dur": 1538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610364952, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610365131, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610365969, "dur": 2109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610368078, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610368223, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_DAC173EE5EC432FE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610368298, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610368362, "dur": 1480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610369843, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752803610369906, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610370299, "dur": 129216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610499517, "dur": 4064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610503582, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610503871, "dur": 5101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610508974, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610509130, "dur": 4144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610513275, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610513360, "dur": 3826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610517186, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610517295, "dur": 5832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752803610523128, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610523256, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610523320, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610523566, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610523699, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610523787, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610524630, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752803610525126, "dur": 419145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610256289, "dur": 53728, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610310018, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610310566, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610310649, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610310852, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610311064, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610311227, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610311374, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610311541, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610311794, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610311934, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610312157, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610312408, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610312691, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610312778, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610313052, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610313237, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610313512, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610313652, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610313826, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610313940, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610314109, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610314183, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610314263, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610314335, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610314467, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610314557, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752803610314773, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610314858, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610314944, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752803610315128, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610315204, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610315330, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610315423, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610315522, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610315637, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752803610315785, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610315888, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316003, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316056, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316144, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316249, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316358, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316453, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316568, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752803610316793, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610316900, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317000, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317085, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317191, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317280, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317393, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317501, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317584, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317711, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317808, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610317960, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610318066, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610319234, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610320861, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610322373, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610323659, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610325294, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610327011, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610328490, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610330213, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610331831, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610333396, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610334995, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610336625, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610338442, "dur": 2135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610340581, "dur": 1925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610342506, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610344197, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610345401, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610347133, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610348544, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610349994, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610351398, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610352918, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610354328, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610355391, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610356819, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610356992, "dur": 86, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610357110, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610357419, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610358043, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610358866, "dur": 5576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610364442, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610364659, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610364723, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610365347, "dur": 3410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610368757, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610368880, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752803610369025, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610369545, "dur": 129958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610499504, "dur": 4258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610503762, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610503907, "dur": 3989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610507896, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610507999, "dur": 4219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610512218, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610512299, "dur": 4741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610517040, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610517128, "dur": 7559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752803610524688, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610524762, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752803610525525, "dur": 418685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610256341, "dur": 53713, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610310057, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610310575, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610310827, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610310978, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610311133, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610311278, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610311548, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610311685, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610311966, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610312138, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610312452, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610312652, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610312813, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610313005, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610313265, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610313461, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610313686, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610313806, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610313978, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610314080, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752803610314307, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610314391, "dur": 3389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610317781, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610317911, "dur": 23056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610340969, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610341203, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610341312, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610341405, "dur": 3933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610345338, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610345431, "dur": 11880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610357373, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610357455, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610357753, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610358041, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610358422, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610358481, "dur": 1612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610360093, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610360498, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610360553, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752803610360787, "dur": 1157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610361944, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610362064, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1752803610364550, "dur": 1251, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610497766, "dur": 422, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610366360, "dur": 131848, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1752803610499492, "dur": 2891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610502384, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610502507, "dur": 5522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610508029, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610508124, "dur": 6384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610514554, "dur": 4072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610518626, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752803610518810, "dur": 6276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752803610525116, "dur": 419102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610256308, "dur": 53723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610310039, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610310585, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610310729, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610310983, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610311113, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610311293, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610311384, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610311609, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610311856, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610312084, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610312296, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610312567, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610312711, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610312927, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610313098, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610313388, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610313581, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610313722, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610313822, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610313992, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610314108, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610314174, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610314266, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610314353, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610314494, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610314592, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752803610314862, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610314973, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752803610315165, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610315302, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610315450, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610315559, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610315690, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610315777, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610315883, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316016, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316121, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316224, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316316, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316424, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316550, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316660, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316743, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316810, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610316924, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317028, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317118, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317225, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317350, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317464, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317544, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317664, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317763, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610317897, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610318003, "dur": 2604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610320607, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610322137, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610323603, "dur": 1665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610325268, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610326991, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610328468, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610330188, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610331774, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610333366, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610334980, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610336608, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610338416, "dur": 2142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610340564, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610342373, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610344157, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610345369, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610347069, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610348481, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610349956, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610351378, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610352889, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610354304, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610355606, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610357110, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610357389, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610358141, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610358485, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610358544, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610359377, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610359649, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610361901, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610362220, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_39AF4F01BD1B7546.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610362351, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610363232, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610363339, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610363813, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610365366, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610365494, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610366546, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610366657, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610367197, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610368481, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610368666, "dur": 1994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610370661, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752803610370748, "dur": 128757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610499513, "dur": 2459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610501973, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610502064, "dur": 4828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610506893, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610507017, "dur": 4637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610511655, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610511726, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610515427, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610515500, "dur": 3907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610519407, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752803610519505, "dur": 5613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752803610525153, "dur": 419070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610256343, "dur": 53723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610310068, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610310569, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610310643, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610310900, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610311039, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610311260, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610311358, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610311567, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610311804, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610312052, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610312221, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610312509, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610312686, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610312849, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610313031, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610313269, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610313505, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610313727, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610313835, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610313992, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610314093, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752803610314267, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610314349, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610314478, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610314587, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610314676, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752803610314734, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610314796, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752803610314947, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315125, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315218, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610315344, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315479, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315601, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315712, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315797, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610315913, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752803610316058, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316165, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316256, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316363, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316462, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316593, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316701, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316791, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316896, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610316995, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317089, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317205, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317307, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317420, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752803610317609, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317732, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317835, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610317985, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610319185, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610320766, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610322282, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610323771, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610325440, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610327100, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610328596, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610330337, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610331901, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610333456, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610335050, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610336708, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610338464, "dur": 2154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610340621, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610342473, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610344211, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610345412, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610347124, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610348510, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610349982, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610351402, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610352895, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610354300, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610355573, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610356979, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610357116, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610357388, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610358054, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610358596, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610359460, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610359842, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610359910, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610360074, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610361707, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610361824, "dur": 1775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610363599, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610363758, "dur": 1387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610365145, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610365303, "dur": 1948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610367251, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610367437, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752803610367769, "dur": 1021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610368790, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610368875, "dur": 130659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610499535, "dur": 5501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610505036, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610505127, "dur": 4502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610509630, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610509751, "dur": 4394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610514145, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610514232, "dur": 5076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610519308, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610519409, "dur": 5573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752803610525045, "dur": 314969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752803610840032, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752803610840016, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752803610840144, "dur": 463, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752803610840611, "dur": 103622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610256345, "dur": 53733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610310080, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610310582, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610310841, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610310997, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610311267, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610311371, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610311596, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610311835, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610312078, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610312279, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610312563, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610312705, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610312925, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610313068, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610313363, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610313541, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610313730, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610313836, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610313992, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314097, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314169, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610314268, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314365, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610314492, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314595, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314691, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752803610314771, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314920, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610314999, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315058, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315198, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610315295, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315412, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315524, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752803610315676, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315761, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315864, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610315987, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316098, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316203, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316301, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316403, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316526, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316642, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316727, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752803610316869, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610316983, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317074, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317193, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317292, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317414, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317522, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317608, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317724, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317818, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610317971, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610319198, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610320798, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610322304, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610323805, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610325476, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610327146, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610328658, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610330414, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610332000, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610333576, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610335216, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610336884, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610338698, "dur": 2165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610340867, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610342732, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610344278, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610345538, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610347249, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610348682, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610350151, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610351620, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610353075, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610354433, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610355077, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610356527, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610356982, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610357124, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610357435, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610358112, "dur": 1340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610359497, "dur": 2721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610362219, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610362650, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610362880, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610362962, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610363671, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610363731, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610365925, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610366207, "dur": 1530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610367737, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610368106, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752803610368164, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610368253, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610368979, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752803610369159, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610369678, "dur": 129844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610499525, "dur": 5870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610505397, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610505540, "dur": 4318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610509859, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610509985, "dur": 5174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610515160, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610515260, "dur": 3218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610518479, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610518545, "dur": 6044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752803610524651, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752803610525157, "dur": 419082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610256346, "dur": 53750, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610310097, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610310573, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610310711, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610310965, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610311094, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610311280, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610311379, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610311592, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610311836, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610312069, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610312233, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610312560, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610312703, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610312922, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610313059, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610313365, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610313530, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610313733, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610313852, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610313998, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610314118, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610314224, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610314289, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610314407, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610314500, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610314661, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610314772, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610314926, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610315020, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752803610315256, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610315359, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752803610315515, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610315648, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610315727, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752803610316028, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316150, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316279, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316388, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316511, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316623, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316731, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316828, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610316948, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317044, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317150, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317245, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317369, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317470, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317560, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317673, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317780, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610317906, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610318028, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610319215, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610320820, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610322332, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610323817, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610325493, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610327152, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610328628, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610330354, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610331911, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610333482, "dur": 1626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610335108, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610336787, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610338616, "dur": 2139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610340758, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610342651, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610344248, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610345466, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610347190, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610348594, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610350069, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610351474, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610352999, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610354367, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610355001, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610356446, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610356989, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610357127, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610357400, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610358075, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610358809, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610359989, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610360123, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_AEDA0D01FA0B3E02.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610360194, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610360308, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610360657, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610360722, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610361274, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610361357, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610361670, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610362810, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610363053, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610363225, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610363310, "dur": 1202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610364512, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610364694, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_5A15FB97D284204D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610364793, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610364862, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610365498, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610365553, "dur": 3475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610369028, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610369094, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610369226, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610369841, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610369903, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610370435, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610370659, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752803610370725, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610371176, "dur": 299510, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610671827, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752803610671561, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610671997, "dur": 158290, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610831269, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752803610831110, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610833177, "dur": 248, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610940326, "dur": 354, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752803610833439, "dur": 107252, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752803610943755, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752803610943748, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752803610943828, "dur": 354, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752803610256345, "dur": 53740, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610310087, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610310565, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610310799, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610310935, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610311140, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610311293, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610311450, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610311605, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610311890, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610312100, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610312373, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610312599, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610312768, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610312963, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610313223, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610313403, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610313665, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610313747, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610313920, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610314037, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752803610314216, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610314285, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610314399, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610314480, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610314658, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610314772, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610314867, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752803610315053, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752803610315401, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610315498, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752803610315653, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610315730, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610315819, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610315950, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316041, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316175, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316267, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316380, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316492, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316604, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316710, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316802, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610316903, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317010, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317102, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317211, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317336, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317435, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317535, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317644, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317744, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317859, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610317995, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610319178, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610320755, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610322276, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610323759, "dur": 1702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610325461, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610327119, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610328615, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610330332, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610331954, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610333510, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610335127, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610336795, "dur": 1790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610338585, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610340668, "dur": 1922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610342590, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610344233, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610345433, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610347139, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610348555, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610350019, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610351445, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610352942, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610354342, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610354965, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610356407, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610357111, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610357399, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610358064, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610358342, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610358927, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610358987, "dur": 1042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610360029, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610360272, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_C0EF7B73E8141BD2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610360331, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610360385, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610360638, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610361237, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610361393, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610363172, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610363382, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610363632, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610366255, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610366442, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610366804, "dur": 2066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610368870, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610368975, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752803610369147, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610369626, "dur": 129894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610499522, "dur": 4236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610503759, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610503836, "dur": 6015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610509898, "dur": 4612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610514510, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610514600, "dur": 7271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752803610521871, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610521990, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610522192, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752803610522248, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610522406, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610522691, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610522765, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610522822, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610523014, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610523130, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610523265, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610523398, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610523590, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752803610523643, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610523855, "dur": 1167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610525067, "dur": 418682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752803610943757, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752803610943750, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752803610943848, "dur": 319, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752803610946848, "dur": 752, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 1937, "ts": 1752803610950045, "dur": 42849, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 1937, "ts": 1752803610992977, "dur": 543, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 1937, "ts": 1752803610948874, "dur": 44699, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}