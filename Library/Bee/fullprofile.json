{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 2659, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 2659, "ts": 1752910034540444, "dur": 3051, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034551156, "dur": 965, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033434117, "dur": 198, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033434317, "dur": 1085438, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033434381, "dur": 55, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033434457, "dur": 108338, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033542823, "dur": 26, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033542851, "dur": 868, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543734, "dur": 3, "ph": "X", "name": "ProcessMessages 2149", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543737, "dur": 75, "ph": "X", "name": "ReadAsync 2149", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543816, "dur": 2, "ph": "X", "name": "ProcessMessages 2981", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543819, "dur": 49, "ph": "X", "name": "ReadAsync 2981", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543876, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543878, "dur": 45, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543925, "dur": 1, "ph": "X", "name": "ProcessMessages 1834", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033543927, "dur": 46, "ph": "X", "name": "ReadAsync 1834", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544042, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544046, "dur": 60, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544107, "dur": 2, "ph": "X", "name": "ProcessMessages 3859", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544110, "dur": 49, "ph": "X", "name": "ReadAsync 3859", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544164, "dur": 1, "ph": "X", "name": "ProcessMessages 1542", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544166, "dur": 58, "ph": "X", "name": "ReadAsync 1542", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544225, "dur": 1, "ph": "X", "name": "ProcessMessages 1928", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544227, "dur": 55, "ph": "X", "name": "ReadAsync 1928", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544285, "dur": 66, "ph": "X", "name": "ReadAsync 1857", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544353, "dur": 2, "ph": "X", "name": "ProcessMessages 2278", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544356, "dur": 49, "ph": "X", "name": "ReadAsync 2278", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544407, "dur": 1, "ph": "X", "name": "ProcessMessages 1451", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544416, "dur": 37, "ph": "X", "name": "ReadAsync 1451", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544455, "dur": 47, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544504, "dur": 2, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544507, "dur": 38, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544549, "dur": 56, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544607, "dur": 1, "ph": "X", "name": "ProcessMessages 1885", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544610, "dur": 44, "ph": "X", "name": "ReadAsync 1885", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544656, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544658, "dur": 44, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544704, "dur": 1, "ph": "X", "name": "ProcessMessages 1606", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544705, "dur": 48, "ph": "X", "name": "ReadAsync 1606", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544754, "dur": 1, "ph": "X", "name": "ProcessMessages 1629", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544756, "dur": 46, "ph": "X", "name": "ReadAsync 1629", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544804, "dur": 1, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544806, "dur": 36, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544852, "dur": 49, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544903, "dur": 1, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544905, "dur": 43, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544950, "dur": 1, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544952, "dur": 42, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544996, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033544997, "dur": 160, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545160, "dur": 1, "ph": "X", "name": "ProcessMessages 2082", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545163, "dur": 43, "ph": "X", "name": "ReadAsync 2082", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545208, "dur": 1, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545218, "dur": 38, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545257, "dur": 1, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545259, "dur": 43, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545304, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545306, "dur": 51, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545359, "dur": 1, "ph": "X", "name": "ProcessMessages 1281", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545361, "dur": 33, "ph": "X", "name": "ReadAsync 1281", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545398, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545400, "dur": 44, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545445, "dur": 1, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545447, "dur": 35, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545483, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545486, "dur": 51, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545539, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545541, "dur": 34, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545578, "dur": 47, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545626, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545629, "dur": 46, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545676, "dur": 1, "ph": "X", "name": "ProcessMessages 1419", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545678, "dur": 72, "ph": "X", "name": "ReadAsync 1419", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545752, "dur": 1, "ph": "X", "name": "ProcessMessages 1553", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545755, "dur": 46, "ph": "X", "name": "ReadAsync 1553", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545802, "dur": 1, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545804, "dur": 44, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545850, "dur": 1, "ph": "X", "name": "ProcessMessages 1261", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545852, "dur": 44, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545899, "dur": 48, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545948, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545950, "dur": 39, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033545992, "dur": 39, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546033, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546034, "dur": 39, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546082, "dur": 41, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546124, "dur": 1, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546126, "dur": 45, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546174, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546176, "dur": 45, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546224, "dur": 46, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546271, "dur": 1, "ph": "X", "name": "ProcessMessages 1314", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546272, "dur": 34, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546309, "dur": 41, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546352, "dur": 28, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546383, "dur": 39, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546424, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546426, "dur": 41, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546468, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546470, "dur": 37, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546509, "dur": 44, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546556, "dur": 45, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546603, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546604, "dur": 36, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546641, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546644, "dur": 38, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546685, "dur": 35, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546723, "dur": 39, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546764, "dur": 1, "ph": "X", "name": "ProcessMessages 1043", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546766, "dur": 38, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546805, "dur": 4, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546811, "dur": 37, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546850, "dur": 43, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546895, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546896, "dur": 53, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546951, "dur": 1, "ph": "X", "name": "ProcessMessages 1804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546953, "dur": 43, "ph": "X", "name": "ReadAsync 1804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033546998, "dur": 1, "ph": "X", "name": "ProcessMessages 1142", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547000, "dur": 36, "ph": "X", "name": "ReadAsync 1142", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547038, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547040, "dur": 36, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547078, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547080, "dur": 42, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547124, "dur": 5, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547129, "dur": 31, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547163, "dur": 83, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547247, "dur": 1, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547248, "dur": 34, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547285, "dur": 36, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547324, "dur": 37, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547363, "dur": 49, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547413, "dur": 1, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547415, "dur": 42, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547478, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547480, "dur": 33, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547516, "dur": 30, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547548, "dur": 41, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547591, "dur": 14, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547606, "dur": 51, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547659, "dur": 1, "ph": "X", "name": "ProcessMessages 1899", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547661, "dur": 37, "ph": "X", "name": "ReadAsync 1899", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547699, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547701, "dur": 41, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547744, "dur": 1, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547746, "dur": 36, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547785, "dur": 35, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547822, "dur": 32, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547857, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547859, "dur": 43, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547903, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547904, "dur": 30, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547936, "dur": 28, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547967, "dur": 27, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033547996, "dur": 35, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548033, "dur": 1, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548035, "dur": 49, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548085, "dur": 1, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548086, "dur": 29, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548117, "dur": 31, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548151, "dur": 64, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548217, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548219, "dur": 35, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548255, "dur": 1, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548257, "dur": 67, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548327, "dur": 1, "ph": "X", "name": "ProcessMessages 1217", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548328, "dur": 37, "ph": "X", "name": "ReadAsync 1217", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548367, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548369, "dur": 36, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548407, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548408, "dur": 33, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548443, "dur": 32, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548477, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548479, "dur": 40, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548522, "dur": 37, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548561, "dur": 40, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548602, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548604, "dur": 33, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548640, "dur": 37, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548685, "dur": 67, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548754, "dur": 1, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548757, "dur": 38, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548796, "dur": 1, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548798, "dur": 32, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548831, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548833, "dur": 34, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548870, "dur": 38, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548909, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548910, "dur": 40, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548952, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033548953, "dur": 49, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549005, "dur": 1, "ph": "X", "name": "ProcessMessages 1385", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549007, "dur": 42, "ph": "X", "name": "ReadAsync 1385", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549051, "dur": 34, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549088, "dur": 38, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549129, "dur": 34, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549165, "dur": 28, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549195, "dur": 29, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549226, "dur": 37, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549265, "dur": 25, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549292, "dur": 25, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549318, "dur": 36, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549357, "dur": 32, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549397, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549399, "dur": 39, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549439, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549477, "dur": 41, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549518, "dur": 1, "ph": "X", "name": "ProcessMessages 1922", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549520, "dur": 40, "ph": "X", "name": "ReadAsync 1922", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549561, "dur": 1, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549564, "dur": 38, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549603, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549605, "dur": 38, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549645, "dur": 35, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549683, "dur": 44, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549729, "dur": 72, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549802, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549804, "dur": 98, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549905, "dur": 20, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033549927, "dur": 99, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550038, "dur": 52, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550091, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550093, "dur": 22, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550117, "dur": 114, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550233, "dur": 1, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550235, "dur": 111, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550348, "dur": 2, "ph": "X", "name": "ProcessMessages 1536", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550351, "dur": 105, "ph": "X", "name": "ReadAsync 1536", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550464, "dur": 6, "ph": "X", "name": "ProcessMessages 1452", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550471, "dur": 34, "ph": "X", "name": "ReadAsync 1452", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550507, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550509, "dur": 39, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550558, "dur": 34, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550594, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550596, "dur": 45, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550642, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550643, "dur": 35, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550681, "dur": 62, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550745, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550747, "dur": 25, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550774, "dur": 69, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550845, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550846, "dur": 51, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550899, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550901, "dur": 43, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033550947, "dur": 67, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551015, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551017, "dur": 54, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551078, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551081, "dur": 40, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551124, "dur": 44, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551169, "dur": 1, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551171, "dur": 36, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551210, "dur": 49, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551260, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551262, "dur": 38, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551302, "dur": 93, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551397, "dur": 1, "ph": "X", "name": "ProcessMessages 1300", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551399, "dur": 32, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551432, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551439, "dur": 53, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551494, "dur": 61, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551557, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551559, "dur": 57, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551617, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551619, "dur": 49, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551670, "dur": 1, "ph": "X", "name": "ProcessMessages 1233", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551672, "dur": 46, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551719, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551721, "dur": 82, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551804, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551806, "dur": 40, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551849, "dur": 79, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551930, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551932, "dur": 30, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033551965, "dur": 46, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552014, "dur": 37, "ph": "X", "name": "ReadAsync 1188", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552053, "dur": 1, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552055, "dur": 44, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552102, "dur": 51, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552154, "dur": 1, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552156, "dur": 31, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552189, "dur": 34, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552226, "dur": 50, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552285, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552287, "dur": 20, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552309, "dur": 4, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552314, "dur": 36, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552353, "dur": 44, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552407, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552409, "dur": 25, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552436, "dur": 68, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552507, "dur": 42, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552551, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552553, "dur": 72, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552626, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552628, "dur": 60, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552689, "dur": 3, "ph": "X", "name": "ProcessMessages 1040", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552693, "dur": 26, "ph": "X", "name": "ReadAsync 1040", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552722, "dur": 22, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552747, "dur": 39, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552788, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552790, "dur": 35, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552827, "dur": 59, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552888, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552890, "dur": 44, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552935, "dur": 1, "ph": "X", "name": "ProcessMessages 1444", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552937, "dur": 27, "ph": "X", "name": "ReadAsync 1444", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033552966, "dur": 130, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553099, "dur": 130, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553231, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553234, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553478, "dur": 103, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553590, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553622, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553623, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553807, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033553852, "dur": 155, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554010, "dur": 80, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554094, "dur": 201, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554298, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554341, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554405, "dur": 269, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554678, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554746, "dur": 114, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033554862, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555050, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555052, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555092, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555146, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555152, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555321, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555323, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555399, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555445, "dur": 153, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555601, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555713, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555755, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555899, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555941, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033555976, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556031, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556066, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556068, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556102, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556223, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556270, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556306, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556352, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556399, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556439, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556539, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556604, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556606, "dur": 42, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556651, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556739, "dur": 202, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556943, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033556976, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557011, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557053, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557136, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557222, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557269, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557271, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557315, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557369, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557371, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557408, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557441, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557522, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557524, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557562, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557568, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557607, "dur": 110, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557720, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557815, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557918, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033557959, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558012, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558063, "dur": 54, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558120, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558183, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558186, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558231, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558262, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558297, "dur": 128, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558426, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558427, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558482, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558486, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558539, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558583, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558618, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558670, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558701, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558756, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558790, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558870, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558872, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033558924, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559012, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559014, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559079, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559118, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559151, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559205, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559207, "dur": 51, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559260, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559262, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559304, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559345, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559376, "dur": 82, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559460, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559462, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559538, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559540, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559583, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559615, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559663, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559700, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559794, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559839, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559885, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559927, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033559972, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560021, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560071, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560124, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560184, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560226, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560228, "dur": 41, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560271, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560311, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560352, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560405, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560440, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560491, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560560, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560618, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560668, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560707, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560712, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560751, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560783, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560785, "dur": 33, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560821, "dur": 6, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560828, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033560877, "dur": 741, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033561619, "dur": 48, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033561668, "dur": 356, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033562027, "dur": 630, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033562661, "dur": 23007, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033585674, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033585676, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033585746, "dur": 936, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033586684, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033586870, "dur": 418, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033587291, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033587441, "dur": 3218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033590662, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033590664, "dur": 10727, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033601413, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033601415, "dur": 435, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033601854, "dur": 315, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033602173, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033602175, "dur": 214, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033602391, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033602534, "dur": 489, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033603026, "dur": 334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033603363, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033603516, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033603580, "dur": 196, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033603779, "dur": 199, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033603980, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033604062, "dur": 224, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033604289, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033604468, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033604470, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033604503, "dur": 996, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033605500, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033605502, "dur": 146, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033605650, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033605652, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033605942, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033606078, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033606192, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033606395, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033606478, "dur": 272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033606753, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033606946, "dur": 214, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607162, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607164, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607313, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607461, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607651, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607770, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033607911, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033608026, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033608102, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033608255, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033608523, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033608571, "dur": 394, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033608967, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033609126, "dur": 382, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033609510, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033609756, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033609916, "dur": 383, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033610302, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033610427, "dur": 305, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033610740, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033610970, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033611124, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033611285, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033611362, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033611427, "dur": 334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033611764, "dur": 272, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612038, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612167, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612243, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612295, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612524, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612652, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612773, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612877, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612879, "dur": 83, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033612965, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613008, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613114, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613217, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613299, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613492, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613664, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613668, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613780, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033613782, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614046, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614195, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614337, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614456, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614579, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614625, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033614725, "dur": 281, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615008, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615052, "dur": 300, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615355, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615419, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615480, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615709, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615736, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033615758, "dur": 343, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033616104, "dur": 119454, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033735566, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033735568, "dur": 116, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033735830, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033735832, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033735913, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033735958, "dur": 62, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033736024, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033736067, "dur": 30, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033736100, "dur": 46, "ph": "X", "name": "ReadAsync 1742", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033736147, "dur": 25, "ph": "X", "name": "ProcessMessages 6", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033736173, "dur": 5013, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033741196, "dur": 21, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033741250, "dur": 642, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033741897, "dur": 1863, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033743764, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033743945, "dur": 411, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033744370, "dur": 716, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033745089, "dur": 401, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033745493, "dur": 1188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033746684, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033746686, "dur": 1931, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033748620, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033748828, "dur": 406, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033749237, "dur": 366, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033749605, "dur": 620, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033750229, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033750497, "dur": 1850, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033752351, "dur": 1020, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033753374, "dur": 514, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033753891, "dur": 1230, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033755124, "dur": 1517, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033756644, "dur": 618, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033757266, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033757345, "dur": 587, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033757935, "dur": 462, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033758410, "dur": 364, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033758777, "dur": 1243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033760023, "dur": 772, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033760799, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033760861, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033760989, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761102, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761188, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761236, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761296, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761489, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761599, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761655, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761714, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761880, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033761943, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762097, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762146, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762235, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762292, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762328, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762440, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762441, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762493, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762621, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762748, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762843, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033762962, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763044, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763136, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763235, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763338, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763370, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763415, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763499, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763536, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763574, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763695, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763770, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763803, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763881, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033763968, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764016, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764083, "dur": 114, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764206, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764211, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764421, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764538, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764613, "dur": 336, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033764954, "dur": 544, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033765500, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033765502, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910033765545, "dur": 383086, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034148637, "dur": 32, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034148671, "dur": 1388, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034150064, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034150067, "dur": 1477, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034151548, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034151550, "dur": 135799, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287357, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287360, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287444, "dur": 45, "ph": "X", "name": "ReadAsync 5586", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287491, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287526, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287576, "dur": 39, "ph": "X", "name": "ProcessMessages 4659", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034287616, "dur": 3256, "ph": "X", "name": "ReadAsync 4659", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034290875, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034290877, "dur": 317, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034291210, "dur": 30, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034291241, "dur": 112161, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034403408, "dur": 30, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034403439, "dur": 2557, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034406002, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034406005, "dur": 105771, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034511785, "dur": 12, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034511802, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034511853, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034511906, "dur": 86, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034511996, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034512045, "dur": 39, "ph": "X", "name": "ProcessMessages 6644", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034512085, "dur": 2999, "ph": "X", "name": "ReadAsync 6644", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034515086, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034515089, "dur": 422, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034515515, "dur": 50, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034515567, "dur": 61, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034515631, "dur": 229, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752910034515863, "dur": 3884, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034552126, "dur": 1567, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 17179869184, "ts": 1752910033404493, "dur": 2190, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 17179869184, "ts": 1752910033406683, "dur": 6794, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 17179869184, "ts": 1752910033413479, "dur": 162, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034553701, "dur": 19, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": **********336478, "dur": 16833, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": **********353317, "dur": 76949, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": **********430274, "dur": 116467, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034553721, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 12884901888, "ts": **********333465, "dur": 1042, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********334509, "dur": 4194857, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********337294, "dur": 15154, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********352579, "dur": 1715, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354298, "dur": 58, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354358, "dur": 259, "ph": "X", "name": "ProcessMessages 8160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354621, "dur": 64, "ph": "X", "name": "ReadAsync 8160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354688, "dur": 3, "ph": "X", "name": "ProcessMessages 7016", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354692, "dur": 26, "ph": "X", "name": "ReadAsync 7016", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354720, "dur": 1, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354721, "dur": 52, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354776, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354778, "dur": 51, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354831, "dur": 1, "ph": "X", "name": "ProcessMessages 1746", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354833, "dur": 41, "ph": "X", "name": "ReadAsync 1746", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354876, "dur": 1, "ph": "X", "name": "ProcessMessages 1644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354877, "dur": 34, "ph": "X", "name": "ReadAsync 1644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354913, "dur": 1, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354914, "dur": 54, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354970, "dur": 1, "ph": "X", "name": "ProcessMessages 1609", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354971, "dur": 23, "ph": "X", "name": "ReadAsync 1609", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********354996, "dur": 2053, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357051, "dur": 3, "ph": "X", "name": "ProcessMessages 8129", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357055, "dur": 55, "ph": "X", "name": "ReadAsync 8129", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357114, "dur": 2, "ph": "X", "name": "ProcessMessages 1336", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357117, "dur": 66, "ph": "X", "name": "ReadAsync 1336", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357186, "dur": 2, "ph": "X", "name": "ProcessMessages 2200", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357189, "dur": 52, "ph": "X", "name": "ReadAsync 2200", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********357251, "dur": 1000, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********358253, "dur": 1, "ph": "X", "name": "ProcessMessages 1673", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********358255, "dur": 1643, "ph": "X", "name": "ReadAsync 1673", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********359900, "dur": 3, "ph": "X", "name": "ProcessMessages 7309", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********359904, "dur": 203, "ph": "X", "name": "ReadAsync 7309", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360108, "dur": 1, "ph": "X", "name": "ProcessMessages 1582", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360110, "dur": 44, "ph": "X", "name": "ReadAsync 1582", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360159, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360161, "dur": 186, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360348, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360350, "dur": 18, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360369, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360371, "dur": 35, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360409, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360410, "dur": 92, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360504, "dur": 1, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360506, "dur": 32, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360563, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360565, "dur": 79, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360646, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360648, "dur": 33, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360684, "dur": 36, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360722, "dur": 91, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360814, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360815, "dur": 25, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360843, "dur": 36, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360881, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********360883, "dur": 187, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361072, "dur": 1, "ph": "X", "name": "ProcessMessages 1453", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361074, "dur": 30, "ph": "X", "name": "ReadAsync 1453", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361106, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361108, "dur": 244, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361353, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361355, "dur": 42, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361399, "dur": 51, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361452, "dur": 177, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361631, "dur": 28, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361661, "dur": 18, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361681, "dur": 33, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361716, "dur": 20, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361738, "dur": 35, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361776, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361777, "dur": 38, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361828, "dur": 1, "ph": "X", "name": "ProcessMessages 1095", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361830, "dur": 44, "ph": "X", "name": "ReadAsync 1095", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361876, "dur": 1, "ph": "X", "name": "ProcessMessages 1203", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361878, "dur": 36, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361915, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********361917, "dur": 333, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362252, "dur": 164, "ph": "X", "name": "ProcessMessages 1293", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362418, "dur": 38, "ph": "X", "name": "ReadAsync 1293", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362458, "dur": 2, "ph": "X", "name": "ProcessMessages 2809", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362470, "dur": 32, "ph": "X", "name": "ReadAsync 2809", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362504, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362506, "dur": 189, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362696, "dur": 1, "ph": "X", "name": "ProcessMessages 2796", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362699, "dur": 93, "ph": "X", "name": "ReadAsync 2796", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362793, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362794, "dur": 31, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362827, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362829, "dur": 39, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362871, "dur": 99, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362972, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********362974, "dur": 38, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363014, "dur": 206, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363222, "dur": 2, "ph": "X", "name": "ProcessMessages 2315", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363224, "dur": 109, "ph": "X", "name": "ReadAsync 2315", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363334, "dur": 2, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363337, "dur": 42, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363381, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363382, "dur": 164, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363548, "dur": 1, "ph": "X", "name": "ProcessMessages 1712", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363550, "dur": 34, "ph": "X", "name": "ReadAsync 1712", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363586, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363588, "dur": 199, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363788, "dur": 1, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363790, "dur": 36, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363829, "dur": 42, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363873, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363894, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363916, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363938, "dur": 25, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363965, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363968, "dur": 21, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********363991, "dur": 225, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********364218, "dur": 1, "ph": "X", "name": "ProcessMessages 1652", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********364220, "dur": 354, "ph": "X", "name": "ReadAsync 1652", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********364576, "dur": 2, "ph": "X", "name": "ProcessMessages 3479", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********364579, "dur": 428, "ph": "X", "name": "ReadAsync 3479", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365009, "dur": 3, "ph": "X", "name": "ProcessMessages 6513", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365013, "dur": 50, "ph": "X", "name": "ReadAsync 6513", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365391, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365392, "dur": 54, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365448, "dur": 3, "ph": "X", "name": "ProcessMessages 4844", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365452, "dur": 23, "ph": "X", "name": "ReadAsync 4844", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365478, "dur": 32, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365512, "dur": 21, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365536, "dur": 24, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365577, "dur": 30, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365608, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365611, "dur": 31, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365644, "dur": 30, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********365677, "dur": 605, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366436, "dur": 4, "ph": "X", "name": "ProcessMessages 6566", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366441, "dur": 80, "ph": "X", "name": "ReadAsync 6566", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366548, "dur": 16, "ph": "X", "name": "ProcessMessages 7197", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366565, "dur": 124, "ph": "X", "name": "ReadAsync 7197", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366692, "dur": 21, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366716, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366739, "dur": 30, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********366772, "dur": 380, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********367153, "dur": 2, "ph": "X", "name": "ProcessMessages 4486", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********367156, "dur": 167, "ph": "X", "name": "ReadAsync 4486", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********367325, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********367326, "dur": 27, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********367356, "dur": 27, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********367385, "dur": 301, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********370043, "dur": 179, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********370226, "dur": 23, "ph": "X", "name": "ProcessMessages 6837", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********370252, "dur": 798, "ph": "X", "name": "ReadAsync 6837", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********371052, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********371054, "dur": 167, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********371223, "dur": 450, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********371676, "dur": 170, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********372037, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********372038, "dur": 40, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********372080, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********372082, "dur": 200, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********372284, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********372286, "dur": 1092, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********373379, "dur": 1, "ph": "X", "name": "ProcessMessages 1790", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********373382, "dur": 755, "ph": "X", "name": "ReadAsync 1790", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********374138, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********374140, "dur": 142, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********374285, "dur": 505, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********374793, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********374795, "dur": 811, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********375608, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********375610, "dur": 134, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********375764, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********375766, "dur": 739, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********376508, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********376512, "dur": 768, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********377282, "dur": 2, "ph": "X", "name": "ProcessMessages 1168", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********377285, "dur": 9807, "ph": "X", "name": "ReadAsync 1168", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********387104, "dur": 13, "ph": "X", "name": "ProcessMessages 6001", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********387120, "dur": 376, "ph": "X", "name": "ReadAsync 6001", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********387499, "dur": 13, "ph": "X", "name": "ProcessMessages 7716", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********387514, "dur": 107, "ph": "X", "name": "ReadAsync 7716", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********387622, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********387624, "dur": 794, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********388420, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********388422, "dur": 52708, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********441134, "dur": 6, "ph": "X", "name": "ProcessMessages 8187", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********441142, "dur": 63, "ph": "X", "name": "ReadAsync 8187", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********441207, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********441210, "dur": 1717, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********442930, "dur": 2, "ph": "X", "name": "ProcessMessages 2735", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********442934, "dur": 457, "ph": "X", "name": "ReadAsync 2735", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********443392, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********443394, "dur": 52, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********443448, "dur": 535, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********443985, "dur": 417, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********444406, "dur": 550, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********445135, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********445138, "dur": 139, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********445279, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********445281, "dur": 360, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********445644, "dur": 356, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********446002, "dur": 347, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********446359, "dur": 304, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********446666, "dur": 366, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********447034, "dur": 268, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********447304, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********447306, "dur": 777, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448085, "dur": 309, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448395, "dur": 111, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448509, "dur": 173, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448685, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448686, "dur": 24, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448712, "dur": 23, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448737, "dur": 12, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448750, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********448768, "dur": 571, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********453913, "dur": 2, "ph": "X", "name": "ProcessMessages 1240", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********453917, "dur": 261, "ph": "X", "name": "ReadAsync 1240", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********454179, "dur": 214, "ph": "X", "name": "ProcessMessages 2267", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********454795, "dur": 2595, "ph": "X", "name": "ReadAsync 2267", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********457420, "dur": 5, "ph": "X", "name": "ProcessMessages 1456", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********457426, "dur": 477, "ph": "X", "name": "ReadAsync 1456", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********457909, "dur": 48, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********457960, "dur": 65, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********458032, "dur": 1970, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460006, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460009, "dur": 164, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460176, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460178, "dur": 57, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460237, "dur": 20, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460260, "dur": 95, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460358, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460464, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460506, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460586, "dur": 213, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460800, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460803, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460841, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460914, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460986, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********460987, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461036, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461083, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461184, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461186, "dur": 39, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461228, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461362, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461415, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461471, "dur": 13, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461485, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461591, "dur": 127, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461881, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461884, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461966, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********461967, "dur": 99, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462067, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462069, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462119, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462164, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462215, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462271, "dur": 42, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462324, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462442, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462506, "dur": 84, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462592, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462638, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462692, "dur": 172, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********462866, "dur": 179, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463048, "dur": 290, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463340, "dur": 120, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463463, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463511, "dur": 227, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463752, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463755, "dur": 120, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463877, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********463985, "dur": 12, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********464000, "dur": 159, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********464160, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********464162, "dur": 102, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********464267, "dur": 251, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********464521, "dur": 16208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********480735, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********480742, "dur": 272, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********481021, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********481023, "dur": 667, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********535006, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********535010, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********535137, "dur": 500, "ph": "X", "name": "ProcessMessages 2488", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********535639, "dur": 101373, "ph": "X", "name": "ReadAsync 2488", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637019, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637021, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637048, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637109, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637134, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637188, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637211, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********637270, "dur": 5865, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": **********643138, "dur": 3864767, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752910032507994, "dur": 526, "ph": "X", "name": "ProcessMessages 2000", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752910032508524, "dur": 6106, "ph": "X", "name": "ReadAsync 2000", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752910032514638, "dur": 3060, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752910032517704, "dur": 10259, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034553733, "dur": 466, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 8589934592, "ts": **********328914, "dur": 217843, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": **********546760, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": **********546770, "dur": 4843, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034554201, "dur": 26, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 4294967296, "ts": **********196578, "dur": 4335175, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": **********216562, "dur": 52034, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752910032568164, "dur": 803328, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752910033371677, "dur": 1148423, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752910033391294, "dur": 12982, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752910034520115, "dur": 5314, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752910034523518, "dur": 362, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752910034525432, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034554229, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752910033413391, "dur": 129949, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910033543353, "dur": 135, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910033543547, "dur": 119, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910033543673, "dur": 9371, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910033553052, "dur": 962560, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910034515620, "dur": 62, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910034515774, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910034515854, "dur": 469, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752910033543596, "dur": 9479, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033553081, "dur": 2407, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033555488, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033555658, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033555764, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033556013, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033556154, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033556251, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033556363, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033556548, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033556664, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033556857, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033557007, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033557206, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033557413, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033557586, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033557816, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033557978, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558117, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558258, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558402, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558511, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558610, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558744, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033558908, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559048, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559167, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559285, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559419, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559546, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559727, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559882, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033559997, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033560171, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033560296, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033560420, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033560568, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033560686, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033560790, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033562352, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033564263, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033565885, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033567400, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033568894, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033570519, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033572085, "dur": 2929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033575015, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033576727, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033578369, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033580122, "dur": 1687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033581809, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033583121, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033584803, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033586198, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033587785, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033589185, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033590591, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033592060, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033593613, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033595137, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033596498, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033598029, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033599354, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033600754, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033601069, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033601399, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033601807, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033602219, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033602497, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033604302, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033604493, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033605117, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033606495, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033606652, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033606917, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033607225, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033609128, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033609256, "dur": 3954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033613210, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033613468, "dur": 2254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033615723, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752910033615800, "dur": 124205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033740006, "dur": 6561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033746569, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033746743, "dur": 5639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033752382, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033752469, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033755127, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033755240, "dur": 4753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033759993, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752910033760102, "dur": 5381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752910033765544, "dur": 750091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033543600, "dur": 9495, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033553112, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033553178, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033553357, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033553486, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033553761, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033553843, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033553980, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033554078, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033554246, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033554425, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033554650, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033554844, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033555000, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033555145, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033555314, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033555452, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033555612, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033555743, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033555964, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033556130, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033556231, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033556334, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033556512, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033556622, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033556846, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033556971, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033557180, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033557344, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033557486, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033557642, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033557807, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033557957, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558108, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558251, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558394, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558496, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558584, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558721, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033558878, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559005, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559141, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559254, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559380, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559503, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559616, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559812, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033559934, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560060, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560239, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560368, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560487, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560640, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560765, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033560862, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033562476, "dur": 1907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033564383, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033565975, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033567498, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033569020, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033570686, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033572204, "dur": 3077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033575281, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033576889, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033578498, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033580299, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033581955, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033583338, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033584978, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033586347, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033587949, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033589348, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033590815, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033592207, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033593807, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033595287, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033596649, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033598184, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033598791, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033600215, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033600729, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033601008, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033601067, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033601349, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033601797, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033602344, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033603290, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033603493, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033603598, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033604708, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033604780, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033605095, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033607443, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033607635, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033608149, "dur": 1552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033609701, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033609869, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033610169, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033612700, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033613026, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_C99541089A8295F6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033613135, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752910033613544, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033614521, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033614624, "dur": 122769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033737396, "dur": 4438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033741835, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033741972, "dur": 3475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033745447, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033745561, "dur": 4944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033750506, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033750606, "dur": 3380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033753986, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033754110, "dur": 4249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033758359, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910033758480, "dur": 6439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752910033764989, "dur": 525731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752910034290734, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752910034290722, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752910034290853, "dur": 382, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752910034291237, "dur": 224429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033543612, "dur": 9518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033553144, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033553286, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033553449, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033553752, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033553831, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033553998, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033554159, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033554268, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033554503, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033554632, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033554878, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033555019, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033555249, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033555356, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033555543, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033555670, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033555879, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033556055, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033556200, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033556292, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033556402, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033556575, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033556728, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033556889, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557088, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557285, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557388, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557515, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557659, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557814, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033557942, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558063, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558197, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558337, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558484, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558557, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558667, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558765, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033558923, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559076, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559191, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559293, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559413, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559537, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559707, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559866, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033559994, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033560151, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033560262, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033560384, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033560535, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033560672, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033560782, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033562370, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033564273, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033565896, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033567411, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033568897, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033570527, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033572100, "dur": 2963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033575064, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033576748, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033578373, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033580138, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033581818, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033583185, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033584862, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033586204, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033587821, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033589211, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033590616, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033592081, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033593633, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033595140, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033596494, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033598026, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033599303, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033600707, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033601075, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033601350, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033601764, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033601995, "dur": 1934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033603929, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033604054, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033604111, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033604739, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033605653, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033605817, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033606207, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033608056, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033608252, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033608581, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033609925, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033610060, "dur": 1947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033612007, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033612159, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033612240, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752910033612743, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033612854, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033614111, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033614218, "dur": 123180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033737399, "dur": 3773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033741173, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033741292, "dur": 3770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033745062, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033745170, "dur": 4438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033749609, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033749726, "dur": 7184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033756910, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033757010, "dur": 5696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910033762707, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033762931, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763043, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763118, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752910033763177, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763273, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763435, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763563, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763706, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763883, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033763990, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033764055, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033764116, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910033764607, "dur": 384911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910034149538, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752910034149519, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910034151315, "dur": 205, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910034287265, "dur": 335, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752910034151538, "dur": 136092, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752910034290719, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752910034290709, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752910034290915, "dur": 337, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752910034291253, "dur": 224361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033543614, "dur": 9568, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033553198, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033553368, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033553510, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033553774, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033553859, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033553983, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033554087, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033554252, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033554450, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033554638, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033554833, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033554994, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033555112, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033555304, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033555432, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033555597, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033555713, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033555917, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033556086, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033556212, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033556310, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033556487, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033556606, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033556821, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033556934, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033557165, "dur": 5405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033562570, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033562674, "dur": 22703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033585377, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033585571, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033585663, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033585752, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033587310, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033587423, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033588843, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033590258, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033591684, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033593142, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033594775, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033596134, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033597602, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033598976, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033600451, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033600861, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033601073, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033601351, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033601794, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033602181, "dur": 5423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033607604, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033607765, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033608323, "dur": 4646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033612969, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033613389, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752910033613721, "dur": 982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033614743, "dur": 124418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033739162, "dur": 4312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033743474, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033743578, "dur": 5051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033748630, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033748757, "dur": 5107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033753865, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033753942, "dur": 4762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033758706, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033758903, "dur": 5550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752910033764453, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033764588, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752910033765553, "dur": 750101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033543626, "dur": 9589, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033553220, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033553388, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033553527, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033553775, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033553866, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033553980, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033554083, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033554250, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033554428, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033554609, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033554772, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033554948, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033555077, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033555285, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033555428, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033555593, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033555719, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033555941, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033556109, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033556220, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033556309, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033556457, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033556620, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033556781, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033556925, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033557133, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033557323, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033557448, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033557613, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033557816, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033557965, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558053, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558173, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558321, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558454, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558568, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558682, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558806, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033558956, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559100, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559229, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559338, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559486, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559590, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559778, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033559920, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560036, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560216, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560338, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560462, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560614, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560737, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033560849, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033562505, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033564419, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033566003, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033567448, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033568959, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033570607, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033572166, "dur": 3074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033575240, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033576842, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033578488, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033580275, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033581933, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033583314, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033584951, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033586334, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033587941, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033589321, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033590784, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033592177, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033593766, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033595221, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033596578, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033598110, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033598776, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033600194, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033600840, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033601065, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033601391, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033601906, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033602272, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033603008, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033603118, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033605521, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033605633, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033606009, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033606470, "dur": 1313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033607783, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033607890, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033608485, "dur": 5746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033614231, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910033614316, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033614413, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615007, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615069, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615432, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615489, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615705, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615998, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752910033615770, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910033616109, "dur": 532489, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910034149493, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910034150034, "dur": 253321, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752910034404102, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752910034404083, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752910034405771, "dur": 182, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910034511632, "dur": 438, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752910034405962, "dur": 106119, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752910034514967, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752910034514958, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752910034515060, "dur": 477, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752910034515540, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033543638, "dur": 9584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033553227, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033553435, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033553737, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033553814, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033553962, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033554063, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033554239, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033554361, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033554605, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033554762, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033554943, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033555057, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033555262, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033555408, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033555576, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033555702, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033555904, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033556096, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033556217, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033556303, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033556405, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033556580, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033556746, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033556892, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033557102, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033557296, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033557407, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033557551, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033557760, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033557957, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558091, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558223, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558369, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558481, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558574, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558694, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558845, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033558974, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559123, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559241, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559366, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559510, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559643, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559823, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033559948, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560083, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560250, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560376, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560494, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560629, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560771, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033560877, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033562488, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033564395, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033565986, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033567398, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033568892, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033570516, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033572102, "dur": 2951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033575054, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033576745, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033578385, "dur": 1765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033580151, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033581832, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033583177, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033584849, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033586246, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033587853, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033589235, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033590642, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033592094, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033593650, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033595151, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033596514, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033598051, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033599200, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033600619, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033601070, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033601340, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033601817, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033602302, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033603393, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033603609, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033603700, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033604079, "dur": 4509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033608588, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033608694, "dur": 2026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033610720, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033610833, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033610995, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033611082, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033612890, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033613093, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033613152, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752910033613612, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033614523, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033614646, "dur": 122892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033737539, "dur": 2454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033739994, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033740303, "dur": 4054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033744357, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033744524, "dur": 5618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033750142, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033750288, "dur": 3694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033753982, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033754089, "dur": 3851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033757941, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752910033758045, "dur": 7410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752910033765527, "dur": 750127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033543643, "dur": 9598, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033553245, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033553412, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033553589, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033553779, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033553872, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033553985, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033554097, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033554272, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033554521, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033554651, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033554877, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033554990, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033555187, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033555302, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033555486, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033555593, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033555762, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033555886, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033556140, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033556211, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033556366, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033556520, "dur": 5424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033561945, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033562021, "dur": 24409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033586430, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033586665, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033586761, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033586845, "dur": 3720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033590566, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033590638, "dur": 10629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033601341, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033601414, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033601763, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033601927, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033604359, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033604532, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_B00737B65E8631B3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033604641, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752910033604925, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033606974, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033607096, "dur": 3896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752910033611111, "dur": 1744, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033735431, "dur": 751, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033612860, "dur": 123333, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752910033737384, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033739986, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033740322, "dur": 3579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033743901, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033743998, "dur": 4768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033748767, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033748890, "dur": 4314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033753204, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033753332, "dur": 4026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033757358, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752910033757484, "dur": 7990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752910033765546, "dur": 750130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033543658, "dur": 9588, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033553251, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033553419, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033553625, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033553783, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033553885, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033554002, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033554169, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033554289, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033554539, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033554711, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033554898, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033555011, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033555210, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033555330, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033555530, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033555622, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033555830, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033556000, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033556179, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033556250, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033556345, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033556520, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033556643, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033556854, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033557031, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033557242, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033557366, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033557534, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033557742, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033557922, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558050, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558208, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558355, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558480, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558602, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558733, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033558899, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559037, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559158, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559276, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559400, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559527, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559692, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559849, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033559980, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033560152, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033560280, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033560409, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033560561, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033560689, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033560823, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033562399, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033564309, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033565924, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033567438, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033568948, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033570573, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033572162, "dur": 3108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033575274, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033576855, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033578491, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033580284, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033581940, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033583331, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033584971, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033586331, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033587936, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033589323, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033590798, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033592197, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033593795, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033595261, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033596630, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033598150, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033598758, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033599523, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033600868, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033601067, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033601395, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033601898, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033603325, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033603385, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033606040, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033606160, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033608966, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033609087, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033610335, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033610489, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033612489, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033612680, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033612777, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752910033612830, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033613035, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033613188, "dur": 1836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033615024, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752910033615082, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033615368, "dur": 124237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033739606, "dur": 4746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033744353, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033744495, "dur": 4762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033749257, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033749406, "dur": 7630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033757036, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033757324, "dur": 7019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752910033764344, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033764452, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752910033765510, "dur": 750106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033543660, "dur": 9597, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033553261, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033553414, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033553615, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033553779, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033553855, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033554016, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033554179, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033554301, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033554556, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033554725, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033554914, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033555030, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033555225, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033555324, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033555524, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033555605, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033555793, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033555956, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033556171, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033556231, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033556339, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033556534, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033556660, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033556871, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033557035, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033557235, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033557375, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033557525, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033557704, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033557842, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558033, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558160, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558290, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558434, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558522, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558642, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558760, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033558917, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559059, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559185, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559280, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559405, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559533, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559673, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559844, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033559964, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033560128, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033560259, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033560362, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033561181, "dur": 444, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752910033561626, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033563434, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033565187, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033566713, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033568249, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033569761, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033571389, "dur": 2061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033573451, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033576070, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033577796, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033579320, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033581276, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033582625, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033584173, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033585685, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033587192, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033587284, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033588746, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033590153, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033591589, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033593022, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033594676, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033596075, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033597523, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033598886, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033600355, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033600671, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033601004, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033601063, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033601345, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033601809, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033602451, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033603611, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033603791, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033603892, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033604565, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033604867, "dur": 1354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033606221, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033606437, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033606496, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033606838, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033607154, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033607372, "dur": 2083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033609455, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033609578, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033611108, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033611430, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033611551, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033611842, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752910033612473, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033612539, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033614073, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033614164, "dur": 123221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033737387, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033739985, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033740310, "dur": 10686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033750997, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033751279, "dur": 5288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033756567, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033756688, "dur": 5255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752910033761944, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033762102, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033762164, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033762347, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752910033762405, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033762502, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033762637, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752910033762709, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033762864, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752910033762931, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763080, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752910033763143, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763271, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763363, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763460, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763604, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752910033763660, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763790, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033763897, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033764003, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033764091, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033764468, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033764540, "dur": 984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752910033765542, "dur": 750102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033543699, "dur": 9574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033553277, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033553421, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033553641, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033553798, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033553948, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033554037, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033554215, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033554340, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033554582, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033554744, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033554917, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033555039, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033555228, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033555378, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033555562, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033555681, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033555859, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033556038, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033556192, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033556273, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033556384, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033556552, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033556691, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033556887, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033557074, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033557270, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033557433, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033557556, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033557774, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033557945, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558075, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558202, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558342, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558470, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558543, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558657, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558783, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033558928, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559090, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559205, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559315, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559456, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559567, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559747, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033559899, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560016, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560191, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560320, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560443, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560593, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560718, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033560842, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033562422, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033564331, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033565943, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033567460, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033568963, "dur": 1650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033570613, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033572159, "dur": 3029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033575189, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033576834, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033578459, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033580263, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033581908, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033583292, "dur": 1650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033584943, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033586321, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033587943, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033589316, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033590779, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033592187, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033593780, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033595253, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033596619, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033598141, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033598781, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033600187, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033600977, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033601082, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033601352, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033601800, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033602599, "dur": 1508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033604108, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033604256, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033604893, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033604955, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033605219, "dur": 2680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033607900, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033608082, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752910033608828, "dur": 1496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033610324, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033610429, "dur": 1724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033612153, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033612310, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033612366, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033613858, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033613949, "dur": 123440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033737392, "dur": 2609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033740002, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033740393, "dur": 3398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033743791, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033743893, "dur": 5309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033749202, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033749307, "dur": 4092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033753399, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033753479, "dur": 7109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752910033760588, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033760868, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033761117, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033761608, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033761718, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033761802, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033761945, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033762188, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033762305, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033762467, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752910033762526, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033762790, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033762934, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033762995, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752910033763056, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763248, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763367, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763435, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763562, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763625, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752910033763677, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763769, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033763959, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033764066, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033764138, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910033764995, "dur": 749969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752910034514991, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752910034514966, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752910034515079, "dur": 490, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752910034518007, "dur": 1074, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1752910032851870, "dur": 502933, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752910032852581, "dur": 108388, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752910033283682, "dur": 3113, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752910033286796, "dur": 67999, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752910033287691, "dur": 45003, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752910033359759, "dur": 888, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752910033359500, "dur": 1296, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": **********328862, "dur": 2710, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********331613, "dur": 21444, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********353163, "dur": 165, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********353916, "dur": 451, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********355223, "dur": 168, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********355587, "dur": 1484, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": **********393025, "dur": 48111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": **********353337, "dur": 96689, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********450033, "dur": 102701, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********552736, "dur": 85738, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": **********638535, "dur": 3873593, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910032512170, "dur": 446, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910032513081, "dur": 563, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910032513727, "dur": 1389, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": **********353220, "dur": 96822, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********450070, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": **********450228, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********451139, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********451952, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********452462, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********453167, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********453399, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********453558, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********453674, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********453805, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********453954, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********454074, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********454304, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********454565, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********454723, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********454822, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********454961, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********455038, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********455104, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********455223, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": **********456236, "dur": 25025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********481261, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********481647, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********481762, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********481878, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********483968, "dur": 24181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********508149, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********508597, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********508685, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********508994, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********509329, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********509389, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********509585, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********510629, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********511033, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_B00737B65E8631B3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********511111, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********511309, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": **********511532, "dur": 1761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": **********513293, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********513561, "dur": 1436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": **********515032, "dur": 164, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********636901, "dur": 331, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********515740, "dur": 121503, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": **********353234, "dur": 96813, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********450081, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********450234, "dur": 916, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********451156, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********451937, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********452314, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********452477, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********452629, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********452797, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********453035, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********453124, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********453250, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********453501, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********453619, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********453735, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********453865, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********454055, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********454360, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********454573, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********454956, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********455341, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********455585, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********455681, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********455789, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********455917, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********456061, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********456328, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********456409, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********456747, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********456862, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********457107, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********457267, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********457516, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********457597, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********457768, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********457906, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********458373, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********458585, "dur": 1483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********460068, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********460229, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********460340, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": **********460430, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********460533, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********460719, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********460785, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********460922, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********461045, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********461158, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********461263, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********461390, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********461591, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********461764, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********461923, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": **********461989, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********462137, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********462325, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********462474, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********462693, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********463197, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********463306, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": **********463780, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********463988, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********464190, "dur": 2488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********466679, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********468458, "dur": 2123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********470581, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********472449, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********474619, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********476556, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********478184, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********479850, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********481588, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********483336, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********486204, "dur": 1036, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Math/Vector3/Vector3Multiply.cs"}}, {"pid": 12345, "tid": 2, "ts": **********485215, "dur": 2319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********487534, "dur": 1299, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Events/GUI/OnMove.cs"}}, {"pid": 12345, "tid": 2, "ts": **********487534, "dur": 2956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********490490, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********492034, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********493731, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********495700, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********497666, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********499389, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********501438, "dur": 2126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********503564, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********505219, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********506649, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********507963, "dur": 116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********508079, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********508561, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********509335, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********509395, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********509971, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********510779, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********511333, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********511555, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********511840, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********512135, "dur": 1705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********513841, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********514350, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********514550, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********515128, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********515470, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********515586, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********516516, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********516666, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_3BDB24518B22122E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********516747, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********516814, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_C1F9C6FB58945FCF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********516886, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********517019, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_62F0C87D527BCC98.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********517119, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********517254, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********517555, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********517738, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": **********518551, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********518979, "dur": 2479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********521458, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": **********521549, "dur": 31177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********353256, "dur": 96807, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********450064, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********451959, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********452309, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********452460, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********452622, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********452864, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********453163, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********453326, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********453422, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********453539, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********453681, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********453838, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********454030, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********454231, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********454493, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********454652, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********454812, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********454969, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********455081, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********455133, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********455250, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********455431, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********455569, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********455662, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********455755, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********455924, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********456323, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********456885, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********457014, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********458590, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********458666, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********458760, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********460275, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********460437, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********460528, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": **********460676, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********460769, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********460911, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********461031, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********461181, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********461323, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********461528, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********461721, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********461929, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********462043, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********462196, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********462389, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********462513, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********462685, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********462779, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********463026, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********463183, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********463297, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": **********463594, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********463764, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********463949, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********464744, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Assets/Layer Lab/GUI Pro-CasualGame/Scripts/PanelControlCasualGame.cs"}}, {"pid": 12345, "tid": 3, "ts": **********465631, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Assets/Layer Lab/GUI Pro-CasualGame/Extensions/UIParticle/UIParticleSystem.cs"}}, {"pid": 12345, "tid": 3, "ts": **********464124, "dur": 3558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********467683, "dur": 2440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********470124, "dur": 1950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********472075, "dur": 1946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********474021, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********475854, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********477666, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********479382, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********481050, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********482711, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********484609, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********486111, "dur": 1180, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Math/Generic/GenericModulo.cs"}}, {"pid": 12345, "tid": 3, "ts": **********486111, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********488327, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/ParameterArgs.cs"}}, {"pid": 12345, "tid": 3, "ts": **********488327, "dur": 2187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********490515, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********492060, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********493800, "dur": 1816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********495617, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********497490, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********499102, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********500858, "dur": 2017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********502875, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********504646, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********506273, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********507047, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********507696, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********508012, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********508578, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********509375, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********509461, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********509585, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********509938, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********510366, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********510491, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********510572, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********510682, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********511667, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********511783, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********511998, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********512122, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********513221, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********513695, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********513883, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********514075, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********514213, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********515392, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********515653, "dur": 2041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********517694, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********518097, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********518185, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********518783, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********518971, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********520024, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": **********520164, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": **********520427, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********520596, "dur": 32120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********353274, "dur": 96802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********450080, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********451959, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********452493, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********452733, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********452843, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********453346, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********453531, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********453657, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********453788, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********453930, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********454118, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********454393, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********454602, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********454732, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********455047, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********455116, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********455213, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": **********455630, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********455828, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": **********456046, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********456310, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********456566, "dur": 23540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********480107, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********480621, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********480706, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********480924, "dur": 1702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********482626, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********484564, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********486138, "dur": 1066, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Logic/ExclusiveOr.cs"}}, {"pid": 12345, "tid": 4, "ts": **********486138, "dur": 2140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********488278, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********490049, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********491637, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********493280, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********494970, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********496885, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********498722, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********500367, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********502363, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********504245, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********505851, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********507060, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********507685, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********507822, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********507960, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********508075, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********508565, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********508740, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********509470, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********509590, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********512282, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********512544, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********512684, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********512924, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********513199, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********513362, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********513803, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********515320, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********515512, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********515960, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********516635, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********516903, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": **********516995, "dur": 1556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": **********518551, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********519063, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********519196, "dur": 33452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********353284, "dur": 96806, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********450094, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********451964, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********452510, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********453018, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********453238, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********453392, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********453640, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********453746, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********453935, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********454175, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********454495, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********454646, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********454781, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********454964, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********455098, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********455215, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********455672, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********455775, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********456077, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 5, "ts": **********456252, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********456474, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": **********456888, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********457167, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": **********457370, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********457542, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********457679, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********457879, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********457961, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********459113, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********459376, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********459697, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********460225, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********460365, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********460450, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 5, "ts": **********460666, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********460760, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********460887, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********461022, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********461235, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********461374, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********461537, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********461683, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********461945, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********461997, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********462149, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********462386, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********462500, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********462674, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********462954, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": **********463614, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********463844, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********464075, "dur": 2580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********466656, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********468294, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********470726, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********472406, "dur": 1983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********474390, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********476244, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********477973, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********479700, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********481475, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********483134, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********484998, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********486250, "dur": 1023, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Events/Physics/OnCollisionEnter.cs"}}, {"pid": 12345, "tid": 5, "ts": **********487960, "dur": 865, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Events/Input/OnMouseUpAsButton.cs"}}, {"pid": 12345, "tid": 5, "ts": **********486250, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********489381, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********491089, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********492639, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********494314, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********496088, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********497881, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********499645, "dur": 1801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********501446, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********503655, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********505243, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********506793, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********507455, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********507681, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********507901, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********508060, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********508575, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********509350, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********509413, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********509614, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********510022, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********510718, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_4D635C4BE46FE4CD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********510844, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********511539, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********512653, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********513001, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********513164, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********513388, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********513902, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********513994, "dur": 1291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********515285, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********515583, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********516506, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********516849, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_BEADB10929D2C8CB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********516979, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********517098, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********517207, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********517367, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********518137, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********518619, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********518869, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********518965, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********519234, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": **********519351, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********519497, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": **********519822, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********520087, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": **********520225, "dur": 32430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********353293, "dur": 96805, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********450100, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********451957, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********452451, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********452615, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********452686, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********453263, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********453333, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********453465, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********453686, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********453792, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********454028, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********454222, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********454482, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********454629, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********454779, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********454941, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********455590, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********455693, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********455808, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********455932, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********455988, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********456060, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********456447, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 6, "ts": **********456798, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********457317, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********457512, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********457623, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********457846, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********458353, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********459289, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********459657, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********460080, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********460250, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********460436, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********460562, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********460799, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********460956, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********461098, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********461202, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********461286, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********461444, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********461539, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********461704, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********461942, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********462104, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********462335, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********462483, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********462658, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********462863, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": **********463261, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********463374, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********463475, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********463774, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********464120, "dur": 2634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********466755, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********468464, "dur": 2076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********470540, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********472428, "dur": 2215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********474643, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********476520, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********478160, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********479785, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********481529, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********483177, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********485070, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********486264, "dur": 2464, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Events/Input/OnKeyboardInput.cs"}}, {"pid": 12345, "tid": 6, "ts": **********486264, "dur": 3730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********489994, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********491566, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********493238, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********494906, "dur": 1850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********496757, "dur": 1946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********498704, "dur": 1691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********500396, "dur": 1954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********502350, "dur": 1904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********504254, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********505898, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********506449, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********507995, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********508575, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********509342, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********509409, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********509597, "dur": 4288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********513886, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********514114, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********514456, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********514641, "dur": 3644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********518286, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********518928, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_3B0260CCB4011188.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********519071, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": **********519156, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": **********519555, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********519637, "dur": 33014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********353303, "dur": 96802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********450109, "dur": 1820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********451930, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********452295, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********452672, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********453193, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********453323, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********453377, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********453613, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********453704, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********453916, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********454054, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********454361, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********454526, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********454661, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********454896, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********455034, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********455088, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********455153, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********455226, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********455714, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********455858, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********455944, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********456036, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********456532, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********456936, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********457001, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": **********457091, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********457452, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********457646, "dur": 742, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********458391, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********458550, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********459941, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********460190, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********460700, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 7, "ts": **********460750, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********460856, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********460999, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********461104, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********461252, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********461415, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********461511, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********461666, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********461825, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********462015, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********462175, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********462397, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********462518, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********462732, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********462926, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********463054, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********463294, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********463733, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********463904, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": **********464114, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********464427, "dur": 2594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********467021, "dur": 2550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********469571, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********471567, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********473342, "dur": 1889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********475231, "dur": 1917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********477149, "dur": 1693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********478842, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********480436, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********482208, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********482289, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********483883, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********485770, "dur": 1447, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Math/Scalar/ScalarMaximum.cs"}}, {"pid": 12345, "tid": 7, "ts": **********485770, "dur": 2552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********488322, "dur": 2065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********490387, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********491929, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********493630, "dur": 1657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********495287, "dur": 2039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********497327, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********498942, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********500729, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********502727, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********504641, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********506236, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********506803, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********507422, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********507689, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********507890, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********507980, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********508057, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********508601, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********509434, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********509533, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********509588, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********510612, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********511274, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********511492, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": **********512181, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********512242, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********513307, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********513538, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********515154, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********515661, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********516196, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********516328, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********517454, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********517675, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********518547, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********518819, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": **********518920, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 7, "ts": **********518983, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********519044, "dur": 33698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********353313, "dur": 96800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********450116, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********451962, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********452464, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********452623, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********452756, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********453134, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********453413, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********453574, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********453823, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********454033, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********454275, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********454514, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********454699, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********454867, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********455004, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********455544, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********455601, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********455728, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********455867, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********456259, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********456817, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********456884, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": **********457147, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********457233, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********457519, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********457639, "dur": 1743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********459382, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********459756, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********459834, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********460111, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********460167, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********460292, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********460369, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********460479, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********460834, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********460978, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********461158, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********461302, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********461648, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********461813, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********462094, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********462227, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********462424, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********462611, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********462790, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********463044, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********463214, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": **********463293, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********463408, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********463514, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********463707, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********463852, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********464104, "dur": 2602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********466707, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********468477, "dur": 2101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********470578, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********472566, "dur": 2074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********474641, "dur": 1941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********476583, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********478212, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********479872, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********481610, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********483382, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********485240, "dur": 1989, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Math/Vector3/Vector3Lerp.cs"}}, {"pid": 12345, "tid": 8, "ts": **********485240, "dur": 3072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********488313, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Collections/Lists/InsertListItem.cs"}}, {"pid": 12345, "tid": 8, "ts": **********488313, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********490520, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********492048, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********493721, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********495350, "dur": 1850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********497201, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********498803, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********500564, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********502619, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********504458, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********506045, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********506635, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********508097, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********508605, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********509443, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********509510, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********509573, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********511982, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********512208, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********512279, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********512428, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********512590, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********512736, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********513253, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********513374, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********513772, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********513840, "dur": 1943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********515783, "dur": 786, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********516576, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": **********516934, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********517132, "dur": 1317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": **********518449, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********518845, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********518997, "dur": 32504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********551502, "dur": 1122, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********353321, "dur": 96812, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********450160, "dur": 1789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********451950, "dur": 675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********452629, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********453055, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********453232, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********453437, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********453568, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********453701, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********453828, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********454045, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********454318, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********454553, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********454679, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********454845, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********454990, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********455092, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********455145, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********455369, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********455621, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********455731, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********455872, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********456097, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********456346, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 9, "ts": **********456670, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********456788, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********457312, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********457489, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********457659, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********458226, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": **********458456, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********459490, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********459895, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********460142, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********460388, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": **********460715, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********460818, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********460969, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********461180, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********461283, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********461335, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********461458, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********461555, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********461690, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********461833, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********461940, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********462088, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********462276, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********462448, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********462653, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********462921, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********463198, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********463332, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********463438, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 9, "ts": **********463720, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********463850, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********464069, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********466681, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********468308, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********470059, "dur": 1702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********471761, "dur": 1964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********473725, "dur": 1905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********475630, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********477494, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********479208, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********480955, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********482663, "dur": 1908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********484571, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********486194, "dur": 1074, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Events/Rendering/OnBecameVisible.cs"}}, {"pid": 12345, "tid": 9, "ts": **********486194, "dur": 2138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********488332, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********488466, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********488589, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********489186, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********490892, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********492520, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********494181, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********495937, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********497719, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********499355, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********501219, "dur": 2328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********503547, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********505150, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********506748, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********507584, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********507708, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********507897, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********507967, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********508068, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********508605, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********508747, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********509471, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********509566, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********509764, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********509985, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********510974, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********511209, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_C0EF7B73E8141BD2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********511307, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********511587, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********511943, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********512108, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********513304, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********514043, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_1A7899D4E6CA9138.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********514193, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********515356, "dur": 3328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********518684, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********518956, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_BBAE91395FB6DB68.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********519029, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********519143, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********519269, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********519812, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********520013, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********520131, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********520465, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********520558, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********520671, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********520725, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********520851, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********521100, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********521301, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********521456, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": **********521538, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": **********521927, "dur": 50, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": **********521992, "dur": 3985378, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********353332, "dur": 96819, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********450151, "dur": 1798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********451949, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********452536, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********453123, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********453358, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********453562, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********453681, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********453858, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********454288, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********454507, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********454688, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********455202, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": **********455625, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********455722, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********455816, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********455929, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********456742, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********456839, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 10, "ts": **********457136, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********457362, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********457570, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********457727, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********457899, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********458201, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": **********458417, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********458542, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********458725, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********459204, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********459462, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 10, "ts": **********459610, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********459993, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********460203, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********460317, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********460755, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********460871, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********461002, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********461119, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********461202, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********461427, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********461577, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********461746, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********461942, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********462120, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********462343, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********462466, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********462634, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********462874, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********463221, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********463778, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********463953, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 10, "ts": **********464030, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********464231, "dur": 2536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********466768, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********468499, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********470586, "dur": 2027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********472614, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********474602, "dur": 1900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********476503, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********478340, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********480104, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********481933, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 10, "ts": **********481984, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********482256, "dur": 1681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********483937, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********485684, "dur": 1529, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Framework/Math/Vector2/Vector2Lerp.cs"}}, {"pid": 12345, "tid": 10, "ts": **********485684, "dur": 2614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********488298, "dur": 1979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********490277, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********491838, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********493492, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********495202, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********497081, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********498780, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********500548, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********502507, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********504441, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********506112, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********506642, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********508049, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********508586, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********509427, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********509491, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********509579, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********510080, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********510731, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********511664, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********511993, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********514478, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********514740, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********514870, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********515256, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********515718, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Runtime.ref.dll_83DAF2B64EDCB1D5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********515857, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********515976, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********516094, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********516204, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********518472, "dur": 611, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********519093, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********519229, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": **********519343, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********519430, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": **********519783, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": **********520234, "dur": 32470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752910032520400, "dur": 1959, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 2659, "ts": 1752910034555758, "dur": 5613, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 39945, "tid": 2659, "ts": 1752910034563823, "dur": 30, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 39945, "tid": 2659, "ts": 1752910034564028, "dur": 5, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 2659, "ts": 1752910034561488, "dur": 2331, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034563906, "dur": 121, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034564050, "dur": 387, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 2659, "ts": 1752910034548641, "dur": 16486, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}