{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 2845, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 2845, "ts": 1752931249559240, "dur": 357, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249576623, "dur": 685, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248847319, "dur": 10572, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248857892, "dur": 696240, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248857926, "dur": 142, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248858075, "dur": 117110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248975190, "dur": 27, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248975220, "dur": 781, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976029, "dur": 2, "ph": "X", "name": "ProcessMessages 2386", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976032, "dur": 27, "ph": "X", "name": "ReadAsync 2386", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976117, "dur": 1, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976119, "dur": 29, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976150, "dur": 1, "ph": "X", "name": "ProcessMessages 2265", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976151, "dur": 57, "ph": "X", "name": "ReadAsync 2265", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976214, "dur": 2, "ph": "X", "name": "ProcessMessages 1723", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976217, "dur": 36, "ph": "X", "name": "ReadAsync 1723", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976254, "dur": 1, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976256, "dur": 32, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976291, "dur": 1, "ph": "X", "name": "ProcessMessages 1171", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976293, "dur": 29, "ph": "X", "name": "ReadAsync 1171", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976323, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976325, "dur": 37, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976364, "dur": 1, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976366, "dur": 76, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976443, "dur": 1, "ph": "X", "name": "ProcessMessages 2539", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976445, "dur": 22, "ph": "X", "name": "ReadAsync 2539", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976469, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976472, "dur": 41, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976515, "dur": 1, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976517, "dur": 38, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976557, "dur": 1, "ph": "X", "name": "ProcessMessages 1363", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976559, "dur": 58, "ph": "X", "name": "ReadAsync 1363", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976619, "dur": 1, "ph": "X", "name": "ProcessMessages 1676", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976621, "dur": 25, "ph": "X", "name": "ReadAsync 1676", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976648, "dur": 52, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976710, "dur": 1, "ph": "X", "name": "ProcessMessages 1452", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976712, "dur": 38, "ph": "X", "name": "ReadAsync 1452", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976752, "dur": 1, "ph": "X", "name": "ProcessMessages 1467", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976753, "dur": 32, "ph": "X", "name": "ReadAsync 1467", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976788, "dur": 27, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976831, "dur": 38, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976871, "dur": 1, "ph": "X", "name": "ProcessMessages 1639", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976873, "dur": 28, "ph": "X", "name": "ReadAsync 1639", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976903, "dur": 34, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976939, "dur": 37, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976979, "dur": 18, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248976998, "dur": 26, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977027, "dur": 33, "ph": "X", "name": "ReadAsync 1575", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977063, "dur": 55, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977119, "dur": 1, "ph": "X", "name": "ProcessMessages 1824", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977121, "dur": 30, "ph": "X", "name": "ReadAsync 1824", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977153, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977154, "dur": 26, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977183, "dur": 53, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977238, "dur": 1, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977240, "dur": 27, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977269, "dur": 42, "ph": "X", "name": "ReadAsync 1046", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977313, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977314, "dur": 30, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977347, "dur": 30, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977379, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977381, "dur": 33, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977417, "dur": 26, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977445, "dur": 25, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977473, "dur": 31, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977506, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977508, "dur": 45, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977555, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977556, "dur": 34, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977594, "dur": 27, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977622, "dur": 34, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977659, "dur": 21, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977682, "dur": 30, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977715, "dur": 32, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977750, "dur": 33, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977784, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977785, "dur": 21, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977809, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977840, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977841, "dur": 31, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977874, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977876, "dur": 29, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977907, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977932, "dur": 21, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977963, "dur": 34, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248977999, "dur": 28, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978029, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978032, "dur": 39, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978074, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978075, "dur": 31, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978109, "dur": 56, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978166, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978168, "dur": 27, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978197, "dur": 27, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978226, "dur": 24, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978253, "dur": 22, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978276, "dur": 32, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978310, "dur": 28, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978341, "dur": 29, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978371, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978372, "dur": 30, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978404, "dur": 11, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978417, "dur": 44, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978462, "dur": 1, "ph": "X", "name": "ProcessMessages 1738", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978464, "dur": 42, "ph": "X", "name": "ReadAsync 1738", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978520, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978521, "dur": 38, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978560, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978562, "dur": 47, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978610, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978612, "dur": 21, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978636, "dur": 42, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978680, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978682, "dur": 28, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978711, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978713, "dur": 27, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978742, "dur": 39, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978784, "dur": 24, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978811, "dur": 30, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978844, "dur": 31, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978877, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978878, "dur": 23, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978903, "dur": 47, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978953, "dur": 28, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248978985, "dur": 32, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979020, "dur": 34, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979055, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979056, "dur": 29, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979087, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979088, "dur": 32, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979122, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979125, "dur": 30, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979157, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979159, "dur": 42, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979203, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979204, "dur": 30, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979237, "dur": 25, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979264, "dur": 50, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979317, "dur": 97, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979415, "dur": 1, "ph": "X", "name": "ProcessMessages 2022", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979417, "dur": 19, "ph": "X", "name": "ReadAsync 2022", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979438, "dur": 89, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979528, "dur": 1, "ph": "X", "name": "ProcessMessages 1205", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979529, "dur": 28, "ph": "X", "name": "ReadAsync 1205", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979559, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979561, "dur": 36, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979600, "dur": 32, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979633, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979635, "dur": 38, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979676, "dur": 39, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979718, "dur": 25, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979745, "dur": 58, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979806, "dur": 24, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979833, "dur": 31, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979866, "dur": 28, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979896, "dur": 30, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979928, "dur": 28, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979958, "dur": 23, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248979983, "dur": 21, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980006, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980030, "dur": 55, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980086, "dur": 1, "ph": "X", "name": "ProcessMessages 1344", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980088, "dur": 24, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980114, "dur": 23, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980140, "dur": 29, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980171, "dur": 45, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980218, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980220, "dur": 24, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980246, "dur": 32, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980280, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980282, "dur": 30, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980315, "dur": 29, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980346, "dur": 40, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980403, "dur": 1, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980405, "dur": 25, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980433, "dur": 24, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980460, "dur": 28, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980491, "dur": 27, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980520, "dur": 34, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980556, "dur": 32, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980592, "dur": 31, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980624, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980625, "dur": 25, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980654, "dur": 33, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980688, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980689, "dur": 28, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980720, "dur": 36, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980759, "dur": 30, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980808, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980810, "dur": 44, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980855, "dur": 1, "ph": "X", "name": "ProcessMessages 1666", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980857, "dur": 35, "ph": "X", "name": "ReadAsync 1666", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980894, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980895, "dur": 31, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980928, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980929, "dur": 28, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248980960, "dur": 37, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981000, "dur": 32, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981034, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981035, "dur": 43, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981080, "dur": 1, "ph": "X", "name": "ProcessMessages 1275", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981081, "dur": 36, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981120, "dur": 44, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981166, "dur": 27, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981195, "dur": 25, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981222, "dur": 44, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981268, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981270, "dur": 37, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981309, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981310, "dur": 24, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981336, "dur": 26, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981365, "dur": 35, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981403, "dur": 28, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981433, "dur": 32, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981467, "dur": 31, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981499, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981501, "dur": 54, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981557, "dur": 1, "ph": "X", "name": "ProcessMessages 1732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981558, "dur": 40, "ph": "X", "name": "ReadAsync 1732", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981600, "dur": 1, "ph": "X", "name": "ProcessMessages 1293", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981602, "dur": 30, "ph": "X", "name": "ReadAsync 1293", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981635, "dur": 30, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981666, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981669, "dur": 30, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981735, "dur": 34, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981771, "dur": 1, "ph": "X", "name": "ProcessMessages 2086", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981773, "dur": 38, "ph": "X", "name": "ReadAsync 2086", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981812, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981814, "dur": 24, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981840, "dur": 41, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981883, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981884, "dur": 27, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981914, "dur": 35, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981951, "dur": 24, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248981977, "dur": 81, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982060, "dur": 66, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982129, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982131, "dur": 37, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982170, "dur": 22, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982194, "dur": 66, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982262, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982264, "dur": 39, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982305, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982307, "dur": 90, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982399, "dur": 9, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982409, "dur": 29, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982440, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982441, "dur": 54, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982497, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982505, "dur": 34, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982540, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982542, "dur": 108, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982651, "dur": 1, "ph": "X", "name": "ProcessMessages 1449", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982653, "dur": 34, "ph": "X", "name": "ReadAsync 1449", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982688, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982690, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982718, "dur": 69, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982789, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982790, "dur": 32, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982825, "dur": 31, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982859, "dur": 30, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982891, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982893, "dur": 43, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982938, "dur": 31, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982971, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248982972, "dur": 75, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983049, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983051, "dur": 41, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983095, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983097, "dur": 81, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983181, "dur": 33, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983217, "dur": 56, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983275, "dur": 65, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983342, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983344, "dur": 64, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983409, "dur": 1, "ph": "X", "name": "ProcessMessages 1262", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983411, "dur": 36, "ph": "X", "name": "ReadAsync 1262", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983448, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983450, "dur": 24, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983476, "dur": 67, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983545, "dur": 29, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983577, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983578, "dur": 56, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983636, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983637, "dur": 65, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983704, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983705, "dur": 38, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983745, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983747, "dur": 37, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983785, "dur": 78, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983865, "dur": 1, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983867, "dur": 27, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983896, "dur": 26, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983924, "dur": 31, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983957, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983959, "dur": 38, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248983999, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984021, "dur": 91, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984114, "dur": 1, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984115, "dur": 23, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984140, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984159, "dur": 27, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984188, "dur": 29, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984220, "dur": 41, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984263, "dur": 30, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984296, "dur": 21, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984320, "dur": 33, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984355, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984356, "dur": 75, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984433, "dur": 1, "ph": "X", "name": "ProcessMessages 1512", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984435, "dur": 39, "ph": "X", "name": "ReadAsync 1512", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984477, "dur": 39, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984518, "dur": 29, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984550, "dur": 41, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984594, "dur": 26, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984622, "dur": 65, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984689, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984690, "dur": 32, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984724, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984725, "dur": 81, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984808, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984809, "dur": 30, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984841, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984842, "dur": 60, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984905, "dur": 81, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248984989, "dur": 47, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985038, "dur": 38, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985078, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985080, "dur": 46, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985127, "dur": 1, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985128, "dur": 20, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985150, "dur": 80, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985232, "dur": 1, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985234, "dur": 21, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985256, "dur": 35, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985293, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985294, "dur": 30, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985326, "dur": 173, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985502, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985504, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985678, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985726, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985882, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985884, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248985947, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986123, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986126, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986167, "dur": 175, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986345, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986395, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986501, "dur": 131, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986635, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986667, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986699, "dur": 169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986870, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986918, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248986957, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987012, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987138, "dur": 80, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987220, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987253, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987388, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987390, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987473, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987553, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987600, "dur": 110, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987713, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987809, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987811, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987851, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248987897, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988015, "dur": 67, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988086, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988173, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988209, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988286, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988316, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988391, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988392, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988509, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988540, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988577, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988624, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988663, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988716, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988718, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988751, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988786, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988819, "dur": 72, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248988894, "dur": 132, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989029, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989070, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989104, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989144, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989177, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989219, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989283, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989330, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989362, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989403, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989503, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989505, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989551, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989584, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989634, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989676, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989717, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989741, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989768, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989813, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989845, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989893, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989929, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989931, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248989985, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990023, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990064, "dur": 84, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990150, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990192, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990229, "dur": 85, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990316, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990418, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990423, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990475, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990477, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990537, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990577, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990645, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990646, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990719, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990765, "dur": 73, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990841, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990895, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990898, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990930, "dur": 27, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248990960, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991031, "dur": 52, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991086, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991113, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991136, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991166, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991227, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991269, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991322, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991370, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991412, "dur": 43, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991459, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991460, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991488, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991530, "dur": 439, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991970, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248991998, "dur": 1653, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248993653, "dur": 28, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931248993684, "dur": 13430, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249007118, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249007120, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249007266, "dur": 940, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249008209, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249008449, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249008616, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249008719, "dur": 2865, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249011587, "dur": 9001, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249020592, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249020594, "dur": 417, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021020, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021025, "dur": 408, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021437, "dur": 101, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021540, "dur": 130, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021673, "dur": 215, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021891, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249021968, "dur": 817, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249022789, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249022968, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023022, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023055, "dur": 443, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023501, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023557, "dur": 215, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023773, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023775, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023823, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249023887, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249024030, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249024157, "dur": 196, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249024355, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249024473, "dur": 218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249024694, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249024821, "dur": 657, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249025481, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249025633, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249025823, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026026, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026203, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026296, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026345, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026528, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026679, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026877, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249026980, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249027063, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249027107, "dur": 48, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249027159, "dur": 469, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249027630, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249027727, "dur": 198, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249027928, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249028283, "dur": 741, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029026, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029029, "dur": 128, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029158, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029277, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029419, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029600, "dur": 220, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249029823, "dur": 730, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249030573, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249030751, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249030835, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031144, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031289, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031338, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031471, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031474, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031535, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031602, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031761, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249031939, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249032074, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249032129, "dur": 247, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249032379, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249032533, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249032673, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249032720, "dur": 372, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249033095, "dur": 364, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249033462, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249033537, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249033638, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249033750, "dur": 263, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034017, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034089, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034091, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034234, "dur": 81, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034317, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034353, "dur": 277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034633, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034663, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034731, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249034942, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249035014, "dur": 404, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249035419, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249035420, "dur": 105074, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140503, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140505, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140564, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140608, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140649, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140689, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140730, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140777, "dur": 56, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249140834, "dur": 4085, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249144924, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249144927, "dur": 324, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249145254, "dur": 1742, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249147001, "dur": 1287, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249148290, "dur": 1501, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249149795, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249149852, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249149946, "dur": 2003, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249151953, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249151956, "dur": 1034, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249152995, "dur": 1508, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249154507, "dur": 472, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249154982, "dur": 556, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249155541, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249155543, "dur": 444, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249155992, "dur": 1219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249157215, "dur": 267, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249157485, "dur": 882, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249158370, "dur": 1700, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249160074, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249160076, "dur": 318, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249160399, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249160804, "dur": 1329, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249162137, "dur": 198, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249162338, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249162580, "dur": 1754, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249164337, "dur": 976, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249165316, "dur": 808, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166128, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166185, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166237, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166375, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166433, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166518, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166628, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166735, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166860, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249166970, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249167050, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249167196, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249167330, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249167491, "dur": 646, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249168146, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249168151, "dur": 392, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249168546, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249168547, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249168643, "dur": 273, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249168919, "dur": 152, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249169075, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249169077, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249169350, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249169354, "dur": 471, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249169828, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249169943, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170137, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170141, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170292, "dur": 185, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170479, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170513, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170605, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170769, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249170772, "dur": 472, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249171246, "dur": 219, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249171468, "dur": 346, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249171818, "dur": 1052, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249172873, "dur": 598, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249173474, "dur": 2072, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249175583, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249175596, "dur": 316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249175946, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249175949, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249176098, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249176100, "dur": 438, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249176541, "dur": 1245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249177789, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249177791, "dur": 261, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249178056, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249178058, "dur": 277, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249178338, "dur": 1507, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249179850, "dur": 397, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249180252, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249180254, "dur": 882, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249181141, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249181272, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249181403, "dur": 275, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249181682, "dur": 210, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249181895, "dur": 120950, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249302855, "dur": 32, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249302888, "dur": 1440, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249304332, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249304346, "dur": 1633, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249305983, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249305986, "dur": 127833, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249433828, "dur": 40, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249433870, "dur": 2671, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249436545, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249436548, "dur": 10007, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446565, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446569, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446730, "dur": 42, "ph": "X", "name": "ReadAsync 5175", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446776, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446833, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446908, "dur": 23, "ph": "X", "name": "ProcessMessages 5070", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249446932, "dur": 3456, "ph": "X", "name": "ReadAsync 5070", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249450393, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249450397, "dur": 442, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249450841, "dur": 28, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249450870, "dur": 96398, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547275, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547278, "dur": 74, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547358, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547360, "dur": 74, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547438, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547489, "dur": 108, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547599, "dur": 22, "ph": "X", "name": "ProcessMessages 6651", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249547622, "dur": 3286, "ph": "X", "name": "ReadAsync 6651", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249550913, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249550916, "dur": 428, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249551347, "dur": 23, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249551371, "dur": 66, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249551440, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 21474836480, "ts": 1752931249551649, "dur": 2479, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249577312, "dur": 976, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 17179869184, "ts": 1752931248847145, "dur": 41, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 17179869184, "ts": 1752931248847186, "dur": 10702, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 17179869184, "ts": 1752931248857890, "dur": 146, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249578290, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752931248087173, "dur": 5772, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752931248092948, "dur": 28847, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752931248121803, "dur": 31873, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249578298, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248085855, "dur": 5905, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248091763, "dur": 69473, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248092421, "dur": 3346, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248095773, "dur": 813, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248096588, "dur": 6933, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248103584, "dur": 297, "ph": "X", "name": "ProcessMessages 6513", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248103884, "dur": 65, "ph": "X", "name": "ReadAsync 6513", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248103966, "dur": 5, "ph": "X", "name": "ProcessMessages 8143", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248103973, "dur": 42, "ph": "X", "name": "ReadAsync 8143", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104027, "dur": 1, "ph": "X", "name": "ProcessMessages 1597", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104028, "dur": 59, "ph": "X", "name": "ReadAsync 1597", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104141, "dur": 1, "ph": "X", "name": "ProcessMessages 1661", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104144, "dur": 49, "ph": "X", "name": "ReadAsync 1661", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104195, "dur": 2, "ph": "X", "name": "ProcessMessages 2466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104199, "dur": 46, "ph": "X", "name": "ReadAsync 2466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104248, "dur": 2, "ph": "X", "name": "ProcessMessages 1830", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104419, "dur": 102, "ph": "X", "name": "ReadAsync 1830", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104543, "dur": 2, "ph": "X", "name": "ProcessMessages 3478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104596, "dur": 83, "ph": "X", "name": "ReadAsync 3478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104682, "dur": 71, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104772, "dur": 1, "ph": "X", "name": "ProcessMessages 1648", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104774, "dur": 39, "ph": "X", "name": "ReadAsync 1648", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104842, "dur": 1, "ph": "X", "name": "ProcessMessages 2330", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104844, "dur": 32, "ph": "X", "name": "ReadAsync 2330", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104878, "dur": 1, "ph": "X", "name": "ProcessMessages 1803", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104879, "dur": 41, "ph": "X", "name": "ReadAsync 1803", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104934, "dur": 1, "ph": "X", "name": "ProcessMessages 1481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248104936, "dur": 35, "ph": "X", "name": "ReadAsync 1481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105063, "dur": 1, "ph": "X", "name": "ProcessMessages 1412", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105065, "dur": 22, "ph": "X", "name": "ReadAsync 1412", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105089, "dur": 1, "ph": "X", "name": "ProcessMessages 2854", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105091, "dur": 396, "ph": "X", "name": "ReadAsync 2854", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105488, "dur": 1, "ph": "X", "name": "ProcessMessages 1634", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105491, "dur": 39, "ph": "X", "name": "ReadAsync 1634", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105585, "dur": 21, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105622, "dur": 2, "ph": "X", "name": "ProcessMessages 2393", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105625, "dur": 172, "ph": "X", "name": "ReadAsync 2393", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105808, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105812, "dur": 31, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105845, "dur": 2, "ph": "X", "name": "ProcessMessages 1431", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248105848, "dur": 276, "ph": "X", "name": "ReadAsync 1431", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106126, "dur": 1, "ph": "X", "name": "ProcessMessages 1690", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106129, "dur": 104, "ph": "X", "name": "ReadAsync 1690", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106259, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106267, "dur": 39, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106307, "dur": 2, "ph": "X", "name": "ProcessMessages 3223", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106310, "dur": 54, "ph": "X", "name": "ReadAsync 3223", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106366, "dur": 2, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106369, "dur": 49, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106421, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106423, "dur": 54, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106478, "dur": 1, "ph": "X", "name": "ProcessMessages 1912", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106480, "dur": 185, "ph": "X", "name": "ReadAsync 1912", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106667, "dur": 2, "ph": "X", "name": "ProcessMessages 5046", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106670, "dur": 116, "ph": "X", "name": "ReadAsync 5046", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106788, "dur": 58, "ph": "X", "name": "ReadAsync 1177", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106867, "dur": 1, "ph": "X", "name": "ProcessMessages 2414", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106869, "dur": 27, "ph": "X", "name": "ReadAsync 2414", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106900, "dur": 1, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106902, "dur": 36, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106940, "dur": 32, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248106984, "dur": 56, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107041, "dur": 1, "ph": "X", "name": "ProcessMessages 1306", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107043, "dur": 64, "ph": "X", "name": "ReadAsync 1306", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107109, "dur": 1, "ph": "X", "name": "ProcessMessages 1699", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107111, "dur": 33, "ph": "X", "name": "ReadAsync 1699", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107150, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107154, "dur": 67, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107223, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107226, "dur": 48, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107275, "dur": 10, "ph": "X", "name": "ProcessMessages 1202", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107289, "dur": 48, "ph": "X", "name": "ReadAsync 1202", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107340, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107343, "dur": 42, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107390, "dur": 1, "ph": "X", "name": "ProcessMessages 1037", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107392, "dur": 35, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107436, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248107440, "dur": 735, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108177, "dur": 1, "ph": "X", "name": "ProcessMessages 1466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108179, "dur": 79, "ph": "X", "name": "ReadAsync 1466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108261, "dur": 4, "ph": "X", "name": "ProcessMessages 8159", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108266, "dur": 27, "ph": "X", "name": "ReadAsync 8159", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108306, "dur": 21, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108329, "dur": 103, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108434, "dur": 1, "ph": "X", "name": "ProcessMessages 1648", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108456, "dur": 23, "ph": "X", "name": "ReadAsync 1648", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108495, "dur": 2, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108498, "dur": 37, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108537, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108540, "dur": 45, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108586, "dur": 1, "ph": "X", "name": "ProcessMessages 1350", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108589, "dur": 31, "ph": "X", "name": "ReadAsync 1350", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108621, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108624, "dur": 113, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108739, "dur": 1, "ph": "X", "name": "ProcessMessages 1836", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108741, "dur": 25, "ph": "X", "name": "ReadAsync 1836", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108778, "dur": 23, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108803, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108806, "dur": 44, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108863, "dur": 2, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108867, "dur": 36, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108914, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108915, "dur": 55, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248108973, "dur": 26, "ph": "X", "name": "ReadAsync 1360", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109002, "dur": 32, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109036, "dur": 37, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109077, "dur": 34, "ph": "X", "name": "ReadAsync 1288", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109113, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109123, "dur": 23, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109158, "dur": 21, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109183, "dur": 36, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109230, "dur": 23, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109257, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109259, "dur": 183, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109444, "dur": 2, "ph": "X", "name": "ProcessMessages 3987", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109446, "dur": 35, "ph": "X", "name": "ReadAsync 3987", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109483, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109486, "dur": 68, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109564, "dur": 1, "ph": "X", "name": "ProcessMessages 1720", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109566, "dur": 47, "ph": "X", "name": "ReadAsync 1720", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109632, "dur": 2, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109635, "dur": 32, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109669, "dur": 1, "ph": "X", "name": "ProcessMessages 1298", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109670, "dur": 53, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109725, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109729, "dur": 50, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109788, "dur": 1, "ph": "X", "name": "ProcessMessages 1199", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109791, "dur": 45, "ph": "X", "name": "ReadAsync 1199", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109842, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109845, "dur": 45, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109908, "dur": 1, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109911, "dur": 67, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109980, "dur": 2, "ph": "X", "name": "ProcessMessages 2135", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248109986, "dur": 49, "ph": "X", "name": "ReadAsync 2135", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110037, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110039, "dur": 41, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110082, "dur": 1, "ph": "X", "name": "ProcessMessages 1233", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110095, "dur": 30, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110126, "dur": 1, "ph": "X", "name": "ProcessMessages 1229", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110128, "dur": 25, "ph": "X", "name": "ReadAsync 1229", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110155, "dur": 25, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110183, "dur": 30, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110215, "dur": 28, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110244, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110246, "dur": 82, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110329, "dur": 1, "ph": "X", "name": "ProcessMessages 2495", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110331, "dur": 33, "ph": "X", "name": "ReadAsync 2495", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110366, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110369, "dur": 32, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110402, "dur": 1, "ph": "X", "name": "ProcessMessages 1138", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110404, "dur": 22, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110429, "dur": 207, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110667, "dur": 28, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110696, "dur": 1, "ph": "X", "name": "ProcessMessages 1954", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248110698, "dur": 624, "ph": "X", "name": "ReadAsync 1954", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111337, "dur": 34, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111373, "dur": 2, "ph": "X", "name": "ProcessMessages 3419", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111385, "dur": 57, "ph": "X", "name": "ReadAsync 3419", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111445, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111448, "dur": 340, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111792, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248111793, "dur": 521, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248112317, "dur": 544, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248112864, "dur": 450, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248113316, "dur": 7, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248113324, "dur": 469, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248113796, "dur": 136, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248113933, "dur": 446, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248114382, "dur": 553, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248114938, "dur": 100, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248115039, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248115050, "dur": 2912, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248117968, "dur": 2, "ph": "X", "name": "ProcessMessages 2713", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248117972, "dur": 288, "ph": "X", "name": "ReadAsync 2713", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118266, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118269, "dur": 46, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118318, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118320, "dur": 94, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118416, "dur": 217, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118636, "dur": 1, "ph": "X", "name": "ProcessMessages 1203", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118638, "dur": 28, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118680, "dur": 26, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248118723, "dur": 284, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119010, "dur": 26, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119037, "dur": 140, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119180, "dur": 219, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119402, "dur": 207, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119611, "dur": 154, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119767, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119769, "dur": 33, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119804, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248119824, "dur": 242, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120070, "dur": 201, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120274, "dur": 173, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120476, "dur": 1, "ph": "X", "name": "ProcessMessages 1162", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120478, "dur": 44, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120524, "dur": 174, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120701, "dur": 182, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120886, "dur": 2, "ph": "X", "name": "ProcessMessages 1161", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120889, "dur": 44, "ph": "X", "name": "ReadAsync 1161", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120934, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120936, "dur": 47, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248120985, "dur": 240, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121228, "dur": 101, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121332, "dur": 110, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121444, "dur": 207, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121654, "dur": 34, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121690, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121692, "dur": 33, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248121727, "dur": 347, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122076, "dur": 203, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122283, "dur": 54, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122339, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122341, "dur": 243, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122586, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122588, "dur": 252, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122865, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248122867, "dur": 174, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123043, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123044, "dur": 38, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123084, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123085, "dur": 37, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123124, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123126, "dur": 33, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123161, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123178, "dur": 393, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123573, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123575, "dur": 245, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123822, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123823, "dur": 43, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248123870, "dur": 378, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248124259, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248124261, "dur": 76, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248124362, "dur": 241, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248124607, "dur": 167, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248124778, "dur": 9, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248124789, "dur": 222, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125013, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125015, "dur": 85, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125103, "dur": 34, "ph": "X", "name": "ReadAsync 916", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125139, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125169, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125187, "dur": 46, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125235, "dur": 140, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125383, "dur": 1, "ph": "X", "name": "ProcessMessages 1556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125385, "dur": 41, "ph": "X", "name": "ReadAsync 1556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125428, "dur": 3, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125432, "dur": 464, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125898, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248125900, "dur": 221, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126123, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126125, "dur": 206, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126345, "dur": 25, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126372, "dur": 220, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126604, "dur": 85, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126693, "dur": 73, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126767, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126770, "dur": 53, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126825, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248126826, "dur": 185, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127014, "dur": 284, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127299, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127310, "dur": 39, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127351, "dur": 181, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127537, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127539, "dur": 278, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127819, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248127820, "dur": 360, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128183, "dur": 69, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128254, "dur": 1, "ph": "X", "name": "ProcessMessages 1145", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128256, "dur": 39, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128297, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128298, "dur": 113, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128413, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128442, "dur": 49, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128492, "dur": 1, "ph": "X", "name": "ProcessMessages 1471", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248128494, "dur": 533, "ph": "X", "name": "ReadAsync 1471", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129029, "dur": 235, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129265, "dur": 291, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129560, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129658, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129660, "dur": 34, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129696, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129698, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129748, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129868, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129922, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248129958, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130067, "dur": 101, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130170, "dur": 132, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130324, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130326, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130380, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130413, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130415, "dur": 191, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130608, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130648, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130650, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130704, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130885, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130886, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248130923, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131050, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131095, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131188, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131303, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131348, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131392, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131457, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131512, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131514, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131548, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131587, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131643, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131714, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131716, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131762, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131812, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131862, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131909, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248131960, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132005, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132086, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132130, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132183, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132221, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132225, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132278, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132280, "dur": 91, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132373, "dur": 52, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132484, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132548, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132595, "dur": 204, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132801, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132803, "dur": 130, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132944, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132946, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248132994, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248133059, "dur": 1077, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134138, "dur": 2, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134141, "dur": 41, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134184, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134186, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134287, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134351, "dur": 78, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134432, "dur": 82, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134517, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134597, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134705, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134754, "dur": 154, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134923, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134924, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248134963, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135053, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135144, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135146, "dur": 60, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135210, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135212, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135296, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135297, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135337, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135378, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135423, "dur": 53, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135479, "dur": 116, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135597, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135599, "dur": 51, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135652, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135654, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135691, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135720, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135866, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135868, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135904, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248135956, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136016, "dur": 172, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136208, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136248, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136250, "dur": 78, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136329, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136331, "dur": 105, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136438, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136479, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248136638, "dur": 13081, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248149723, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248149725, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248149831, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248149833, "dur": 1102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248150938, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248151110, "dur": 784, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248151896, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248151898, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248152033, "dur": 2987, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248155023, "dur": 2709, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248157735, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248157737, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248157774, "dur": 125, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752931248157914, "dur": 3280, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249578311, "dur": 644, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 8589934592, "ts": 1752931248083452, "dur": 70253, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752931248153708, "dur": 17, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752931248153727, "dur": 1810, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249578958, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931248033033, "dur": 129187, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931248039422, "dur": 35156, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931248162318, "dur": 672531, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931248835012, "dur": 719348, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931248835424, "dur": 11491, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931249554514, "dur": 3509, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931249556919, "dur": 274, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752931249558026, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249578965, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752931248858615, "dur": 117849, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248976470, "dur": 134, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248976670, "dur": 122, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248976799, "dur": 9414, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248986219, "dur": 566037, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931249552439, "dur": 456, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752931248976735, "dur": 9497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248986235, "dur": 2492, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248988732, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248988844, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248989024, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989122, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248989292, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989360, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989444, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989520, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989612, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989686, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248989823, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248989964, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990076, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990192, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990295, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990426, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990525, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990674, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990771, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248990880, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991004, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991128, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991276, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991381, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991484, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991594, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991653, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991751, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991862, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248991944, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248992088, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248992235, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248992351, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248993884, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248995256, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248996240, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248997214, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248998048, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248998780, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248999931, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249001083, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249002122, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249003268, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249004371, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249005911, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249006844, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249008231, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249009795, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249010945, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249012060, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249013317, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249014584, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249015822, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249016862, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249018056, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249019265, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249020039, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249021074, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249021161, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249021424, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249021750, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249022113, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249022180, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249024834, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249025022, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249025300, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249027362, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249027550, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_EF3566CB9F379C74.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249027745, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249027864, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249028489, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249028561, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249030163, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249030371, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249030752, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249033062, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249033251, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_C99541089A8295F6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249033360, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249033553, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931249033867, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249035103, "dur": 109421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249144527, "dur": 4558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249149085, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249149193, "dur": 5081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249154334, "dur": 4824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249159211, "dur": 3963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249163174, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249163258, "dur": 8356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752931249171614, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249172180, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752931249172337, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249173177, "dur": 919, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249174140, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752931249174259, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249174658, "dur": 1584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249176242, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752931249176303, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752931249176577, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249176793, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752931249176855, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Wx.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752931249176907, "dur": 1427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249178340, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249178832, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249178889, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752931249178995, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249179177, "dur": 2705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249181882, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931249182722, "dur": 369544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248976740, "dur": 9504, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248986293, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248986449, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248986534, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248986681, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248986784, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248986939, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248987034, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248987196, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248987402, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248987505, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248987663, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248987753, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248987974, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248988128, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248988229, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248988353, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248988440, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248988631, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248988728, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248988828, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248988930, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248989107, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248989161, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248989318, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248989389, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248989482, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248989593, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248989662, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248989817, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248989930, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990036, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990142, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990267, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990395, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990492, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990631, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990735, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990800, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248990941, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991075, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991200, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991332, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991421, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991521, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991668, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991771, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991868, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248991978, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248992079, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248992262, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248992379, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248993741, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248995030, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248996115, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248996922, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248997834, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248998570, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248999552, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249000784, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249001818, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249002970, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249004002, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249005543, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249006579, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249007686, "dur": 1629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249009315, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752931249009371, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249009450, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249010653, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249011780, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249012949, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249014246, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249015521, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249016592, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249017693, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249018940, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249020069, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249021159, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249021443, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249021834, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249022318, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249022846, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249025172, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249025266, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249026278, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249026420, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249026625, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249027086, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249027728, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249027780, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249028526, "dur": 4119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249032645, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249032923, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249033382, "dur": 1738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249035143, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249035214, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249035524, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249035578, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249035789, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931249035853, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249036274, "dur": 267333, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249304779, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752931249304467, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249305006, "dur": 62, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249305078, "dur": 129516, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249435367, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752931249435352, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249437109, "dur": 191, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249547994, "dur": 398, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931249437309, "dur": 111093, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752931249551604, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752931249551597, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752931249551683, "dur": 459, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752931249552145, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248976741, "dur": 9516, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248986300, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248986456, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248986570, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248986698, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248986897, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248986989, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248987156, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248987275, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248987427, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248987527, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248987708, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248987847, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248987966, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248988113, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248988250, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248988348, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248988503, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248988633, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248988789, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248988915, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248989100, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989181, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248989321, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989402, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989499, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989579, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989642, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989727, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989847, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248989972, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990079, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990206, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990328, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990439, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990541, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990678, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990758, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990863, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248990978, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991107, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991253, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991369, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991453, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991572, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991640, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991728, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991824, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991890, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248991996, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248992094, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248992162, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248992276, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248993688, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248994973, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248996081, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248997031, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248997927, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248998662, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248999745, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249000894, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249001934, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249003079, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249004111, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249005698, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249006689, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249007907, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249009591, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249010765, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249011864, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249013060, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249014367, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249015643, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249016680, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249017782, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249019068, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249019606, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249020673, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249021097, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249021162, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249021426, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249021784, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931249022444, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249022563, "dur": 2737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249025301, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249025450, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931249026586, "dur": 2156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249028742, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249028909, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931249029158, "dur": 1492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249030650, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249030744, "dur": 1102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249031846, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249031938, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931249032628, "dur": 1573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249034201, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249034532, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249034597, "dur": 1193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249035791, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931249035870, "dur": 109580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249145454, "dur": 5953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249151409, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249151528, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249156197, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249156307, "dur": 6620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249162928, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249163011, "dur": 18615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249181627, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249181869, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249181923, "dur": 122556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249304517, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752931249304481, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249306561, "dur": 208, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249447242, "dur": 490, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931249306787, "dur": 140954, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752931249451000, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752931249450988, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752931249451207, "dur": 447, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752931249451656, "dur": 100605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248976742, "dur": 9531, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248986274, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248986503, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248986687, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248986807, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248986958, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248987109, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248987206, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248987399, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248987487, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248987641, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248987764, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248987929, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248987995, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248988149, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248988258, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248988403, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248988596, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248988712, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248988863, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248988999, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248989123, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248989266, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248989371, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248989458, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248989587, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248989650, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248989791, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248989922, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990037, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990168, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990276, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990419, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990508, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990653, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990743, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990821, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248990954, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991095, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991223, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991343, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991441, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991548, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991630, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991706, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991816, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991897, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248991958, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248992071, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248992201, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248992327, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248993660, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248994935, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248996050, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248996984, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248997883, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248998619, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248999642, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249000843, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249001882, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249003029, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249004055, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249005629, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249006632, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249007801, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249009456, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249010655, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249011788, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249012970, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249014303, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249015568, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249016622, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249017726, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249019017, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249019910, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249020939, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249021059, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249021163, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249021427, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249021771, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931249022246, "dur": 5101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249027347, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249027626, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931249028007, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249028071, "dur": 3843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249031915, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249032098, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_3B0260CCB4011188.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931249032197, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249032368, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931249033003, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249034164, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249034387, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752931249034564, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249035145, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931249035207, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249035473, "dur": 107426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249142941, "dur": 4625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249147566, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249147656, "dur": 5134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249152790, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249152885, "dur": 3895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249156781, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249156884, "dur": 4701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249161585, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249161667, "dur": 4503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249166171, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931249166266, "dur": 16392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752931249182706, "dur": 369563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248976791, "dur": 9486, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248986278, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248986498, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248986646, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248986760, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248986971, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248987129, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248987240, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248987462, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248987632, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248987720, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248987891, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248987958, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248988141, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248988226, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248988360, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248988453, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248988636, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248988714, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248988917, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248989033, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248989143, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248989260, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248989354, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248989449, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248989549, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248989622, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248989700, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248989878, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990022, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990134, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990249, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990378, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990472, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990563, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990701, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990777, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248990916, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991042, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991155, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991308, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991401, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991505, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991607, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991691, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991782, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248991877, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248992002, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248992139, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248992268, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248992377, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248993744, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248995015, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248996110, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248997028, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248997916, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248998651, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248999743, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249000882, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249001914, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249003058, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249004088, "dur": 1577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249005665, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249006664, "dur": 1210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249007874, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249009516, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249010702, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249011814, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249012991, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249014324, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249015609, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249016661, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249017762, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249019051, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249019609, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249020661, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249021117, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249021167, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249021428, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249021793, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249022403, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249023634, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249023768, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249023834, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249024518, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249024615, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249026951, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249027130, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_C193AA5F93FF728B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249027220, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249027952, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249028632, "dur": 1250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249029882, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249030018, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249030077, "dur": 1876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249031954, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249032183, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249032498, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249032699, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249032758, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931249033125, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249033199, "dur": 1679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249034914, "dur": 108005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249142926, "dur": 2691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249145617, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249145819, "dur": 4968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249150787, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249150908, "dur": 5480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249156388, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249156482, "dur": 4749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249161231, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249161335, "dur": 3913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249165249, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931249165346, "dur": 17129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752931249182506, "dur": 369735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248976792, "dur": 9495, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248986288, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248986512, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248986685, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248986794, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248986939, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248987028, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248987162, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248987315, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248987452, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248987569, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248987717, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248987892, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248987963, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248988163, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248988246, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248988371, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248988484, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248988666, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248988769, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248988874, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248989132, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248989307, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989369, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989448, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989567, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989645, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989734, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989874, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248989978, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990111, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990220, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990358, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990454, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990613, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990725, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990793, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248990929, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991063, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991186, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991323, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991427, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991526, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991669, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991789, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248991886, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248992000, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248992102, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248992192, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248992297, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248993693, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248994989, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248996092, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248997026, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248997905, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248998640, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248999677, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249000866, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249001899, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249003046, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249004067, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249005635, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249006641, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249007810, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249009462, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249010661, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249011782, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249012942, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249014289, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249015547, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249016611, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249017721, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249019002, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249020106, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249021148, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249021446, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249021800, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931249022287, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249022365, "dur": 1865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249024231, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249024394, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931249025086, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931249025522, "dur": 1327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249026850, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249027009, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931249027492, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931249028193, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249028253, "dur": 1577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249029831, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249029978, "dur": 3085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249033063, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249033320, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249034462, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249034626, "dur": 108252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249142887, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249146029, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249146147, "dur": 4621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249150769, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249150927, "dur": 4885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249155813, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249155914, "dur": 4938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249160852, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249160964, "dur": 5828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752931249166793, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249166952, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249167014, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249167074, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249167203, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249167470, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249167688, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249167952, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249168016, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249168169, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249168255, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249168346, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249168707, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249169178, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249169406, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249169590, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249169701, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249169875, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249170310, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249170776, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249170871, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249171279, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249171410, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249171737, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249172098, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249172198, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249172601, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752931249172659, "dur": 1559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249174270, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249174373, "dur": 2035, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249176413, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249176637, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249176892, "dur": 1306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249178209, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249178606, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249178981, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249179124, "dur": 1546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249180673, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752931249180751, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249180823, "dur": 1677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931249182514, "dur": 369766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248976793, "dur": 9527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248986321, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248986464, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248986520, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248986703, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248986899, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248986998, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248987156, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248987248, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248987457, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248987638, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248987734, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248987916, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248988036, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248988197, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248988289, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248988397, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248988519, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248988696, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248988856, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248988969, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248989119, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248989229, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248989338, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248989425, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248989526, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248989607, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248989670, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248989823, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248989944, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990052, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990198, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990316, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990438, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990559, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990683, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990763, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990856, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248990972, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991104, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991235, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991353, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991449, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991563, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991634, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991717, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991823, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248991922, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248992690, "dur": 182, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752931248992872, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248994080, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248995469, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248996386, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248997339, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248998148, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248998885, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249000062, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249001228, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249002287, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249003432, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249004558, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249006036, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249006967, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249008390, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249009943, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249011055, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249012203, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249013495, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249014753, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249015976, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249017034, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249018235, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249019470, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249020511, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249021169, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249021435, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249021803, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249022457, "dur": 1963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249024420, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249024575, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_4404C737633913B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249024682, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249024818, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249025599, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249025834, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249027062, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249027191, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249027265, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249027893, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249027988, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249028486, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249028543, "dur": 1910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249030453, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249030599, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249030965, "dur": 1675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249032640, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249033013, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931249033504, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249035108, "dur": 107778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249142889, "dur": 4950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249147839, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249147938, "dur": 5852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249153790, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249153890, "dur": 4367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249158257, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249158358, "dur": 3992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249162350, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249162435, "dur": 5734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752931249168169, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249168579, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249168801, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249169035, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249169299, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249169591, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249169796, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249169945, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249170135, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249170308, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249170427, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752931249170751, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249170870, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249170969, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249171158, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752931249171311, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249171389, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249171473, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249171549, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249172037, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249172142, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249172640, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249172701, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249172782, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752931249173720, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249174336, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249174733, "dur": 1851, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249176590, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249176775, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249176835, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249176885, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249176959, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249177595, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249178587, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249178913, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752931249179011, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249179252, "dur": 2806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249182103, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249182170, "dur": 369451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931249551622, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752931249551700, "dur": 489, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752931248976791, "dur": 9489, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248986284, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248986451, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248986538, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248986642, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248986752, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248986934, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248987016, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248987161, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248987312, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248987430, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248987554, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248987698, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248987808, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248987950, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248988070, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248988207, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248988298, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248988423, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248988601, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248988719, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248988918, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248989051, "dur": 3752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248992804, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248992886, "dur": 15861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249008747, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249009022, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931249009155, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249009276, "dur": 3070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931249012388, "dur": 8978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249021408, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931249021471, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249021748, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931249021950, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249022017, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249023645, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249023904, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931249024332, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249024409, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249026285, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249026494, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1752931249029126, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249029195, "dur": 567, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249141219, "dur": 394, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249029768, "dur": 111868, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1752931249142877, "dur": 2739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249145616, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249145756, "dur": 4803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249150559, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249150732, "dur": 4556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249155288, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249155401, "dur": 5087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249160489, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249160595, "dur": 4541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249165136, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931249165252, "dur": 16921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752931249182245, "dur": 370002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248976794, "dur": 9517, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248986311, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248986507, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248986644, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248986721, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248986908, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248987011, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248987179, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248987393, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248987479, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248987655, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248987782, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248987949, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248988063, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248988188, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248988271, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248988388, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248988460, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248988640, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248988734, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248988830, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248989019, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248989116, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248989267, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248989345, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248989436, "dur": 4190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248993627, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248993682, "dur": 13964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249007647, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249007904, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931249008007, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249008090, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249009700, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249010834, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249011944, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249013219, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249014510, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249015759, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249016791, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249017968, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249019190, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249019755, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249020786, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249021064, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249021167, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249021430, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249021806, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931249022531, "dur": 1335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249023867, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249024022, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249026668, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249026841, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931249027820, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249029617, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249029720, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249031456, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249031644, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249033564, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249033679, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931249033941, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249035073, "dur": 107855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249143687, "dur": 6594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249150296, "dur": 7179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249157475, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249157555, "dur": 5762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249163317, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249163394, "dur": 18546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752931249181941, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249182101, "dur": 268895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931249451008, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752931249450996, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752931249451125, "dur": 537, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752931249451663, "dur": 100574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248976798, "dur": 9520, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248986318, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248986500, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248986649, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248986747, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248986931, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248987004, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248987163, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248987324, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248987464, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248987638, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248987747, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248987931, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248988006, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248988148, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248988220, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248988349, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248988430, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248988631, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248988761, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248988861, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248989074, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989147, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248989308, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989384, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989464, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989579, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989650, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989749, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989851, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248989993, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990122, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990231, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990366, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990466, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990595, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990714, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990786, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248990898, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991040, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991146, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991296, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991387, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991495, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991610, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991673, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991809, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248991892, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248992009, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248992150, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248992304, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248993672, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248994943, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248996058, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248996991, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248997876, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248998610, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248999645, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249000837, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249001880, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249003000, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249004026, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249005589, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249006605, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249007751, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249009450, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249009536, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249010711, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249011824, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249013003, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249014333, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249015617, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249016669, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249017779, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249019062, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249019616, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249020682, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249021064, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249021160, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249021431, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249021977, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931249022777, "dur": 1643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249024420, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249024566, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_1A4EF4AE609B2576.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931249024757, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931249025697, "dur": 4382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249030080, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249030213, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249031406, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249031536, "dur": 3259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249034873, "dur": 108035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249142918, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249145418, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249145493, "dur": 5038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249150533, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249150686, "dur": 7383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249158069, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249158163, "dur": 5868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249164032, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249164127, "dur": 15862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752931249179990, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249180660, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249180767, "dur": 1450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931249182262, "dur": 370009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931249554185, "dur": 512, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1752931248293049, "dur": 527720, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752931248293909, "dur": 118185, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752931248759026, "dur": 3191, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752931248762218, "dur": 58541, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752931248762826, "dur": 42049, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752931248826113, "dur": 984, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752931248825809, "dur": 1450, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752931248088984, "dur": 1913, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248090934, "dur": 13041, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248104054, "dur": 106, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248104826, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752931248105468, "dur": 164, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752931248109035, "dur": 177, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752931248104166, "dur": 25762, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248129935, "dur": 27389, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248157324, "dur": 199, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248157568, "dur": 1041, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248158687, "dur": 616, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752931248104100, "dur": 25855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248129980, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752931248130155, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248130354, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248130432, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248130525, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248130633, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248130752, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248130821, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248130952, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248131047, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248131184, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248131304, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248131461, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248131568, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248131711, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248131803, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248131945, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248132067, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752931248132200, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248132324, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752931248132866, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248132944, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752931248133019, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248133210, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752931248133378, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248133451, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752931248133741, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248133826, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752931248134149, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248134266, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248134413, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248134499, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752931248134729, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248134796, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248134855, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752931248134919, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752931248135054, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248135169, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248135315, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248135456, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752931248135637, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248135765, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752931248136007, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136157, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136256, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136358, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136472, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136616, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136742, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136844, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248136923, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248137000, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248137057, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248137135, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248137250, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248138722, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248139855, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248141152, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248142919, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248144512, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248145861, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248147255, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248148441, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248149793, "dur": 1590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248151384, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248153283, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248154527, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752931248155964, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248104101, "dur": 25862, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248129986, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752931248130153, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248130464, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248130559, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248130708, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248130789, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248130920, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248131024, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248131170, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248131293, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248131439, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248131536, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248131732, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248131873, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248131964, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248132162, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248132275, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752931248132442, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248132567, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248132710, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248132815, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248132936, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248133083, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248133188, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752931248133483, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248133679, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752931248133817, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248133902, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752931248134040, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248134170, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248134230, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752931248134692, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248134763, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248134957, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248135035, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248135124, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752931248135535, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248135663, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248135769, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248135844, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248135946, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136117, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136241, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136341, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136454, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136593, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752931248136749, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136821, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136908, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248136979, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248137041, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248137115, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248137257, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248137346, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248138795, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248139945, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248141276, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248143051, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248144562, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248145944, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248147331, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248148522, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248149865, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248151484, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248153331, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248154577, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752931248156059, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248104108, "dur": 25864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248129980, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248130323, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248130433, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248130506, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248130679, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248130746, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248130894, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248130985, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248131123, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248131201, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248131401, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248131496, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248131661, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248131725, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248131864, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248131943, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248132109, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248132204, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248132441, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248132615, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248132725, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752931248132872, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248132958, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752931248133116, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248133251, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752931248133376, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248133432, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752931248133768, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248133889, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248133973, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752931248134219, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248134324, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752931248134940, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248134993, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135091, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135198, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135351, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135464, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752931248135616, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135731, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135822, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248135912, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248136043, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248136178, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248136285, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752931248136504, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248136636, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752931248136814, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248136885, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248136947, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248137021, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248137083, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248137170, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248137293, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248138726, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248139831, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248141128, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248142896, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248144505, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248145844, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248147236, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248148416, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248149761, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248151332, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248153212, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248154479, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752931248155921, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248104117, "dur": 25860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248129979, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248130360, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248130501, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248130587, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248130736, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248130804, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248130944, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248131039, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248131174, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248131329, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248131463, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248131588, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248131744, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248131871, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248131977, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248132143, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248132243, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248132414, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248132492, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248132603, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248132737, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248132845, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752931248132963, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248133081, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248133174, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248133268, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752931248133379, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248133471, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752931248133730, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248133873, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248133954, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752931248134131, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752931248134185, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248134653, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248134724, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248134830, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248134880, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248134967, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135074, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135219, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135331, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248135399, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135530, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135668, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135791, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248135887, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136008, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136170, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136274, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136376, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136501, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136654, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248136796, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248136869, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752931248137129, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248137250, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248137342, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248138785, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248139905, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248141220, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248142944, "dur": 1575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248144519, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248145847, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248147240, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248148435, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248149776, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248151363, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248153266, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248154501, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752931248155942, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248104125, "dur": 25856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248129984, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248130346, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248130408, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248130527, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248130651, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248130757, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248130858, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248130987, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248131087, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248131198, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248131337, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248131462, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248131579, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248131709, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248131796, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248131931, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248132049, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248132197, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248132304, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752931248132503, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248132618, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248133574, "dur": 17927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752931248151501, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248151815, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248151884, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248152009, "dur": 3810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752931248155820, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752931248155920, "dur": 2634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752931248104132, "dur": 25861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248129996, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248130332, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248130455, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248130525, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248130680, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248130753, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248130889, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248130970, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248131169, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248131267, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248131438, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248131556, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248131741, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248131871, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248131958, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248132142, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248132250, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248132410, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248132514, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248132657, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248132775, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248132897, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248133017, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248133120, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248133246, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752931248133355, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248133493, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248133565, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752931248133662, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752931248133800, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248133922, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752931248134207, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248134369, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248134428, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752931248134667, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752931248134728, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248134788, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248134876, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752931248134974, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135065, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135179, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135291, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135449, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135551, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752931248135690, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135795, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135870, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248135956, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248136053, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248136200, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248136302, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248136400, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248136524, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752931248136798, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752931248137119, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248137227, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248137330, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248138778, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248139922, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248141261, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248142806, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248144457, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248145777, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248147172, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248148364, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248149708, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248151294, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248153148, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248154396, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752931248155815, "dur": 1609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248104139, "dur": 25859, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248130001, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248130297, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248130508, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248130610, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248130752, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248130845, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248130977, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248131073, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248131199, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248131345, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248131463, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248131585, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248131708, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248131782, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248131923, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248132012, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248132172, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248132287, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752931248132440, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248132536, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248132694, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248132763, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248132912, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248133021, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248133177, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752931248133306, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752931248133681, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248133813, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752931248134082, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248134173, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752931248134295, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248134559, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752931248134659, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248134741, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248134885, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752931248135006, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135111, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135225, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135326, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752931248135485, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135648, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135754, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135842, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248135931, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248136077, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248136204, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248136308, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248136400, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248136514, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248136658, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752931248136936, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248137013, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248137078, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248137156, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248137283, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248138757, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248139888, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248141209, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248142988, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248144526, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248145891, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248147286, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248148453, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248149813, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248151430, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248153307, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248154571, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752931248156034, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248104147, "dur": 25860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248130010, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248130312, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248130378, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248130501, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248130574, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248130889, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248130963, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248131130, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248131250, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248131424, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248131523, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248131754, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248131885, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248131966, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248132142, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248132216, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248132390, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248132486, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248132671, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248132753, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248132897, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248133001, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248133106, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752931248133294, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248133638, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752931248133804, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248133884, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248133942, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752931248134051, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248134141, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752931248134194, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248134320, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248134497, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248134593, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248134691, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248134765, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248134848, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752931248134949, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248135024, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248135160, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248135259, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248135350, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248135505, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248135641, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248135760, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248136075, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248136193, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248136339, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248136433, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248136552, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248136679, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248136766, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752931248136920, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248136991, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248137064, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248137141, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248137276, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248138724, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248139839, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248141140, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248142859, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248144496, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248145833, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248147225, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248148411, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248149753, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248151358, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248153229, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248154490, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752931248155931, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248104154, "dur": 25858, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248130014, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248130352, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248130462, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248130541, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248130695, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248130771, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248130906, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248130999, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248131132, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248131218, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248131410, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248131502, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248131673, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248131765, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248131898, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248131988, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248132159, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248132265, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248132409, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248132519, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248132665, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248132797, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248132908, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752931248133082, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248133197, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248133381, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248133500, "dur": 16774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752931248150275, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248150554, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752931248150623, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248150712, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248152597, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752931248152647, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248152723, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752931248152794, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248152878, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248154164, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248155558, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752931248157204, "dur": 113, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248104161, "dur": 25856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248130017, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248130340, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248130454, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248130516, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248130659, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248130737, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248130871, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248130955, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248131103, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248131182, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248131371, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248131470, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248131651, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248131719, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248131864, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248131941, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248132123, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248132186, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248132355, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248132427, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248132583, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248132698, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752931248132829, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248132923, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248133093, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248133200, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248133385, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248133519, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248134195, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248134300, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248134481, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248134539, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248134784, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248134860, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248134975, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248135045, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248135143, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248135237, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248135395, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248135754, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248135832, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248135926, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136089, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136213, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136319, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136412, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136520, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248136579, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136715, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248136880, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248136941, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248137121, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248137228, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752931248137451, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248137515, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248138890, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248140069, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248141389, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248143132, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248144617, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248146029, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248147440, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248148591, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248149926, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248151651, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248153430, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248154688, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752931248156160, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752931248161122, "dur": 473, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 2845, "ts": 1752931249580042, "dur": 4113, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 39945, "tid": 2845, "ts": 1752931249585767, "dur": 40, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 39945, "tid": 2845, "ts": 1752931249585961, "dur": 5, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 2845, "ts": 1752931249584283, "dur": 1479, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249585846, "dur": 114, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249585991, "dur": 254, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 2845, "ts": 1752931249575177, "dur": 11890, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}