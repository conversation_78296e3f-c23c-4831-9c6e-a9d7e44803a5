{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 3277, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 3277, "ts": 1752942434273178, "dur": 97, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434273338, "dur": 14, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 167503724544, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433840702, "dur": 13624, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433854327, "dur": 418107, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433854409, "dur": 74, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433854519, "dur": 7, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433854527, "dur": 122614, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433977152, "dur": 51, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433977206, "dur": 705, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433977917, "dur": 3, "ph": "X", "name": "ProcessMessages 2740", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433977921, "dur": 45, "ph": "X", "name": "ReadAsync 2740", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433977985, "dur": 1, "ph": "X", "name": "ProcessMessages 1450", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433977987, "dur": 40, "ph": "X", "name": "ReadAsync 1450", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978041, "dur": 1, "ph": "X", "name": "ProcessMessages 1376", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978044, "dur": 41, "ph": "X", "name": "ReadAsync 1376", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978098, "dur": 1, "ph": "X", "name": "ProcessMessages 1869", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978100, "dur": 41, "ph": "X", "name": "ReadAsync 1869", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978152, "dur": 1, "ph": "X", "name": "ProcessMessages 1771", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978155, "dur": 43, "ph": "X", "name": "ReadAsync 1771", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978200, "dur": 1, "ph": "X", "name": "ProcessMessages 1958", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978203, "dur": 40, "ph": "X", "name": "ReadAsync 1958", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978245, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978261, "dur": 61, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978323, "dur": 2, "ph": "X", "name": "ProcessMessages 2453", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978326, "dur": 66, "ph": "X", "name": "ReadAsync 2453", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978394, "dur": 1, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978396, "dur": 41, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978448, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978450, "dur": 35, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978487, "dur": 1, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978488, "dur": 49, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978539, "dur": 1, "ph": "X", "name": "ProcessMessages 1682", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978541, "dur": 43, "ph": "X", "name": "ReadAsync 1682", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978585, "dur": 1, "ph": "X", "name": "ProcessMessages 1888", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978587, "dur": 56, "ph": "X", "name": "ReadAsync 1888", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978664, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978666, "dur": 41, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978708, "dur": 1, "ph": "X", "name": "ProcessMessages 2452", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978710, "dur": 32, "ph": "X", "name": "ReadAsync 2452", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978745, "dur": 30, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978778, "dur": 44, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978825, "dur": 77, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978903, "dur": 1, "ph": "X", "name": "ProcessMessages 2573", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978904, "dur": 45, "ph": "X", "name": "ReadAsync 2573", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978952, "dur": 1, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978953, "dur": 38, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978993, "dur": 1, "ph": "X", "name": "ProcessMessages 1380", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433978995, "dur": 52, "ph": "X", "name": "ReadAsync 1380", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979049, "dur": 1, "ph": "X", "name": "ProcessMessages 1690", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979051, "dur": 44, "ph": "X", "name": "ReadAsync 1690", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979105, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979107, "dur": 40, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979149, "dur": 1, "ph": "X", "name": "ProcessMessages 1188", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979151, "dur": 228, "ph": "X", "name": "ReadAsync 1188", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979381, "dur": 1, "ph": "X", "name": "ProcessMessages 1630", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979383, "dur": 35, "ph": "X", "name": "ReadAsync 1630", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979420, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979421, "dur": 38, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979462, "dur": 33, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979509, "dur": 26, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979557, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979558, "dur": 24, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979585, "dur": 39, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979627, "dur": 36, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979665, "dur": 66, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979733, "dur": 25, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979761, "dur": 28, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979790, "dur": 26, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979819, "dur": 21, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979841, "dur": 43, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979886, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979888, "dur": 43, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979932, "dur": 1, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979934, "dur": 38, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979973, "dur": 15, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433979990, "dur": 62, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980053, "dur": 1, "ph": "X", "name": "ProcessMessages 1711", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980056, "dur": 38, "ph": "X", "name": "ReadAsync 1711", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980096, "dur": 44, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980148, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980149, "dur": 52, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980203, "dur": 1, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980205, "dur": 42, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980250, "dur": 28, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980281, "dur": 29, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980312, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980313, "dur": 21, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980336, "dur": 46, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980385, "dur": 20, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980407, "dur": 33, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980442, "dur": 21, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980464, "dur": 42, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980509, "dur": 32, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980543, "dur": 40, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980584, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980585, "dur": 18, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980605, "dur": 32, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980639, "dur": 55, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980697, "dur": 35, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980734, "dur": 18, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980765, "dur": 23, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980790, "dur": 20, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980812, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980836, "dur": 22, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980860, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980885, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980904, "dur": 33, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980940, "dur": 24, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980965, "dur": 18, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433980985, "dur": 24, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981011, "dur": 35, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981074, "dur": 18, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981093, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981095, "dur": 32, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981129, "dur": 26, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981157, "dur": 17, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981176, "dur": 23, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981201, "dur": 37, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981241, "dur": 21, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981264, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981285, "dur": 35, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981322, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981354, "dur": 22, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981378, "dur": 33, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981413, "dur": 31, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981446, "dur": 25, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981473, "dur": 19, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981495, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981517, "dur": 15, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981535, "dur": 73, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981609, "dur": 1, "ph": "X", "name": "ProcessMessages 1420", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981617, "dur": 34, "ph": "X", "name": "ReadAsync 1420", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981652, "dur": 112, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981767, "dur": 28, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981797, "dur": 24, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981823, "dur": 39, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981864, "dur": 28, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981894, "dur": 35, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981931, "dur": 24, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981956, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433981977, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982009, "dur": 58, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982069, "dur": 50, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982121, "dur": 8, "ph": "X", "name": "ProcessMessages 1043", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982129, "dur": 41, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982171, "dur": 1, "ph": "X", "name": "ProcessMessages 1365", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982172, "dur": 23, "ph": "X", "name": "ReadAsync 1365", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982197, "dur": 36, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982235, "dur": 32, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982268, "dur": 25, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982295, "dur": 43, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982340, "dur": 30, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982382, "dur": 32, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982415, "dur": 19, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982436, "dur": 34, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982472, "dur": 27, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982501, "dur": 21, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982524, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982550, "dur": 19, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982571, "dur": 30, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982603, "dur": 24, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982630, "dur": 37, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982669, "dur": 27, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982698, "dur": 19, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982720, "dur": 29, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982750, "dur": 53, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982806, "dur": 33, "ph": "X", "name": "ReadAsync 1340", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982841, "dur": 31, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982885, "dur": 43, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982930, "dur": 25, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433982957, "dur": 53, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983011, "dur": 1, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983013, "dur": 24, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983039, "dur": 48, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983090, "dur": 1, "ph": "X", "name": "ProcessMessages 1362", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983092, "dur": 31, "ph": "X", "name": "ReadAsync 1362", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983125, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983127, "dur": 32, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983160, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983162, "dur": 38, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983202, "dur": 42, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983248, "dur": 25, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983275, "dur": 37, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983315, "dur": 31, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983355, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983357, "dur": 30, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983388, "dur": 1, "ph": "X", "name": "ProcessMessages 1123", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983390, "dur": 31, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983423, "dur": 38, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983463, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983464, "dur": 37, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983503, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983505, "dur": 52, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983558, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983559, "dur": 25, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983587, "dur": 18, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983607, "dur": 37, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983647, "dur": 49, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983697, "dur": 27, "ph": "X", "name": "ReadAsync 1145", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983726, "dur": 26, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983754, "dur": 24, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983781, "dur": 46, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983830, "dur": 1, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983846, "dur": 32, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983880, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983909, "dur": 60, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983971, "dur": 1, "ph": "X", "name": "ProcessMessages 2554", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433983973, "dur": 33, "ph": "X", "name": "ReadAsync 2554", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984008, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984010, "dur": 31, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984043, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984044, "dur": 31, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984078, "dur": 38, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984118, "dur": 39, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984160, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984161, "dur": 105, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984268, "dur": 1, "ph": "X", "name": "ProcessMessages 1292", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984270, "dur": 27, "ph": "X", "name": "ReadAsync 1292", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984299, "dur": 45, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984346, "dur": 17, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984365, "dur": 63, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984434, "dur": 55, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984492, "dur": 71, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984575, "dur": 29, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984607, "dur": 28, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984637, "dur": 40, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984680, "dur": 87, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984769, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984771, "dur": 31, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984803, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984805, "dur": 78, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984886, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984887, "dur": 31, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984920, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984922, "dur": 68, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433984992, "dur": 47, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985040, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985042, "dur": 37, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985087, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985109, "dur": 31, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985142, "dur": 23, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985167, "dur": 34, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985204, "dur": 29, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985235, "dur": 31, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985268, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985290, "dur": 30, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985321, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985323, "dur": 32, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985357, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985359, "dur": 48, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985409, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985410, "dur": 32, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985445, "dur": 98, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985544, "dur": 1, "ph": "X", "name": "ProcessMessages 1551", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985546, "dur": 22, "ph": "X", "name": "ReadAsync 1551", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985570, "dur": 39, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985611, "dur": 26, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985641, "dur": 30, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985673, "dur": 29, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985705, "dur": 66, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985773, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985775, "dur": 48, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985824, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985826, "dur": 130, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985957, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433985959, "dur": 43, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986005, "dur": 38, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986045, "dur": 102, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986149, "dur": 1, "ph": "X", "name": "ProcessMessages 1317", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986150, "dur": 18, "ph": "X", "name": "ReadAsync 1317", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986170, "dur": 61, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986243, "dur": 40, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986285, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986287, "dur": 47, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986336, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986337, "dur": 31, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986372, "dur": 119, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986492, "dur": 1, "ph": "X", "name": "ProcessMessages 1548", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986493, "dur": 62, "ph": "X", "name": "ReadAsync 1548", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986557, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986558, "dur": 51, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986612, "dur": 33, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986647, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986649, "dur": 40, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986691, "dur": 2, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986701, "dur": 33, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986736, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986738, "dur": 50, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986790, "dur": 26, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986819, "dur": 56, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986877, "dur": 1, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986890, "dur": 91, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433986983, "dur": 40, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987026, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987028, "dur": 89, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987119, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987121, "dur": 44, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987168, "dur": 41, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987222, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987223, "dur": 32, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987257, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987259, "dur": 32, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987293, "dur": 21, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987323, "dur": 93, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987417, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987418, "dur": 44, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987464, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987466, "dur": 44, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987513, "dur": 29, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987544, "dur": 29, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987587, "dur": 38, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987627, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987629, "dur": 54, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987684, "dur": 1, "ph": "X", "name": "ProcessMessages 1419", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987686, "dur": 42, "ph": "X", "name": "ReadAsync 1419", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987730, "dur": 54, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987787, "dur": 134, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987923, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433987925, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988061, "dur": 95, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988173, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988324, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988326, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988370, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988445, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988563, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988627, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988701, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988841, "dur": 128, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433988971, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989020, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989051, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989110, "dur": 120, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989233, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989280, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989335, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989402, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989507, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989535, "dur": 124, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989663, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989766, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989806, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989858, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433989964, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990052, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990090, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990133, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990229, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990291, "dur": 76, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990371, "dur": 52, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990426, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990466, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990506, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990555, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990705, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990729, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990765, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990785, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433990968, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991011, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991012, "dur": 96, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991119, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991167, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991265, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991343, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991439, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991492, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991540, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991581, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991625, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991721, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991760, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991795, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991838, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991898, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433991962, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992004, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992071, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992101, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992146, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992178, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992212, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992233, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992328, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992378, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992420, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992517, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992545, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992621, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992652, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992735, "dur": 9, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992745, "dur": 100, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992848, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992893, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992896, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992936, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433992986, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993067, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993069, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993106, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993108, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993155, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993188, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993228, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993230, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993267, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993285, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993313, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993360, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993401, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993432, "dur": 51, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993484, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993521, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993598, "dur": 64, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993665, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993736, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993782, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993784, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993845, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993885, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993943, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993966, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433993987, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994020, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994077, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994135, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994137, "dur": 45, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994185, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994229, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994231, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994291, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994328, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994364, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994391, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994423, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994504, "dur": 8, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433994513, "dur": 651, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433995165, "dur": 77, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433995243, "dur": 232, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433995479, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942433995729, "dur": 16737, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434012499, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434012503, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434012683, "dur": 825, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434013512, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434013515, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434013655, "dur": 542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434014200, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434014201, "dur": 3496, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434017701, "dur": 9566, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434027273, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434027277, "dur": 572, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434027852, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434027930, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434027953, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434028075, "dur": 260, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434028339, "dur": 537, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434028879, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434028880, "dur": 309, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434029192, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434029194, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434029280, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434029384, "dur": 425, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434029812, "dur": 149, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434029964, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434030169, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434030245, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434030512, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434030513, "dur": 334, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434030912, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434030972, "dur": 313, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434031287, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434031288, "dur": 101, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434031391, "dur": 288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434031683, "dur": 244, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434031930, "dur": 374, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434032308, "dur": 218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434032529, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434032700, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434032733, "dur": 44, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434032779, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434032941, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033086, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033263, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033311, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033487, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033625, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033705, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434033888, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434034077, "dur": 159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434034251, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434034481, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434034694, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434034884, "dur": 218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434035105, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434035106, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434035229, "dur": 440, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434035672, "dur": 338, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434036023, "dur": 339, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434036364, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434036511, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434036693, "dur": 343, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037038, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037130, "dur": 363, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037495, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037497, "dur": 146, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037647, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037845, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434037983, "dur": 116, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434038102, "dur": 112, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434038218, "dur": 203, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434038423, "dur": 197, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434038623, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434038748, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434038949, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039165, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039167, "dur": 121, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039290, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039291, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039319, "dur": 335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039657, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039827, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039860, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434039954, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040007, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040106, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040188, "dur": 364, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040554, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040606, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040762, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434040827, "dur": 276, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041105, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041148, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041233, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041470, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041589, "dur": 317, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041914, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434041916, "dur": 116241, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158165, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158169, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158223, "dur": 61, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158286, "dur": 28, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158327, "dur": 22, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158351, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158390, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158436, "dur": 35, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434158472, "dur": 5066, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434163541, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434163543, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434163757, "dur": 500, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434164261, "dur": 361, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434164625, "dur": 2806, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434167436, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434167439, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434167534, "dur": 335, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434167871, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434168114, "dur": 718, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434168837, "dur": 1559, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434170405, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434170410, "dur": 568, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434170982, "dur": 530, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434171519, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434171522, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434171787, "dur": 609, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434172398, "dur": 1001, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434173401, "dur": 369, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434173775, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434173777, "dur": 1106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434174887, "dur": 946, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434175836, "dur": 347, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434176187, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434176244, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434176497, "dur": 814, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434177317, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434177476, "dur": 428, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434177907, "dur": 2101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434180012, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434180013, "dur": 1479, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434181503, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434181508, "dur": 314, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434181826, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434181828, "dur": 797, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434182628, "dur": 1794, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434184427, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434184429, "dur": 809, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434185254, "dur": 354, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434185611, "dur": 340, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434185954, "dur": 153, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434186110, "dur": 380, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434186494, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434186497, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434186559, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434186603, "dur": 1476, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434188081, "dur": 3, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434188085, "dur": 45, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434188147, "dur": 1217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434189367, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434189369, "dur": 2462, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434191834, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434191843, "dur": 964, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434192815, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434192821, "dur": 417, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193241, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193244, "dur": 82, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193332, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193335, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193406, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193407, "dur": 378, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193788, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193925, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434193997, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434194004, "dur": 268, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434194274, "dur": 271, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434194548, "dur": 238, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434194788, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434194789, "dur": 71561, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434266358, "dur": 27, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434266386, "dur": 1139, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434267528, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 167503724544, "ts": 1752942434267530, "dur": 4901, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434273353, "dur": 1104, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 163208757248, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 163208757248, "ts": 1752942433840534, "dur": 69, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 163208757248, "ts": 1752942433840604, "dur": 13723, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 163208757248, "ts": 1752942433854328, "dur": 125, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434274459, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752942432998837, "dur": 6010, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752942433004852, "dur": 32425, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752942433037279, "dur": 69774, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434274475, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 158913789952, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942432998641, "dur": 17628, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433016270, "dur": 99463, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433016362, "dur": 90, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433016464, "dur": 17, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433016481, "dur": 1580, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018085, "dur": 2, "ph": "X", "name": "ProcessMessages 2746", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018092, "dur": 50, "ph": "X", "name": "ReadAsync 2746", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018144, "dur": 1, "ph": "X", "name": "ProcessMessages 2384", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018146, "dur": 51, "ph": "X", "name": "ReadAsync 2384", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018201, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018204, "dur": 59, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018282, "dur": 1, "ph": "X", "name": "ProcessMessages 1926", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018285, "dur": 52, "ph": "X", "name": "ReadAsync 1926", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018364, "dur": 1, "ph": "X", "name": "ProcessMessages 2072", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018367, "dur": 119, "ph": "X", "name": "ReadAsync 2072", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018489, "dur": 2, "ph": "X", "name": "ProcessMessages 2532", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018492, "dur": 61, "ph": "X", "name": "ReadAsync 2532", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018556, "dur": 2, "ph": "X", "name": "ProcessMessages 3527", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018560, "dur": 48, "ph": "X", "name": "ReadAsync 3527", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018621, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018625, "dur": 56, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018682, "dur": 1, "ph": "X", "name": "ProcessMessages 1411", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018684, "dur": 43, "ph": "X", "name": "ReadAsync 1411", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018732, "dur": 1, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018735, "dur": 81, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018860, "dur": 2, "ph": "X", "name": "ProcessMessages 2750", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018863, "dur": 71, "ph": "X", "name": "ReadAsync 2750", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018954, "dur": 34, "ph": "X", "name": "ProcessMessages 2349", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433018993, "dur": 94, "ph": "X", "name": "ReadAsync 2349", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433019090, "dur": 4, "ph": "X", "name": "ProcessMessages 4968", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433019096, "dur": 38, "ph": "X", "name": "ReadAsync 4968", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433019175, "dur": 1, "ph": "X", "name": "ProcessMessages 1353", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433019177, "dur": 46, "ph": "X", "name": "ReadAsync 1353", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433019240, "dur": 1, "ph": "X", "name": "ProcessMessages 2045", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433019241, "dur": 1244, "ph": "X", "name": "ReadAsync 2045", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020487, "dur": 1, "ph": "X", "name": "ProcessMessages 1660", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020489, "dur": 41, "ph": "X", "name": "ReadAsync 1660", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020538, "dur": 5, "ph": "X", "name": "ProcessMessages 8149", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020545, "dur": 64, "ph": "X", "name": "ReadAsync 8149", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020631, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020633, "dur": 42, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020677, "dur": 3, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433020682, "dur": 359, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021051, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021055, "dur": 63, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021119, "dur": 3, "ph": "X", "name": "ProcessMessages 7057", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021186, "dur": 44, "ph": "X", "name": "ReadAsync 7057", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021232, "dur": 3, "ph": "X", "name": "ProcessMessages 1802", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021252, "dur": 47, "ph": "X", "name": "ReadAsync 1802", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021321, "dur": 1, "ph": "X", "name": "ProcessMessages 1488", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021322, "dur": 66, "ph": "X", "name": "ReadAsync 1488", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021391, "dur": 1, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021397, "dur": 39, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021440, "dur": 1, "ph": "X", "name": "ProcessMessages 1419", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021442, "dur": 482, "ph": "X", "name": "ReadAsync 1419", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021926, "dur": 1, "ph": "X", "name": "ProcessMessages 1327", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433021928, "dur": 94, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022023, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022030, "dur": 118, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022167, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022168, "dur": 421, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022604, "dur": 9, "ph": "X", "name": "ProcessMessages 1676", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022734, "dur": 209, "ph": "X", "name": "ReadAsync 1676", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022945, "dur": 4, "ph": "X", "name": "ProcessMessages 8123", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433022949, "dur": 95, "ph": "X", "name": "ReadAsync 8123", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433023149, "dur": 1, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433023150, "dur": 365, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433023579, "dur": 4, "ph": "X", "name": "ProcessMessages 4726", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433023584, "dur": 2538, "ph": "X", "name": "ReadAsync 4726", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433026305, "dur": 26, "ph": "X", "name": "ProcessMessages 8183", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433026536, "dur": 3370, "ph": "X", "name": "ReadAsync 8183", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433029911, "dur": 6, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433030747, "dur": 157, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433030907, "dur": 6, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433030919, "dur": 120, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031083, "dur": 2, "ph": "X", "name": "ProcessMessages 4240", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031086, "dur": 428, "ph": "X", "name": "ReadAsync 4240", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031519, "dur": 4, "ph": "X", "name": "ProcessMessages 4502", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031524, "dur": 111, "ph": "X", "name": "ReadAsync 4502", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031692, "dur": 5, "ph": "X", "name": "ProcessMessages 5021", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031698, "dur": 55, "ph": "X", "name": "ReadAsync 5021", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031758, "dur": 2, "ph": "X", "name": "ProcessMessages 2723", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031762, "dur": 51, "ph": "X", "name": "ReadAsync 2723", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031815, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433031817, "dur": 4951, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433036774, "dur": 5, "ph": "X", "name": "ProcessMessages 4294", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037090, "dur": 80, "ph": "X", "name": "ReadAsync 4294", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037172, "dur": 5, "ph": "X", "name": "ProcessMessages 6138", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037177, "dur": 161, "ph": "X", "name": "ReadAsync 6138", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037340, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037342, "dur": 466, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037809, "dur": 1, "ph": "X", "name": "ProcessMessages 1557", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433037812, "dur": 263, "ph": "X", "name": "ReadAsync 1557", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038076, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038079, "dur": 148, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038301, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038304, "dur": 63, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038370, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038372, "dur": 182, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038556, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038559, "dur": 246, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038807, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038809, "dur": 145, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038957, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433038959, "dur": 266, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433039227, "dur": 55, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433039285, "dur": 2715, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042001, "dur": 4, "ph": "X", "name": "ProcessMessages 8189", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042006, "dur": 86, "ph": "X", "name": "ReadAsync 8189", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042107, "dur": 2, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042110, "dur": 154, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042268, "dur": 2, "ph": "X", "name": "ProcessMessages 2645", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042270, "dur": 262, "ph": "X", "name": "ReadAsync 2645", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042535, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042537, "dur": 256, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042795, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433042797, "dur": 217, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043016, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043018, "dur": 244, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043264, "dur": 1, "ph": "X", "name": "ProcessMessages 1560", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043350, "dur": 60, "ph": "X", "name": "ReadAsync 1560", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043414, "dur": 80, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043496, "dur": 71, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043570, "dur": 9, "ph": "X", "name": "ProcessMessages 1568", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043581, "dur": 152, "ph": "X", "name": "ReadAsync 1568", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043735, "dur": 1, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043737, "dur": 89, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043861, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433043863, "dur": 155, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433044020, "dur": 1, "ph": "X", "name": "ProcessMessages 1584", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433044022, "dur": 224, "ph": "X", "name": "ReadAsync 1584", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433044247, "dur": 1, "ph": "X", "name": "ProcessMessages 1862", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433044249, "dur": 100, "ph": "X", "name": "ReadAsync 1862", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433044351, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433044353, "dur": 804, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433045159, "dur": 3, "ph": "X", "name": "ProcessMessages 4666", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433045162, "dur": 41, "ph": "X", "name": "ReadAsync 4666", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433045205, "dur": 242, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433045450, "dur": 505, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433045956, "dur": 2, "ph": "X", "name": "ProcessMessages 4260", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046001, "dur": 347, "ph": "X", "name": "ReadAsync 4260", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046351, "dur": 2, "ph": "X", "name": "ProcessMessages 2050", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046353, "dur": 419, "ph": "X", "name": "ReadAsync 2050", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046794, "dur": 19, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046815, "dur": 146, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046984, "dur": 1, "ph": "X", "name": "ProcessMessages 1835", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433046986, "dur": 117, "ph": "X", "name": "ReadAsync 1835", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433047106, "dur": 169, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433047277, "dur": 231, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433047524, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433047530, "dur": 448, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433047980, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433047982, "dur": 453, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433048439, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433048441, "dur": 53291, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433101739, "dur": 17, "ph": "X", "name": "ProcessMessages 5104", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433101758, "dur": 1373, "ph": "X", "name": "ReadAsync 5104", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433103135, "dur": 8269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433111423, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433111425, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 158913789952, "ts": 1752942433111607, "dur": 4124, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434274489, "dur": 221, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 154618822656, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 154618822656, "ts": 1752942432998473, "dur": 108590, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 154618822656, "ts": 1752942433107065, "dur": 207, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434274712, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 150323855360, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942432984110, "dur": 132199, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942432984475, "dur": 13861, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942433116333, "dur": 710375, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942433826819, "dur": 445833, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942433827201, "dur": 13054, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942434272659, "dur": 184, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 150323855360, "ts": 1752942434272693, "dur": 62, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434274727, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1752942433196785, "dur": 614411, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752942433197501, "dur": 109281, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752942433729864, "dur": 4024, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752942433733892, "dur": 77292, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752942433734799, "dur": 56404, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752942433817050, "dur": 1001, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752942433816672, "dur": 1570, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752942433016586, "dur": 1396, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433017994, "dur": 355, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433018422, "dur": 142, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433020344, "dur": 844, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433022367, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752942433022550, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752942433022764, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433023261, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752942433023486, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433023636, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433023882, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752942433024764, "dur": 2042, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752942433026959, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433027314, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_F3BBD1233F3279D4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752942433027405, "dur": 3085, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433030713, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Editor.ref.dll_357252F5B9391B9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752942433031018, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752942433031299, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Psdimporter.Editor.ref.dll_773D775121F7A6E7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752942433031374, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752942433031751, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752942433033463, "dur": 3947, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752942433042445, "dur": 205, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752942433046009, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752942433018570, "dur": 29242, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433047819, "dur": 61359, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433109179, "dur": 1827, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433111007, "dur": 92, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433111199, "dur": 65, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433111349, "dur": 60, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433111410, "dur": 531, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433111945, "dur": 68, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433112199, "dur": 1069, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752942433018489, "dur": 29342, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433047863, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433047991, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433048275, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433048501, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433048755, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433048915, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433049451, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433049527, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433049720, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433049847, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433050061, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433050169, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433050311, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433050438, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433050554, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433050683, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433050830, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433050942, "dur": 4627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433055570, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433055781, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433055898, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433056097, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433056164, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433056389, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433056483, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433056569, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433056642, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433056795, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433056949, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057061, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057261, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057435, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057562, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057688, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057827, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433057943, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433058097, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752942433058183, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433058357, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433058492, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752942433058705, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433058889, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433059121, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433059261, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433059417, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752942433059480, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433059623, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433059846, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433060164, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752942433060287, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433060440, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433060606, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433060770, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433063554, "dur": 1135, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752942433062954, "dur": 3329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433066283, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433068474, "dur": 2889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433073345, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/Window/TimelineEditorWindow.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752942433071367, "dur": 2832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433074199, "dur": 2292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433076491, "dur": 2097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433078589, "dur": 2482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433081072, "dur": 2204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433083277, "dur": 2002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433085280, "dur": 2269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433087549, "dur": 3160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433090710, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433093273, "dur": 2696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433095970, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433098119, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433099967, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433102217, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433104567, "dur": 2582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433107149, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433109053, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433018496, "dur": 29340, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433047887, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433048079, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433048406, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433048584, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433048833, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433049466, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433049632, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433049826, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433049925, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433050110, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433050192, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433050339, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433050463, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433050597, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433050714, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433050855, "dur": 4634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433055493, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433055579, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433055824, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433055949, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433056116, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433056216, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433056340, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433056451, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433056556, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433056616, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433056726, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433056913, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057015, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057119, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057342, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057503, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057633, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057728, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057863, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433057999, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433058254, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433058433, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433058595, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433058743, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433058882, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433059105, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433059228, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433059390, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433059532, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433059695, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433059896, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752942433060043, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433060285, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433060367, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433060450, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433060617, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433060779, "dur": 2179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433063480, "dur": 1150, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.ObjectModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752942433062958, "dur": 3386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433066345, "dur": 2388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433068733, "dur": 2861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433073303, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/treeview/ItemGui/ISelectable.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752942433071594, "dur": 2793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433074388, "dur": 2214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433076602, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433078709, "dur": 2492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433081201, "dur": 2207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433083409, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433085368, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433087618, "dur": 3231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433090850, "dur": 2489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433093340, "dur": 2694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433096034, "dur": 2135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433098169, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433100060, "dur": 2348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433102408, "dur": 2249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433104657, "dur": 2591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433107249, "dur": 1954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433018503, "dur": 29361, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433047867, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433048267, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433048474, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433048760, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433049343, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433049641, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433049738, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433049789, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433049930, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433050046, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433050186, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433050289, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433050448, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433050502, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433050713, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433050818, "dur": 4679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433055498, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433055568, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433055813, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433055928, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433056111, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056197, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433056329, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056417, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056512, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056605, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056706, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056868, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433056982, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057098, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057323, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057481, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057609, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057706, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057839, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433057988, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433058201, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752942433058389, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433058576, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433058684, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433058827, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433059069, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433059193, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433059368, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433059515, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433059631, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433059811, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752942433059885, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752942433060054, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433060279, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433060421, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433060542, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433060709, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433060829, "dur": 2242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433063568, "dur": 1127, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/mscorlib.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752942433063071, "dur": 3285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433066356, "dur": 2365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433068722, "dur": 2825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433073250, "dur": 827, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/treeview/TrackGui/TimelineGroupGUI.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752942433071547, "dur": 2787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433074334, "dur": 2224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433076558, "dur": 2088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433078646, "dur": 2504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433081150, "dur": 2181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433083331, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433085283, "dur": 2222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433087505, "dur": 3165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433090670, "dur": 2576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433093246, "dur": 2653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433095900, "dur": 2231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433098132, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433100000, "dur": 2205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433102205, "dur": 2344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433104549, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433107137, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433109067, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433018514, "dur": 29364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433047887, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433048262, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433048407, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433048755, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433048893, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433049486, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433049713, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433049973, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433050090, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433050233, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433050328, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433050454, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433050514, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433050697, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433050801, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433050947, "dur": 4604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433055556, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433055806, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433055919, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433056106, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433056172, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433056328, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433056402, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433056496, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433056566, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433056636, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433056769, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433056944, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057054, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057246, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057410, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057557, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057693, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057820, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433057955, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433058148, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433058354, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433058512, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752942433058664, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433058780, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433058999, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433059130, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433059289, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433059437, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433059571, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433059732, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752942433059782, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433060133, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433060368, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433060463, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433060649, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433060791, "dur": 2229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433063527, "dur": 1151, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.IO.Compression.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752942433063021, "dur": 3363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433066385, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433068693, "dur": 2844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433073254, "dur": 817, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/Utilities/FileUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752942433071537, "dur": 2806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433074344, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433076597, "dur": 2077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433078675, "dur": 2528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433081203, "dur": 2161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433083365, "dur": 1937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433085303, "dur": 2204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433087507, "dur": 3176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433090683, "dur": 2536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433093219, "dur": 2550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433095770, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433098094, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433099954, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433102120, "dur": 2366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433104491, "dur": 2562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433107054, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433108986, "dur": 179, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433018525, "dur": 29366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433047895, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433048277, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433048520, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433048760, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433048894, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433049458, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433049627, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433049819, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433049921, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433050087, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433050151, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433050316, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433050448, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433050604, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433050754, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433050856, "dur": 4682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433055538, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433055620, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433055878, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433055991, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433056128, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433056261, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433056364, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433056476, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433056573, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433056672, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433056839, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433056970, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433057114, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433057360, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433057531, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433057674, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433057772, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433057893, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433058024, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433058238, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752942433058304, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433058481, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433058585, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433058709, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752942433058856, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433059050, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433059180, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433059347, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433059514, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433059644, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433059882, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433060129, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433060299, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433060435, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433060566, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433060725, "dur": 2145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433063578, "dur": 1119, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752942433062871, "dur": 3324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433066196, "dur": 2282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433068485, "dur": 2857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433073300, "dur": 795, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/Window/TimelineWindow_StateChange.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752942433071343, "dur": 2819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433074162, "dur": 2307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433076469, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433078583, "dur": 2508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433081091, "dur": 2198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433083289, "dur": 1977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433085267, "dur": 2186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433087453, "dur": 3176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433090629, "dur": 2605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433093234, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433095854, "dur": 2235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433098103, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433099973, "dur": 2171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433102144, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433104525, "dur": 2551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433107077, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433109013, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433018534, "dur": 29364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433047902, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433048265, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433048426, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433048755, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433048875, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433049456, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433049592, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433049797, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433049893, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433050082, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433050143, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433050333, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433050414, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433050548, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433050647, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433050804, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433050903, "dur": 4652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433055555, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433055716, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433055865, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433055983, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433056124, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433056250, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433056360, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433056464, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433056561, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433056623, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433056731, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433056908, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057022, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057137, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057380, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057517, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057664, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057754, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433057887, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433058038, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433058264, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752942433058326, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433058477, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433058579, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433058706, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433058833, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433059034, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433059150, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433059342, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433059516, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433059694, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433059864, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752942433060035, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433060281, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433060434, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433060569, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433060728, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433060854, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433063611, "dur": 1090, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Runner/System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752942433063074, "dur": 3410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433066485, "dur": 2293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433068778, "dur": 2899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433073315, "dur": 786, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/State/WindowState.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752942433071677, "dur": 2811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433074488, "dur": 2244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433076732, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433078854, "dur": 2442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433081296, "dur": 2272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433083568, "dur": 1961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433085529, "dur": 2260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433087790, "dur": 3154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433090944, "dur": 2476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433093420, "dur": 2795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433096216, "dur": 2083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433098299, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433100179, "dur": 2318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433102497, "dur": 2229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433104726, "dur": 2604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433107331, "dur": 1900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433018543, "dur": 29365, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433047911, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433048269, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433048482, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433048766, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433049317, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433049484, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433049695, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433049822, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433049985, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433050112, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433050219, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433050304, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433050461, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433050519, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433050689, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433050776, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433050945, "dur": 4600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433055548, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433055783, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433055891, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433056089, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433056156, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433056376, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433056481, "dur": 5770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433062251, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433062389, "dur": 33534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942433095923, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433096379, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433096490, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433096601, "dur": 2248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433098850, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433100735, "dur": 2485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433103220, "dur": 2399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433105619, "dur": 2274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433107894, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433018553, "dur": 29367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433047924, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433048265, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433048488, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433048817, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433049458, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433049580, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433049789, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433049886, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433050064, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433050174, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433050366, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433050509, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433050617, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433050787, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433050893, "dur": 4656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433055549, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433055705, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433055837, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433055972, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433056142, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433056287, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433056405, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433056500, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433056604, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433056662, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433056826, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433056964, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057079, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057287, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057453, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057588, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057701, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057836, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433057976, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433058197, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433058361, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433058495, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752942433058698, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433058876, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433059094, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433059204, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433059354, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433059518, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433059639, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433059817, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752942433059910, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752942433060037, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433060286, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433060395, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433060539, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433060683, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433060809, "dur": 2255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433063535, "dur": 1148, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.Data.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752942433063064, "dur": 3345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433066409, "dur": 2353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433068763, "dur": 2555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433073288, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.ide.visualstudio@2.0.22/Editor/AssemblyInfo.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752942433071319, "dur": 2828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433074148, "dur": 2217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433076366, "dur": 2109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433078476, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433080963, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433083139, "dur": 2028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433085167, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433087378, "dur": 3199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433090578, "dur": 2534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433093112, "dur": 2577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433095690, "dur": 2269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433097986, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433098079, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752942433098133, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433098209, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433100122, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433102303, "dur": 2298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433104601, "dur": 2597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433107198, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433109143, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433018559, "dur": 29381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433047943, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433048267, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433048465, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433048756, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433048883, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433049458, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433049619, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433049826, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433049917, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433050088, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433050156, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433050313, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433050442, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433050551, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433050658, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433050784, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433050874, "dur": 4672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433055547, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433055638, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433055899, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433056026, "dur": 5616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433061643, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433061706, "dur": 35891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752942433097597, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433097933, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433098021, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433098098, "dur": 5503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433103601, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433103690, "dur": 8206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752942433018565, "dur": 29381, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433047946, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433048269, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433048484, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433048764, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433049311, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433049458, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433049593, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433049814, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433049904, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433050067, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433050146, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433050309, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433050434, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433050611, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433050768, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433050868, "dur": 4672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433055541, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433055634, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433055899, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433056011, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433056164, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433056273, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433056392, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433056488, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433056564, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433056625, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433056740, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433056908, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057003, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057118, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057392, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057540, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057684, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057814, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433057912, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433058084, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752942433058159, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433058332, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752942433058387, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433058529, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752942433058700, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433058822, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433059026, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433059140, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433059310, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433059457, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433059617, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433059787, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752942433059887, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433060096, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433060289, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433060464, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433060662, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752942433060722, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433060821, "dur": 2147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433063698, "dur": 1011, "ph": "X", "name": "File", "args": {"detail": "/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/ilpp/Unity.ILPP.Trigger/System.Net.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752942433062969, "dur": 3358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433066327, "dur": 2350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433068677, "dur": 2849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433073334, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.timeline@1.7.6/Editor/Utilities/StyleManager.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752942433071526, "dur": 2887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433074413, "dur": 2202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433076615, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433078728, "dur": 2493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433081221, "dur": 2261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433083483, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433085392, "dur": 2213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433087606, "dur": 3158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433090764, "dur": 2543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433093307, "dur": 2678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433095987, "dur": 2168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433098156, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433100048, "dur": 2208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433102257, "dur": 2324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433104581, "dur": 2648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433107230, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433109110, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433115217, "dur": 531, "ph": "X", "name": "ProfilerWriteOutput"}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752942433854709, "dur": 123379, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433978094, "dur": 132, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433978291, "dur": 96, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433978393, "dur": 9947, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942433988356, "dur": 279421, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942434267998, "dur": 1032, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752942433978329, "dur": 10037, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433988380, "dur": 2372, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433990752, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433990915, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433991049, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433991211, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433991335, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942433991445, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433991532, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433991671, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433991801, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433991954, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992090, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992147, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992344, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992473, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992594, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992715, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992864, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433992964, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993072, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993184, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993307, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993481, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993602, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993698, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993833, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433993905, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994039, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994177, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994306, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994462, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994584, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994694, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994818, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433994936, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433996392, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433997608, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942433998843, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434000146, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434001410, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434002638, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434004051, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434005258, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434006528, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434007609, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434008562, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434009881, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434011019, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434012296, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434013845, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434015183, "dur": 1702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434016885, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434018144, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434019628, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434020999, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434022263, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434023544, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434024752, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434025809, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434027177, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434027495, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434027549, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434027791, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434028176, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942434028436, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942434028876, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434029651, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434029878, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752942434030416, "dur": 5233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434035649, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434035824, "dur": 3273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434039099, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434039341, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434040332, "dur": 119836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434160169, "dur": 3630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434163800, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434163933, "dur": 6795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434170728, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434170856, "dur": 7145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434178002, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434178116, "dur": 7600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752942434185717, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434186212, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434186321, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434186477, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752942434186649, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434187020, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434187125, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752942434187179, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434187306, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434187561, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434187703, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434187946, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434188224, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434188325, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752942434188536, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434188666, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752942434188728, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434188834, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434188965, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752942434189026, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434189110, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752942434189173, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434189254, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752942434190116, "dur": 2260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434192383, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434193332, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752942434193422, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434193811, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434193893, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752942434193950, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434194049, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752942434194789, "dur": 72973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433978333, "dur": 10051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433988431, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433988521, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433988601, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433988775, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433988868, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433989031, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433989117, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433989341, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433989546, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433989639, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433989822, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433989958, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433990090, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433990267, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433990374, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433990528, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433990635, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433990797, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433990906, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433991027, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433991185, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433991272, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433991419, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433991503, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433991625, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433991850, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433991958, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942433992118, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433992283, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433992435, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433992568, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433992678, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433992859, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433992969, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993080, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993216, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993371, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993473, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993620, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993712, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993845, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433993929, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994063, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994198, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994313, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994473, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994595, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994718, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994830, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433994954, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433996440, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433997657, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942433998890, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434000213, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434001447, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434002660, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434004063, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434005279, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434006574, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434007646, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434008602, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434009935, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434011102, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434012379, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434013901, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434015290, "dur": 1650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434016940, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434018252, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434019725, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434021072, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434022384, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434023618, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434024822, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434025865, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434027208, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434027465, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434027537, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434027794, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434028175, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942434028564, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434028625, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434030702, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434030933, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942434031465, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434032766, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434032948, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942434033390, "dur": 1858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434035248, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434035406, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434036821, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434036967, "dur": 2568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434039535, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434039772, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434039861, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434040517, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752942434040647, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434041158, "dur": 121981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434163141, "dur": 5504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434168646, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434168737, "dur": 6589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434175327, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434175583, "dur": 6019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434181602, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434181741, "dur": 6777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752942434188519, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434188676, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434188799, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752942434188880, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434189079, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434189177, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434189779, "dur": 928, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434190710, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752942434191907, "dur": 1342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434193255, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434193312, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434193412, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434193482, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1752942434193757, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434193969, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434194442, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752942434194927, "dur": 72826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433978337, "dur": 10058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433988444, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433988561, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433988661, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433988813, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433988908, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433989048, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433989148, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433989383, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433989561, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433989710, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433989822, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433990023, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433990086, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433990279, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433990362, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433990506, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433990644, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433990834, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433991005, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433991091, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433991230, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433991359, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433991458, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433991577, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433991702, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433991872, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433991971, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942433992113, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433992277, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433992414, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433992555, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433992668, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433992825, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433992949, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993047, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993175, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993299, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993437, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993563, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993663, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993778, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993873, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433993973, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433994120, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433994212, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433994380, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433994521, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433995353, "dur": 344, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752942433995698, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433997103, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433998216, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942433999521, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434000898, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434002056, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434003395, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434004676, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434006097, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434007158, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434008146, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434009368, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434010537, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434011806, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434013193, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434014632, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434014723, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434016356, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434017675, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434019045, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434020528, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434021865, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434023137, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434024407, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434025425, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434026699, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434027031, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434027369, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434027549, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434027792, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434028165, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434028401, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434028519, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434030442, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434030732, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434031528, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434031619, "dur": 1395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434033015, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752942434033320, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434033544, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434033962, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434034213, "dur": 6329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434040589, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434040723, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434041314, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434041375, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434041692, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434041748, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434041997, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752942434042109, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752942434042453, "dur": 224355, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752942433978352, "dur": 10050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433988407, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433988550, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433988619, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433988780, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433988937, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433989065, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433989179, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433989374, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433989515, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433989608, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433989795, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433989898, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433990073, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433990207, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433990319, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433990420, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433990569, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433990692, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433990839, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433990954, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433991064, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433991206, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433991324, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433991445, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433991551, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433991683, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433991841, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433991987, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942433992103, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992161, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992326, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992450, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992573, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992700, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992867, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433992973, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993068, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993179, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993300, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993447, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993587, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993674, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993805, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433993886, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994016, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994159, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994260, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994422, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994551, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994668, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994779, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433994918, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433996397, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433997601, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942433998808, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434000183, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434001441, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434002679, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434004083, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434005337, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434006604, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434007666, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434008634, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434009986, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434011241, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434012480, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434013949, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434015349, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434016987, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434018349, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434019769, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434021155, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434022460, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434023696, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434024887, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434025255, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434025460, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434026635, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434027342, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434027545, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434027773, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434027828, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434028255, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434029020, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434030496, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434030642, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_AEDA0D01FA0B3E02.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434030702, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434030759, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434031406, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434032062, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434032139, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434032310, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434034740, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434034921, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434035175, "dur": 1823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434036999, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434037203, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434037625, "dur": 1868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434039493, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434039883, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434041317, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752942434041376, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434041644, "dur": 118820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434160465, "dur": 4253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434164719, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434164856, "dur": 3497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434168353, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434168522, "dur": 5401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434173923, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434174033, "dur": 4311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434178344, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434178468, "dur": 3861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434182329, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752942434182770, "dur": 11927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752942434194757, "dur": 73030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433978353, "dur": 10058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433988416, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433988517, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433988586, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433988760, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433988852, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433989030, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433989104, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433989347, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433989506, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433989588, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433989767, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433989861, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433990040, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433990166, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433990359, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433990506, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433990621, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433990748, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433990862, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433991007, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433991159, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433991241, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942433991408, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433991480, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433991609, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433991748, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433991934, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992028, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992140, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992311, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992462, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992614, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992722, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992877, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433992982, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993089, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993191, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993325, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993439, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993569, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993670, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993799, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993884, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433993986, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994137, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994226, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994401, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994530, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994632, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994754, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433994881, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433995005, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433996446, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433997659, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942433998885, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434000211, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434001444, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434002651, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434004070, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434005324, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434006589, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434007658, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434008614, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434009960, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434011154, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434012414, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434013923, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434015321, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434016968, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434018317, "dur": 1435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434019752, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434021105, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434022404, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434023635, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434024842, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434025371, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434026304, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434027447, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434027548, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434027783, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434028153, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942434028701, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434031935, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434032093, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942434032408, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434034432, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434034598, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942434035321, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434037672, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434037883, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942434038093, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434040514, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752942434040643, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434041092, "dur": 119083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434160183, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434164272, "dur": 7930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434172204, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434172379, "dur": 5409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434177788, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434177900, "dur": 16406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752942434194307, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434194462, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752942434194999, "dur": 72759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433978367, "dur": 10054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433988426, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433988532, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433988589, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433988741, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433988842, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433989017, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433989087, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433989281, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433989392, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433989545, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433989635, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433989796, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433989903, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433990102, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433990276, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433990410, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433990537, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433990654, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433990789, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433990849, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433990963, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433991135, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433991227, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433991394, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433991465, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433991576, "dur": 4608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942433996185, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942433996247, "dur": 16511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434012759, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434012963, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942434013039, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434013177, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434014627, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434014735, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434016371, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434017691, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434019060, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434020545, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434021876, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434023150, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434024416, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434025454, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434026669, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434027151, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434027446, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434027527, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434027796, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434028162, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942434028400, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434028471, "dur": 5153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434033625, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434033892, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942434034194, "dur": 4322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434038516, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434038726, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752942434038997, "dur": 1358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434040356, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434040465, "dur": 119737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434160204, "dur": 4046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434164251, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434164403, "dur": 3487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434167890, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434168009, "dur": 3491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434171500, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434171716, "dur": 4603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434176320, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434176449, "dur": 5880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434182330, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434182647, "dur": 11650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752942434194297, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434194417, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434194484, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752942434195319, "dur": 72459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433978369, "dur": 10103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433988475, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433988591, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433988695, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433988843, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433988973, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433989095, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433989236, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433989402, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433989478, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433989614, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433989730, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433989938, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433990039, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433990172, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433990276, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433990393, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433990498, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433990656, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433990739, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433990917, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433991051, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433991212, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433991347, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433991456, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433991564, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433991695, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433991868, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433991962, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942433992108, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433992263, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433992391, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433992523, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433992641, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433992782, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433992921, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993017, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993119, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993251, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993402, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993495, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993633, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993745, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993857, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433993945, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994077, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994205, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994332, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994492, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994585, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994703, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994854, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433994962, "dur": 1453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433996416, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433997637, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942433998872, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434000238, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434001486, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434002718, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434004118, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434005392, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434006639, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434007685, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434008643, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434009981, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434011220, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434012459, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434013935, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434015342, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434016973, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434018322, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434019761, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434021122, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434022429, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434023681, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434024866, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434025391, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434026590, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434027337, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434027535, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434027805, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434028262, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942434029195, "dur": 1847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434031042, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434031249, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942434031675, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434033918, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434034071, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942434034287, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434035445, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434035614, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434036557, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434036683, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434038779, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434038914, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752942434039134, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434039246, "dur": 1262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434040549, "dur": 119656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434160206, "dur": 7781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434167987, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434168174, "dur": 3774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434171948, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434172148, "dur": 4587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434176736, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434176852, "dur": 5918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434182771, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752942434183263, "dur": 11700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752942434194990, "dur": 72785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433978381, "dur": 10064, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433988450, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433988555, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433988641, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433988780, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433988873, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433989035, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433989124, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433989379, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433989541, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433989631, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433989802, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433989944, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433990074, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433990212, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433990349, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433990501, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433990589, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433990737, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433990827, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433991006, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433991095, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433991237, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433991369, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433991471, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433991594, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433991729, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433991877, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433991966, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942433992106, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433992223, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433992369, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433992523, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433992648, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433992804, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433992941, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993031, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993146, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993276, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993420, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993516, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993647, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993771, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993865, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433993960, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994085, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994208, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994362, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994507, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994614, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994738, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994873, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433994981, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433996424, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433997642, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942433998869, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434000202, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434001454, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434002676, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434004081, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434005347, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434006600, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434007662, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434008622, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434009968, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434011151, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434012399, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434013910, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434015274, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434016943, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434018272, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434019735, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434021079, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434022397, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434023625, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434024833, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434025439, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434026694, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434027108, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434027526, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434027786, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434028178, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434028788, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434029838, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434030039, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434030344, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434031035, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434031525, "dur": 3013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434034539, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434034724, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434035073, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434036215, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434036402, "dur": 1800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434038203, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434038628, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434038970, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434039054, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752942434039388, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434040353, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434040415, "dur": 119772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434160189, "dur": 3889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434164079, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434164168, "dur": 3726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434167894, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434168061, "dur": 3844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434171906, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434172121, "dur": 4845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434176968, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434177101, "dur": 4094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434181195, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434181319, "dur": 3430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434184750, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752942434184995, "dur": 10283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752942434195310, "dur": 72458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433978384, "dur": 10067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433988455, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433988551, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433988609, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433988778, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433988860, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433989027, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433989082, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433989285, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433989406, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433989551, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433989652, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433989817, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433989952, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433990076, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433990217, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433990321, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433990437, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433990592, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433990740, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433990821, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433991012, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433991125, "dur": 4803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942433995928, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942433995989, "dur": 17904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434013893, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434014036, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942434014201, "dur": 3831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942434018032, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434018121, "dur": 9594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434027775, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942434027845, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434028145, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942434028277, "dur": 3161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434031438, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434031740, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752942434031870, "dur": 1698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434033569, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434033707, "dur": 3514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1752942434037222, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434037288, "dur": 658, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434158525, "dur": 445, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434037952, "dur": 121026, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1752942434160166, "dur": 3796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434163963, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434164065, "dur": 4004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434168069, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434168472, "dur": 4375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434172847, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434172974, "dur": 3514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434176488, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434176601, "dur": 8844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752942434185445, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434185721, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434185852, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434186057, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752942434186241, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434186595, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434186698, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434186905, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752942434187092, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434187226, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434187342, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434187411, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434187599, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434187782, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434187876, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188106, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188165, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188291, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188351, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188608, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188710, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434188783, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752942434188834, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434189020, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434189078, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434189160, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434189220, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752942434189295, "dur": 2578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434191880, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752942434192384, "dur": 846, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434193278, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752942434193388, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434193764, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752942434193856, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434194009, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752942434194511, "dur": 73230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433978389, "dur": 10069, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433988462, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433988555, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433988628, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433988799, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433988884, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433989047, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433989134, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433989323, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433989413, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433989567, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433989722, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433989838, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433990032, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433990110, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433990278, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433990394, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433990566, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433990672, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433990798, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433990912, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433991034, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433991188, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433991294, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942433991429, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433991532, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433991644, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433991791, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433991944, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992040, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992196, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992354, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992489, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992627, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992732, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992888, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433992994, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993101, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993210, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993352, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993463, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993590, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993680, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993823, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433993895, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994020, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994171, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994285, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994441, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994566, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994683, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994802, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433994907, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433995027, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433996471, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433997685, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942433998918, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434000064, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434001368, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434002591, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434004008, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434005178, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434006508, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434007602, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434008564, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434009897, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434011085, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434012351, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434013872, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434015218, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434016913, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434018207, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434019696, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434021050, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434022354, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434023587, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434024795, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434025814, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434027160, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434027469, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434027539, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434027806, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434028407, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942434028936, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434029417, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434029566, "dur": 2918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434032484, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434032612, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942434032854, "dur": 2594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434035449, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434035626, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434035734, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942434035991, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434038002, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434038165, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942434038511, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434038592, "dur": 1488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434040080, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434040197, "dur": 1804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434042002, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752942434042133, "dur": 118058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434160192, "dur": 4902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434165094, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434165214, "dur": 4148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434169363, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434169475, "dur": 4776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434174251, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434174352, "dur": 6114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434180467, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752942434180644, "dur": 14210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752942434194892, "dur": 72872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752942434271331, "dur": 1385, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 3277, "ts": 1752942434274841, "dur": 31, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 39945, "tid": 3277, "ts": 1752942434275029, "dur": 19, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 3277, "ts": 1752942434275324, "dur": 100136, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 39945, "tid": 3277, "ts": 1752942434274943, "dur": 86, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434275065, "dur": 258, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434375600, "dur": 555, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 39945, "tid": 3277, "ts": 1752942434273281, "dur": 102918, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}