{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 3696, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 3696, "ts": 1753021384037569, "dur": 644, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 3696, "ts": 1753021384041577, "dur": 674, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1753021379136268, "dur": 6469, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1753021379142742, "dur": 55183, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1753021379197933, "dur": 28380, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 3696, "ts": 1753021384042255, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379134590, "dur": 42619, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379177216, "dur": 4852063, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379177856, "dur": 3423, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379181304, "dur": 816, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379182124, "dur": 10895, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379193635, "dur": 516, "ph": "X", "name": "ProcessMessages 8160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195307, "dur": 376, "ph": "X", "name": "ReadAsync 8160", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195686, "dur": 3, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195694, "dur": 68, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195764, "dur": 40, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195810, "dur": 29, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195842, "dur": 41, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195885, "dur": 1, "ph": "X", "name": "ProcessMessages 1264", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195887, "dur": 41, "ph": "X", "name": "ReadAsync 1264", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195930, "dur": 1, "ph": "X", "name": "ProcessMessages 1262", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195931, "dur": 37, "ph": "X", "name": "ReadAsync 1262", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379195970, "dur": 27, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196003, "dur": 44, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196050, "dur": 42, "ph": "X", "name": "ReadAsync 1292", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196094, "dur": 54, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196151, "dur": 197, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196354, "dur": 2, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196357, "dur": 57, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196418, "dur": 1, "ph": "X", "name": "ProcessMessages 1684", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196420, "dur": 60, "ph": "X", "name": "ReadAsync 1684", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196486, "dur": 1, "ph": "X", "name": "ProcessMessages 1757", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196489, "dur": 35, "ph": "X", "name": "ReadAsync 1757", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196582, "dur": 36, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196619, "dur": 1, "ph": "X", "name": "ProcessMessages 2124", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196622, "dur": 46, "ph": "X", "name": "ReadAsync 2124", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196670, "dur": 2, "ph": "X", "name": "ProcessMessages 1419", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196674, "dur": 208, "ph": "X", "name": "ReadAsync 1419", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379196884, "dur": 122, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197008, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197009, "dur": 32, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197043, "dur": 408, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197454, "dur": 1, "ph": "X", "name": "ProcessMessages 1624", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197457, "dur": 43, "ph": "X", "name": "ReadAsync 1624", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197502, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197505, "dur": 42, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197549, "dur": 1, "ph": "X", "name": "ProcessMessages 1333", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197551, "dur": 34, "ph": "X", "name": "ReadAsync 1333", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197587, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197589, "dur": 32, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197623, "dur": 25, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197656, "dur": 88, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197747, "dur": 30, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197779, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197781, "dur": 80, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379197864, "dur": 243, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198109, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198110, "dur": 36, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198148, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198150, "dur": 39, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198191, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198193, "dur": 47, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198243, "dur": 39, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198283, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198285, "dur": 104, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198391, "dur": 2, "ph": "X", "name": "ProcessMessages 1468", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198393, "dur": 33, "ph": "X", "name": "ReadAsync 1468", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198428, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198430, "dur": 44, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198475, "dur": 1, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198482, "dur": 38, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198523, "dur": 33, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198558, "dur": 37, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198598, "dur": 39, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198638, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198640, "dur": 198, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198841, "dur": 1, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198842, "dur": 31, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198876, "dur": 34, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379198912, "dur": 203, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199117, "dur": 1, "ph": "X", "name": "ProcessMessages 1169", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199119, "dur": 29, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199150, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199152, "dur": 219, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199372, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199374, "dur": 25, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199401, "dur": 21, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199425, "dur": 154, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199582, "dur": 24, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199608, "dur": 24, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199634, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199635, "dur": 34, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199671, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199673, "dur": 28, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199703, "dur": 192, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199910, "dur": 2, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199913, "dur": 36, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199950, "dur": 1, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379199952, "dur": 71, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200025, "dur": 1, "ph": "X", "name": "ProcessMessages 1287", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200027, "dur": 116, "ph": "X", "name": "ReadAsync 1287", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200159, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200162, "dur": 42, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200221, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200223, "dur": 165, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200393, "dur": 33, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200428, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200430, "dur": 33, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200465, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200466, "dur": 176, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200643, "dur": 1, "ph": "X", "name": "ProcessMessages 1746", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200645, "dur": 38, "ph": "X", "name": "ReadAsync 1746", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200688, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200690, "dur": 40, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200731, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200733, "dur": 35, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200771, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200774, "dur": 61, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200844, "dur": 34, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200880, "dur": 51, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200934, "dur": 29, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200964, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379200966, "dur": 171, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201138, "dur": 1, "ph": "X", "name": "ProcessMessages 1289", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201140, "dur": 29, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201171, "dur": 1, "ph": "X", "name": "ProcessMessages 954", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201172, "dur": 130, "ph": "X", "name": "ReadAsync 954", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201303, "dur": 1, "ph": "X", "name": "ProcessMessages 1711", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201305, "dur": 31, "ph": "X", "name": "ReadAsync 1711", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201338, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201340, "dur": 238, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201582, "dur": 40, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201623, "dur": 1, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201624, "dur": 181, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201807, "dur": 1, "ph": "X", "name": "ProcessMessages 1368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201809, "dur": 34, "ph": "X", "name": "ReadAsync 1368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201853, "dur": 33, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201906, "dur": 1, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201908, "dur": 42, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201951, "dur": 7, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379201960, "dur": 278, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202240, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202241, "dur": 43, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202286, "dur": 1, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202288, "dur": 69, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202375, "dur": 1, "ph": "X", "name": "ProcessMessages 1309", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202377, "dur": 27, "ph": "X", "name": "ReadAsync 1309", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202407, "dur": 37, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202453, "dur": 27, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202482, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202483, "dur": 26, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202511, "dur": 7, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202518, "dur": 160, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202693, "dur": 1, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202695, "dur": 28, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202728, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202731, "dur": 100, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202833, "dur": 1, "ph": "X", "name": "ProcessMessages 1169", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202836, "dur": 47, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202904, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202906, "dur": 44, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202951, "dur": 1, "ph": "X", "name": "ProcessMessages 1325", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202953, "dur": 40, "ph": "X", "name": "ReadAsync 1325", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202994, "dur": 1, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379202997, "dur": 34, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203034, "dur": 104, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203139, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203141, "dur": 34, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203176, "dur": 1, "ph": "X", "name": "ProcessMessages 1666", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203178, "dur": 28, "ph": "X", "name": "ReadAsync 1666", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203208, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203250, "dur": 50, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203301, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203302, "dur": 32, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203338, "dur": 40, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203380, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203381, "dur": 53, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203436, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203438, "dur": 36, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203477, "dur": 45, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203524, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203526, "dur": 216, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203745, "dur": 31, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203778, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203780, "dur": 40, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203822, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203824, "dur": 55, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203881, "dur": 1, "ph": "X", "name": "ProcessMessages 1188", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203883, "dur": 63, "ph": "X", "name": "ReadAsync 1188", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203962, "dur": 1, "ph": "X", "name": "ProcessMessages 1430", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379203964, "dur": 54, "ph": "X", "name": "ReadAsync 1430", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204020, "dur": 1, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204022, "dur": 37, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204061, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204063, "dur": 36, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204108, "dur": 33, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204143, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204145, "dur": 183, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204360, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204362, "dur": 21, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204409, "dur": 132, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204543, "dur": 1, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204544, "dur": 33, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204579, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204580, "dur": 73, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204678, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204679, "dur": 223, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204905, "dur": 1, "ph": "X", "name": "ProcessMessages 1401", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204907, "dur": 32, "ph": "X", "name": "ReadAsync 1401", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379204941, "dur": 261, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205204, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205205, "dur": 32, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205241, "dur": 161, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205423, "dur": 1, "ph": "X", "name": "ProcessMessages 1445", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205425, "dur": 33, "ph": "X", "name": "ReadAsync 1445", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205460, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205462, "dur": 279, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205769, "dur": 44, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205815, "dur": 2, "ph": "X", "name": "ProcessMessages 2422", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205819, "dur": 34, "ph": "X", "name": "ReadAsync 2422", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205856, "dur": 32, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205891, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205893, "dur": 36, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205947, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205949, "dur": 48, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205998, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379205999, "dur": 1239, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379207241, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379207243, "dur": 306, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379207560, "dur": 116, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379207688, "dur": 514, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379208208, "dur": 353, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379208564, "dur": 266, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379208832, "dur": 84, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379208919, "dur": 267, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379209199, "dur": 530, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379209733, "dur": 476, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379210219, "dur": 641, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379210862, "dur": 3, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379210866, "dur": 139, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379211009, "dur": 361, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379211371, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379211373, "dur": 616, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379211993, "dur": 511, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379212506, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379212508, "dur": 537, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213047, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213049, "dur": 273, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213324, "dur": 309, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213636, "dur": 271, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213908, "dur": 1, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213909, "dur": 36, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213947, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213949, "dur": 37, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379213989, "dur": 31, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379214025, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379214026, "dur": 712, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379214740, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379214742, "dur": 728, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379216127, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379216128, "dur": 85, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379216217, "dur": 420, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379216638, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379216640, "dur": 1303, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379217945, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379217947, "dur": 201, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218151, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218152, "dur": 25, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218180, "dur": 172, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218354, "dur": 95, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218451, "dur": 183, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218636, "dur": 193, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218831, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379218833, "dur": 187, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219022, "dur": 150, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219183, "dur": 1, "ph": "X", "name": "ProcessMessages 1342", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219185, "dur": 28, "ph": "X", "name": "ReadAsync 1342", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219215, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219217, "dur": 326, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219548, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379219550, "dur": 449, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379220320, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379220330, "dur": 32, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379220371, "dur": 352, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379220725, "dur": 117, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379220854, "dur": 275, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379221142, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379221192, "dur": 333, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379221554, "dur": 38, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379221596, "dur": 126, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379221736, "dur": 361, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379222100, "dur": 765, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379222868, "dur": 171, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379223041, "dur": 2, "ph": "X", "name": "ProcessMessages 1678", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379223044, "dur": 613, "ph": "X", "name": "ReadAsync 1678", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379223660, "dur": 94, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379223756, "dur": 435, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379224196, "dur": 1350, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379225550, "dur": 621, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379226174, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379226176, "dur": 542, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379226721, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379226723, "dur": 215, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379226940, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379226942, "dur": 87, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227032, "dur": 2, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227034, "dur": 41, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227077, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227078, "dur": 401, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227481, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227482, "dur": 152, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227636, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227638, "dur": 38, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227688, "dur": 36, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379227727, "dur": 529, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379228258, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379228260, "dur": 444, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379228708, "dur": 158, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379228869, "dur": 384, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379229255, "dur": 179, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379229437, "dur": 453, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379229891, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379229893, "dur": 666, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379230561, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379230564, "dur": 128, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379230694, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379230696, "dur": 664, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379231362, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379231364, "dur": 506, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379231871, "dur": 2, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379231874, "dur": 613, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379232491, "dur": 109, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379232625, "dur": 513, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379233141, "dur": 31, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379233175, "dur": 613, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379233792, "dur": 140, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379233942, "dur": 1, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379233944, "dur": 68, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234015, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234017, "dur": 502, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234521, "dur": 10, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234531, "dur": 137, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234670, "dur": 1, "ph": "X", "name": "ProcessMessages 1314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234671, "dur": 35, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234708, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379234710, "dur": 535, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379235252, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379235253, "dur": 624, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379235889, "dur": 162, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236053, "dur": 560, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236632, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236635, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236679, "dur": 113, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236795, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236848, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236949, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379236950, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237060, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237136, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237188, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237190, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237365, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237418, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237461, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237503, "dur": 146, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237651, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237711, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237716, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237752, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237900, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237946, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379237948, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238085, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238088, "dur": 96, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238186, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238301, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238493, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238544, "dur": 63, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238608, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238611, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238661, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238773, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238854, "dur": 138, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238994, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379238995, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239042, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239127, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239129, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239233, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239272, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239314, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239449, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239451, "dur": 194, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239648, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239696, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239748, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239819, "dur": 96, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379239918, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240002, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240033, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240068, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240210, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240282, "dur": 49, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240336, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240422, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240426, "dur": 171, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240681, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240683, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240745, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240812, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240813, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379240832, "dur": 261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379241095, "dur": 215, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379241317, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379241413, "dur": 100, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379241515, "dur": 469, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379242067, "dur": 2728, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379244798, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379244800, "dur": 104, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379244928, "dur": 5, "ph": "X", "name": "ProcessMessages 1708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379244936, "dur": 52, "ph": "X", "name": "ReadAsync 1708", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379244991, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379244993, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245038, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245043, "dur": 99, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245146, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245179, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245330, "dur": 89, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245422, "dur": 107, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245531, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245533, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245582, "dur": 134, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245718, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245802, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245804, "dur": 69, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245875, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245877, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245948, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379245952, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379246008, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379246012, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379246123, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379246251, "dur": 12675, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379258932, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379258935, "dur": 311, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379259250, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379259252, "dur": 860, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379260117, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379260273, "dur": 468, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379260744, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379260909, "dur": 3611, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379264524, "dur": 23431, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379287960, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379287963, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379288010, "dur": 868, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379288883, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379289097, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379289145, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379289148, "dur": 583, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379289734, "dur": 314, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290051, "dur": 372, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290426, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290429, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290529, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290590, "dur": 344, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290938, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379290982, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379291225, "dur": 201, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379291429, "dur": 186, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379291619, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379291736, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379291817, "dur": 69, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379291889, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292027, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292125, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292270, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292434, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292436, "dur": 234, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292673, "dur": 95, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379292770, "dur": 364, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379293138, "dur": 342, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379293482, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379293529, "dur": 289, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379293820, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379293881, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379293957, "dur": 228, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379294189, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379294310, "dur": 237, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379294550, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379294685, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379294971, "dur": 369, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379295341, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379295502, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379295713, "dur": 315, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379296030, "dur": 381, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379296421, "dur": 20, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379296444, "dur": 83, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379296529, "dur": 709, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379297240, "dur": 188, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379297432, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379297477, "dur": 288, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379297768, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379297770, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379297840, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298064, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298065, "dur": 109, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298177, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298179, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298294, "dur": 118, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298414, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298594, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298692, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298752, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298754, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379298881, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299135, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299138, "dur": 268, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299411, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299525, "dur": 338, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299866, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299868, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379299926, "dur": 311, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379300239, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379300241, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379300319, "dur": 746, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379301068, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379301069, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379301104, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379301342, "dur": 473, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379301818, "dur": 1796, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379303626, "dur": 8, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379303636, "dur": 165829, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469470, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469473, "dur": 73, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469549, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469591, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469622, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469658, "dur": 26, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469686, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379469728, "dur": 1204, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379470936, "dur": 3222, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379474191, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379474204, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379474443, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379474445, "dur": 677, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379475126, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379475338, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379475340, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379475636, "dur": 1660, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379477300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379477302, "dur": 409, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379477715, "dur": 773, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379478492, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379478606, "dur": 309, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379478918, "dur": 639, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379479560, "dur": 579, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379480142, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379480143, "dur": 627, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379480774, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379480776, "dur": 598, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379481379, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379481482, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379481484, "dur": 1996, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379483484, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379483585, "dur": 779, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379484368, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379484369, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379484667, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379484882, "dur": 750, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379485635, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379486159, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379486166, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379486450, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379486451, "dur": 1726, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379488181, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379488314, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379488324, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379488874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379488877, "dur": 684, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379489565, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379489568, "dur": 1190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379490762, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379490764, "dur": 298, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491065, "dur": 489, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491557, "dur": 19, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491578, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491711, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491712, "dur": 117, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491832, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491835, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379491949, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492026, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492085, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492171, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492265, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492319, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492373, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492464, "dur": 113, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492580, "dur": 361, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492943, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379492945, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493007, "dur": 78, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493087, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493095, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493215, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493290, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493396, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493599, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493653, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493754, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493813, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493843, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493986, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379493988, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494095, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494134, "dur": 11, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494148, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494210, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494256, "dur": 373, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494631, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494633, "dur": 191, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494827, "dur": 142, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379494973, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495033, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495067, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495145, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495200, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495275, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495277, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495371, "dur": 192, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495566, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495670, "dur": 219, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379495891, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379496129, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379496212, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379496332, "dur": 130, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379496465, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379496539, "dur": 527, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379497070, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379497228, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379497230, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021379497263, "dur": 4145395, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383642663, "dur": 31, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383642696, "dur": 1602, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383644303, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383644307, "dur": 1768, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383646080, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383646082, "dur": 215797, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383861885, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383861889, "dur": 90, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383861983, "dur": 1, "ph": "X", "name": "ProcessMessages 5636", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383862047, "dur": 39, "ph": "X", "name": "ReadAsync 5636", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383862092, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383862094, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383862134, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383862185, "dur": 33, "ph": "X", "name": "ProcessMessages 4609", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383862220, "dur": 3472, "ph": "X", "name": "ReadAsync 4609", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383865695, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383865699, "dur": 559, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383866260, "dur": 26, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383866287, "dur": 40993, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383907284, "dur": 20, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383907306, "dur": 2979, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383910287, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021383910290, "dur": 111604, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384021898, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384021901, "dur": 69, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384021974, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384022045, "dur": 76, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384022126, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384022129, "dur": 87, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384022219, "dur": 20, "ph": "X", "name": "ProcessMessages 6644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384022240, "dur": 3516, "ph": "X", "name": "ReadAsync 6644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384025761, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384025766, "dur": 385, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384026155, "dur": 28, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384026184, "dur": 313, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384026501, "dur": 376, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1753021384026879, "dur": 2336, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 3696, "ts": 1753021384042272, "dur": 994, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 8589934592, "ts": 1753021379132555, "dur": 93867, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1753021379226423, "dur": 23, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1753021379226448, "dur": 1972, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 3696, "ts": 1753021384043268, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 4294967296, "ts": 1753021379073066, "dur": 4957862, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1753021379078484, "dur": 39398, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1753021384030990, "dur": 4426, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1753021384033343, "dur": 321, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1753021384035477, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 3696, "ts": 1753021384043274, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753021379173750, "dur": 2519, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753021379176311, "dur": 15124, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753021379191525, "dur": 116, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753021379192290, "dur": 364, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753021379193029, "dur": 3496, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753021379196928, "dur": 198, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753021379191646, "dur": 44397, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753021379236050, "dur": 4791075, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753021384027348, "dur": 580, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753021379191589, "dur": 44475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379236068, "dur": 12641, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379248709, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379250084, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379251633, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379253240, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379254889, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379256977, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379258735, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379260515, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379262393, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379264237, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379266090, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379268022, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379269750, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379271640, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379273035, "dur": 943, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/ReorderableList/ElementAdderMenu/ElementAdderMenuCommandAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753021379273035, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379274947, "dur": 1715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379276662, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379278288, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379280038, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379281879, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379283625, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379285389, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379287038, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379287613, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379287710, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379288203, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379288697, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379289485, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379289688, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379289855, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379290390, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379290688, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379290795, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379291092, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379291335, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379292429, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379292967, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379293174, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379293292, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379294241, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379294771, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379294829, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379295042, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379295335, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379295397, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379295654, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379295734, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379297001, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379297445, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379298521, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379298836, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_43307AC03DF2DC88.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379298952, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379299213, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753021379299575, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379299733, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379300438, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379300728, "dur": 171195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379471927, "dur": 3964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379475891, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379475966, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379479207, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379479330, "dur": 5048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379484379, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379484489, "dur": 4446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379488935, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379489083, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753021379493752, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379494073, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379494153, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379494376, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379494552, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753021379494622, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379494843, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PsdPlugin.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753021379494899, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495052, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495127, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495259, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495445, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495549, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495637, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753021379495698, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495787, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379495921, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379496132, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753021379496308, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753021379496361, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379496473, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753021379496601, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379497167, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753021379498041, "dur": 4529015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379191593, "dur": 44485, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379236100, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379236252, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379236621, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379236985, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379237569, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379237714, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379237808, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379237958, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379238085, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379238307, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379238533, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379238685, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379238896, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379239052, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379239273, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379239360, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379239469, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379239630, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379239834, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379239961, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379240058, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379240212, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379240286, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379240357, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379240468, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379240630, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379240720, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379240850, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379241067, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379241243, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379241416, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379241573, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379241750, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379241905, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379242145, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379242224, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379242703, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379242772, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379242838, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379243090, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379243286, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379243923, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379243990, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753021379244251, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379244426, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379244533, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379244617, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379244790, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379244957, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245073, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245298, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245392, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245494, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245590, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245683, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245859, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379245975, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379246033, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379246127, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753021379246664, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379246780, "dur": 1627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379248408, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379249706, "dur": 1511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379251217, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379252824, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379254425, "dur": 2045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379256470, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379258281, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379260009, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379261891, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379263720, "dur": 1827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379265547, "dur": 1997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379267544, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379269229, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379271115, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379272863, "dur": 1262, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Inspection/InspectorAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753021379272863, "dur": 2545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379275408, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379277214, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379278873, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379280631, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379282361, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379284078, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379285857, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379286491, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379288189, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379288715, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379289707, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379289797, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379289914, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379292186, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379292610, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_BB27E5BA9B36011C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379292696, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379292878, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379293579, "dur": 3333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379296913, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379297072, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_BBAE91395FB6DB68.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379297122, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379297214, "dur": 1594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379298808, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379299078, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379299230, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753021379299677, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379299745, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379301049, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379301124, "dur": 170697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379471824, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379474526, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379474687, "dur": 3785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379478473, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379478568, "dur": 5025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379483593, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379483715, "dur": 4372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379488088, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379488227, "dur": 4173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753021379492696, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379492807, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493286, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493400, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493508, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493588, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493653, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753021379493777, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493889, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379493985, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494084, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753021379494169, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494308, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494408, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753021379494463, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494601, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494720, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753021379494791, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494878, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379494990, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753021379495085, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753021379495138, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379495293, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379495386, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379495497, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753021379495568, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379495763, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379495820, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379495884, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753021379495934, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496085, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496194, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496262, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496355, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496464, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496543, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379496978, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021379497370, "dur": 4529101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753021384026500, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753021384026473, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753021384026595, "dur": 428, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753021379191596, "dur": 44499, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379236101, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753021379236252, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379236604, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379236978, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379237618, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379237760, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379237873, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379238002, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379238222, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379238436, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379238539, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379238704, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379238907, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379239064, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379239424, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379239569, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379239798, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379239962, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379240070, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379240179, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379240241, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379240513, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379240643, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379240797, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379240925, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379241380, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379241520, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379242012, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753021379242260, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753021379242541, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379242723, "dur": 968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379243746, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753021379243928, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379244095, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379244372, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379244489, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379244591, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379244693, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379244835, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379244914, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245043, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245311, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245407, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245530, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245648, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245759, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379245905, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379246055, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379246163, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379246659, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753021379246917, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379247007, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379248437, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379249735, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379251254, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379252836, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379254434, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379256527, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379258337, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379260075, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379261961, "dur": 1942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379263904, "dur": 1827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379265732, "dur": 2017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379267750, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379269361, "dur": 1863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379271225, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379272860, "dur": 1245, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Inspection/Primitives/DoubleInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753021379272860, "dur": 2554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379275414, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379277225, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379278891, "dur": 1716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379280608, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379282382, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379284144, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379285876, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379286511, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379288188, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379288706, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379289469, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379289694, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379289900, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379292489, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379292747, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379293075, "dur": 3577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379296652, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379297091, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_1A7899D4E6CA9138.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379297167, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379297286, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379298277, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379299901, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379300083, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379300303, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379301434, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379301705, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379301957, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753021379302016, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379302093, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379302577, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379302840, "dur": 168960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379471804, "dur": 4558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379476362, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379476466, "dur": 3997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379480463, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379480607, "dur": 4479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379485086, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379485235, "dur": 4391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379489627, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379489806, "dur": 7235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753021379497042, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379497135, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753021379497892, "dur": 4529191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379191603, "dur": 44510, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379236116, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379236972, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379237401, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379237473, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379237529, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379237746, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379237894, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379238009, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379238146, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379238265, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379238412, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379238517, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379238657, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379238743, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379238876, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379239268, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379239321, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379239451, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379239606, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379239807, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379239943, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379240044, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379240180, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379240265, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379240345, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379240410, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379240499, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379240649, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379240809, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379240920, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379241050, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379241160, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379241482, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753021379241659, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379241886, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379242204, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379242507, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379242624, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379243116, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379243282, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379243608, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379243924, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379244065, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753021379244315, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379244417, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379244551, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379244641, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379244854, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379244918, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245030, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245268, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245439, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245553, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245656, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245855, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379245999, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379246057, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379246274, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753021379246476, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379246559, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379246755, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379248299, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379249582, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379251092, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379252650, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379254391, "dur": 2015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379256406, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379258221, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379259957, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379261880, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379263663, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379265496, "dur": 1978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379267474, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379269315, "dur": 1877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379271192, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379272926, "dur": 1205, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Documentation/DocumentationGenerator.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753021379272926, "dur": 2609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379275535, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379277263, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379278898, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379280653, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379282401, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379284134, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379285866, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379286496, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379287999, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379288219, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379288704, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379289488, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379289690, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379289872, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379290514, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379291012, "dur": 2069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379293081, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379293251, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379293451, "dur": 1555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379295007, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379295276, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379295643, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379295702, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379297201, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379297558, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379298144, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379298622, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753021379298804, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379298879, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379300806, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379301008, "dur": 170783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379471798, "dur": 3024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379474823, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379475227, "dur": 3144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379478371, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379478466, "dur": 3570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379482036, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379482128, "dur": 4934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379487062, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379487188, "dur": 5074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379492262, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753021379492354, "dur": 5664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753021379498066, "dur": 4529041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379191610, "dur": 44511, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379236124, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379236979, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379237478, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379237572, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379237665, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379237821, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379237937, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379238079, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379238215, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379238405, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379238510, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379238757, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379238878, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379239038, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379239270, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379239388, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379239498, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379239625, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379239776, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379239894, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379240018, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379240117, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753021379240255, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379240458, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379240609, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379240691, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379240839, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379240982, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379241058, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753021379241236, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753021379241428, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379241554, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379242234, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379242341, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753021379242559, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379242854, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379244236, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379244389, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379244623, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379244739, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379244909, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245007, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753021379245173, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245337, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245416, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245521, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245612, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379245670, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245744, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379245879, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379246076, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379246248, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379246323, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753021379246637, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379246776, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379248424, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379249720, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379251276, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379252884, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379254774, "dur": 2096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379256870, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379258567, "dur": 1756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379260323, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379262177, "dur": 1855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379264032, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379265852, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379267861, "dur": 1723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379269585, "dur": 1962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379271547, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379273050, "dur": 1050, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Analysis/IAnalysis.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753021379273050, "dur": 2308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379275358, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379277167, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379278746, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379280403, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379282261, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379284020, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379285780, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379286413, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379288045, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379288221, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379288707, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379289679, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379289764, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379289862, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379290097, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379290693, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379291043, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379291331, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379291417, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379291859, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379295471, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379295640, "dur": 1282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379296923, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379297230, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379299096, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379299310, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379299618, "dur": 1485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379301103, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379301298, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379301666, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379301930, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_14FFB93D51BC3953.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379302000, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379302144, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379302323, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379302764, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379302937, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021379303078, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753021379303205, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021379303723, "dur": 4339726, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021383644777, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753021383644458, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021383645054, "dur": 59, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021383645124, "dur": 263018, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753021383909159, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753021383908907, "dur": 1539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753021383910935, "dur": 193, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021384022673, "dur": 378, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753021383911139, "dur": 111921, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753021384026474, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753021384026463, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753021384026558, "dur": 437, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753021384026998, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379191616, "dur": 44512, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379236130, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379236979, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379237471, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379237653, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379237766, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379238032, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379238161, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379238346, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379238431, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379238659, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379238721, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379238859, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379238971, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379239099, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379239445, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379239578, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379239803, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379239943, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379240049, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379240191, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379240254, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379240382, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379240531, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379240661, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379240796, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379240946, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379241016, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379241397, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379241586, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379241907, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379242183, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379242337, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753021379242396, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379243159, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379243368, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379243629, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379244353, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379244464, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379244564, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379244653, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379245011, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245113, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245287, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245340, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245422, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245538, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245634, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245722, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379245866, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379246036, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379246238, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379246305, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379246390, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379246542, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/WxEditor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753021379246634, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379246745, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379248376, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379249632, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379251129, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379252772, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379254450, "dur": 2030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379256480, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379258275, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379259993, "dur": 2023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379262016, "dur": 1875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379263892, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379265750, "dur": 2037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379267787, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379269458, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379271325, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379272944, "dur": 1172, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/SQLite/SQLite.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753021379272944, "dur": 2447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379275392, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379277192, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379278764, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379280421, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379282292, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379283989, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379285770, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379286768, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379288192, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379288692, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379289451, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379289657, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379289984, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379290953, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379291376, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379291541, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379291724, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379292120, "dur": 1295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379293416, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379293713, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_EF3566CB9F379C74.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379293776, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379294008, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379294263, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379294388, "dur": 1554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379295942, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379296253, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Editor.ref.dll_2D025F9D5B7671C7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379296368, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379296643, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379296913, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379297107, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379297838, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379298076, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379298710, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379298888, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379300191, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379300640, "dur": 2442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379303083, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753021379303239, "dur": 168650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379471893, "dur": 5313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379477206, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379477349, "dur": 4835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379482184, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379482264, "dur": 4091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379486355, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379486483, "dur": 3772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379490256, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021379490358, "dur": 6971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753021379497368, "dur": 4368991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753021383866392, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753021383866362, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753021383866530, "dur": 607, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1753021383867140, "dur": 159975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379191623, "dur": 44514, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379236140, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379237003, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379237458, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379237603, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379237739, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379238085, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379238195, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379238362, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379238544, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379238666, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379238779, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379238849, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379238954, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379239080, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379239284, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379239411, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379239544, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379239661, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379239789, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379239911, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379240031, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379240143, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753021379240259, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379240475, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379241208, "dur": 19344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379260553, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379260896, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379260976, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379261058, "dur": 4147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379265206, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379265306, "dur": 22962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379288268, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379288723, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379288805, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379289095, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379289446, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379289709, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379289864, "dur": 2026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379291890, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379292335, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_B00737B65E8631B3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379292433, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379292655, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753021379293189, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379293871, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379294057, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1753021379296562, "dur": 428, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379470155, "dur": 380, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379297428, "dur": 173126, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1753021379471791, "dur": 3071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379474864, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379475180, "dur": 5160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379480340, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379480571, "dur": 4806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379485377, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379485490, "dur": 5008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379490498, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379490597, "dur": 6604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753021379497201, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379497286, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753021379498061, "dur": 4528990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379191629, "dur": 44517, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379236149, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379236982, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379237384, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379237590, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379237723, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379237874, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379238133, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379238292, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379238536, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379238738, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379238833, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379238960, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379239275, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379239430, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379239575, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379239679, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379239832, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379239938, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379240069, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379240172, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379240681, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379240803, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379240925, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379240998, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379241132, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379241188, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379241623, "dur": 1239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379242891, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379243040, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753021379243274, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379244092, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379244164, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379244376, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379244482, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379244534, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379244609, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379244893, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379244973, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379245295, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379245359, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379245446, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379245566, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379245665, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379245852, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379245983, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379246034, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379246322, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379246406, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379246582, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753021379246655, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379246920, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379248724, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379250105, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379251635, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379253260, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379254900, "dur": 2073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379256973, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379258727, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379260545, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379262412, "dur": 1789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379264202, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379266073, "dur": 1907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379267980, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379269648, "dur": 1861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379271509, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379273042, "dur": 1030, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Canvases/CanvasProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753021379273042, "dur": 2380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379275422, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379277198, "dur": 1583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379278782, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379280562, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379282234, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379283961, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379285672, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379287393, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379287620, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379288071, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379288218, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379288710, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379289715, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379289794, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379289935, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379290440, "dur": 856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379291299, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_1A4EF4AE609B2576.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379291379, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379291451, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379291607, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379291774, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379292417, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379292481, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379293044, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379293206, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379293511, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379294667, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379295142, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_38895AFAB811F11B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379295240, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379295322, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753021379295824, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379296897, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379297466, "dur": 2734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379300201, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379300426, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379300575, "dur": 1465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379302040, "dur": 170679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379472723, "dur": 5228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379477951, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379478075, "dur": 3907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379481984, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379482211, "dur": 4692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379486905, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379487250, "dur": 4560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379491810, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753021379491915, "dur": 6066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753021379498028, "dur": 4529059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379191635, "dur": 44519, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379236157, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379236982, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379237577, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379237749, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379237983, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379238163, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379238302, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379238436, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379238554, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379238672, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379238887, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379239045, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379239406, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379239527, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379239773, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379239927, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379240017, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379240157, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379240231, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379240616, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379240776, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379240882, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379241304, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753021379241448, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379241590, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753021379241778, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753021379242048, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379242568, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379243491, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379243728, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1753021379244001, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753021379244203, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379244366, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379244477, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379244579, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379244697, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379244848, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379244952, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245035, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245128, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245382, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245510, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245594, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379245722, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245844, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379245968, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379246246, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379246343, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379246400, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379246629, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379246714, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753021379246773, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379248253, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Assets/Layer Lab/GUI Pro-CasualGame/Scripts/PanelControlCasualGame.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753021379246877, "dur": 2600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379249478, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379250962, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379252504, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379254024, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379255815, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379257749, "dur": 1696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379259445, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379261415, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379261529, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1753021379261581, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379261700, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379263574, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379265445, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379267409, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379269377, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379271157, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379273065, "dur": 895, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Inspection/Root/LudiqBehaviourEditor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753021379272854, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379274439, "dur": 1696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379276136, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379277721, "dur": 1698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379279419, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379281146, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379282814, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379284592, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379286339, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379287539, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379288200, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379288695, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379289458, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379289665, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379289887, "dur": 2265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379292152, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379292587, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_F3F1807631F79CD9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379292720, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379292825, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379294154, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379294301, "dur": 3909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379298211, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379298489, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379298844, "dur": 1966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379300811, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379300990, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379301089, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379301204, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379301641, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379301901, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379302077, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753021379302200, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379302349, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379302622, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379302794, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379302930, "dur": 168881, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379471815, "dur": 3888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379475704, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379475774, "dur": 4177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379479951, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379480059, "dur": 4172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379484232, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379484331, "dur": 4678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379489009, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379489165, "dur": 7644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021379496809, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379496934, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021379497300, "dur": 4147204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021383644799, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753021383644507, "dur": 1666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021383646673, "dur": 203, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021383862591, "dur": 460, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753021383646894, "dur": 216165, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753021383866367, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753021383866355, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753021383866556, "dur": 546, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753021383867104, "dur": 160005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379191642, "dur": 44521, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379236164, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379236970, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379237643, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379237780, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379237906, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379238002, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379238103, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379238282, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379238424, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379238527, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379238650, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379238740, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379238842, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379238978, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379239099, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379239292, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379239379, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379239516, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379239744, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379239865, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379239996, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379240086, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753021379240215, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379240304, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379240358, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379240547, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379240702, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379240846, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753021379240975, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379241165, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379241323, "dur": 17796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379259120, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379259661, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379259749, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379260034, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379261917, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379263806, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379265593, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379267551, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379269241, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379271094, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379272841, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379273168, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379273318, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379273455, "dur": 129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379273632, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Core/Variables/Variables.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753021379273584, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379274973, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379276748, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379278347, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379279986, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379281835, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379283549, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379285331, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379286949, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379287869, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379288206, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379288700, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379289505, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379289711, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379289895, "dur": 1657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379291552, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379291985, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379292211, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379294007, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379294096, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379294210, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379294350, "dur": 1666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379296018, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379296400, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379296516, "dur": 1881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379298398, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379298766, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_BEADB10929D2C8CB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379298873, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379299082, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379299177, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753021379299705, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379299774, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379301146, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379301370, "dur": 170489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379471891, "dur": 4269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379476160, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379476387, "dur": 4550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379480937, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379481046, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379485714, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379485806, "dur": 5634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379491441, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753021379491780, "dur": 6026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753021379497888, "dur": 4529184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753021384028864, "dur": 865, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 3696, "ts": 1753021384044050, "dur": 9595, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 3696, "ts": 1753021384053768, "dur": 1405, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 3696, "ts": 1753021384040302, "dur": 15514, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}