{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 2570, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 2570, "ts": 1752889885600673, "dur": 750, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 2570, "ts": 1752889885606866, "dur": 598, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752889880011864, "dur": 14627, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752889880026497, "dur": 122464, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752889880148971, "dur": 112981, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 2570, "ts": 1752889885607469, "dur": 40, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880009684, "dur": 56199, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880065886, "dur": 5501963, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880067646, "dur": 130486, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880198145, "dur": 1704, "ph": "X", "name": "ProcessMessages 8115", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880199852, "dur": 67, "ph": "X", "name": "ReadAsync 8115", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880199921, "dur": 3, "ph": "X", "name": "ProcessMessages 8167", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880199925, "dur": 63, "ph": "X", "name": "ReadAsync 8167", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880199994, "dur": 2, "ph": "X", "name": "ProcessMessages 1241", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880199997, "dur": 54, "ph": "X", "name": "ReadAsync 1241", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200052, "dur": 1, "ph": "X", "name": "ProcessMessages 1417", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200053, "dur": 51, "ph": "X", "name": "ReadAsync 1417", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200108, "dur": 29, "ph": "X", "name": "ReadAsync 1736", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200139, "dur": 1, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200142, "dur": 31, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200177, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200179, "dur": 28, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200209, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200210, "dur": 51, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880200264, "dur": 2023, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202290, "dur": 3, "ph": "X", "name": "ProcessMessages 8129", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202295, "dur": 213, "ph": "X", "name": "ReadAsync 8129", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202510, "dur": 2, "ph": "X", "name": "ProcessMessages 4321", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202513, "dur": 31, "ph": "X", "name": "ReadAsync 4321", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202546, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202547, "dur": 36, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880202586, "dur": 661, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203249, "dur": 1, "ph": "X", "name": "ProcessMessages 3792", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203258, "dur": 228, "ph": "X", "name": "ReadAsync 3792", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203487, "dur": 1, "ph": "X", "name": "ProcessMessages 1673", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203488, "dur": 206, "ph": "X", "name": "ReadAsync 1673", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203696, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203698, "dur": 30, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203730, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203731, "dur": 134, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880203868, "dur": 482, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204352, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204354, "dur": 38, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204394, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204396, "dur": 271, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204670, "dur": 251, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204922, "dur": 2, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880204925, "dur": 141, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205068, "dur": 1, "ph": "X", "name": "ProcessMessages 1358", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205071, "dur": 187, "ph": "X", "name": "ReadAsync 1358", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205259, "dur": 2, "ph": "X", "name": "ProcessMessages 2290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205262, "dur": 43, "ph": "X", "name": "ReadAsync 2290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205308, "dur": 38, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205347, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205349, "dur": 338, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205688, "dur": 2, "ph": "X", "name": "ProcessMessages 3563", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205691, "dur": 184, "ph": "X", "name": "ReadAsync 3563", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205877, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205878, "dur": 33, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880205915, "dur": 177, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206094, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206095, "dur": 28, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206126, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206141, "dur": 35, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206178, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206180, "dur": 31, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206213, "dur": 1, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206215, "dur": 33, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206249, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206252, "dur": 38, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206291, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206300, "dur": 28, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206330, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206332, "dur": 338, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206671, "dur": 3, "ph": "X", "name": "ProcessMessages 4307", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206674, "dur": 40, "ph": "X", "name": "ReadAsync 4307", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206716, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206717, "dur": 30, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206750, "dur": 171, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206923, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206925, "dur": 36, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206962, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880206964, "dur": 145, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207117, "dur": 26, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207144, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207145, "dur": 22, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207170, "dur": 157, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207328, "dur": 1, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207331, "dur": 36, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207368, "dur": 1, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207498, "dur": 20, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207519, "dur": 1, "ph": "X", "name": "ProcessMessages 2243", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207521, "dur": 323, "ph": "X", "name": "ReadAsync 2243", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207845, "dur": 1, "ph": "X", "name": "ProcessMessages 2243", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880207847, "dur": 310, "ph": "X", "name": "ReadAsync 2243", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208159, "dur": 1, "ph": "X", "name": "ProcessMessages 2096", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208160, "dur": 84, "ph": "X", "name": "ReadAsync 2096", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208246, "dur": 1, "ph": "X", "name": "ProcessMessages 1464", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208478, "dur": 23, "ph": "X", "name": "ReadAsync 1464", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208502, "dur": 11, "ph": "X", "name": "ProcessMessages 4761", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208514, "dur": 290, "ph": "X", "name": "ReadAsync 4761", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208815, "dur": 33, "ph": "X", "name": "ReadAsync 1448", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880208851, "dur": 190, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209042, "dur": 1, "ph": "X", "name": "ProcessMessages 1297", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209065, "dur": 214, "ph": "X", "name": "ReadAsync 1297", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209280, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209282, "dur": 120, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209404, "dur": 1, "ph": "X", "name": "ProcessMessages 1423", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209719, "dur": 27, "ph": "X", "name": "ReadAsync 1423", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209747, "dur": 2, "ph": "X", "name": "ProcessMessages 2769", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880209750, "dur": 64, "ph": "X", "name": "ReadAsync 2769", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880210156, "dur": 1, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880210159, "dur": 346, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880210507, "dur": 3, "ph": "X", "name": "ProcessMessages 5146", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880210511, "dur": 44, "ph": "X", "name": "ReadAsync 5146", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880210557, "dur": 3, "ph": "X", "name": "ProcessMessages 4066", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880210561, "dur": 976, "ph": "X", "name": "ReadAsync 4066", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211538, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211543, "dur": 171, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211715, "dur": 1, "ph": "X", "name": "ProcessMessages 1934", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211717, "dur": 33, "ph": "X", "name": "ReadAsync 1934", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211752, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211754, "dur": 32, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211787, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211789, "dur": 32, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211823, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211830, "dur": 30, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211863, "dur": 32, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211897, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880211898, "dur": 209, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880212109, "dur": 2, "ph": "X", "name": "ProcessMessages 2641", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880212295, "dur": 18, "ph": "X", "name": "ReadAsync 2641", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880212315, "dur": 1, "ph": "X", "name": "ProcessMessages 1404", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880212316, "dur": 913, "ph": "X", "name": "ReadAsync 1404", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880213233, "dur": 37620, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880250858, "dur": 4, "ph": "X", "name": "ProcessMessages 4214", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880252038, "dur": 92, "ph": "X", "name": "ReadAsync 4214", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880252132, "dur": 4, "ph": "X", "name": "ProcessMessages 8142", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880252137, "dur": 21, "ph": "X", "name": "ReadAsync 8142", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880252175, "dur": 653, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880252831, "dur": 16, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880252849, "dur": 538, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880253390, "dur": 250, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880253642, "dur": 682, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880254327, "dur": 137, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880254467, "dur": 445, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880254913, "dur": 170, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880255086, "dur": 841, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880255929, "dur": 170, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880256101, "dur": 401, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880256504, "dur": 155, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880256662, "dur": 614, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880257279, "dur": 348, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880257629, "dur": 279, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880257910, "dur": 107, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880258162, "dur": 39, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880258203, "dur": 321, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880258526, "dur": 293, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880258821, "dur": 149, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880258972, "dur": 450, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880259425, "dur": 189, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880259619, "dur": 2, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880259623, "dur": 690, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880260315, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880260317, "dur": 754, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261075, "dur": 140, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261217, "dur": 255, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261474, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261477, "dur": 138, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261618, "dur": 265, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261899, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261901, "dur": 76, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261979, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880261981, "dur": 242, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262226, "dur": 166, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262393, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262395, "dur": 207, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262603, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262605, "dur": 149, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262757, "dur": 36, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262795, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262797, "dur": 178, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880262978, "dur": 227, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880263207, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880263209, "dur": 624, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880263835, "dur": 2, "ph": "X", "name": "ProcessMessages 2767", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880263847, "dur": 125, "ph": "X", "name": "ReadAsync 2767", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880263975, "dur": 147, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880264124, "dur": 285, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880264412, "dur": 202, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880264616, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880264618, "dur": 33, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880264653, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880264817, "dur": 238, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880265056, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880265058, "dur": 206, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880265266, "dur": 180, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880265448, "dur": 393, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880265842, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880265844, "dur": 267, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266114, "dur": 126, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266243, "dur": 116, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266361, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266368, "dur": 43, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266414, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266416, "dur": 277, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266695, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880266696, "dur": 423, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880267121, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880267123, "dur": 297, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880267421, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880267423, "dur": 30, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880267461, "dur": 294, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880267757, "dur": 1246, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880269006, "dur": 3, "ph": "X", "name": "ProcessMessages 6457", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880269009, "dur": 93, "ph": "X", "name": "ReadAsync 6457", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880269104, "dur": 159, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880269270, "dur": 238, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880270150, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880270152, "dur": 30, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880270183, "dur": 1, "ph": "X", "name": "ProcessMessages 1674", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880270184, "dur": 1319, "ph": "X", "name": "ReadAsync 1674", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880271505, "dur": 2, "ph": "X", "name": "ProcessMessages 3378", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880271508, "dur": 89, "ph": "X", "name": "ReadAsync 3378", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880271599, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880271601, "dur": 379, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880271982, "dur": 2, "ph": "X", "name": "ProcessMessages 2050", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880271985, "dur": 30, "ph": "X", "name": "ReadAsync 2050", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272018, "dur": 329, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272349, "dur": 1, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272351, "dur": 40, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272394, "dur": 29, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272425, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272426, "dur": 402, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880272831, "dur": 630, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880273463, "dur": 147, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880273612, "dur": 663, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274278, "dur": 91, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274371, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274373, "dur": 241, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274620, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274624, "dur": 42, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274677, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880274720, "dur": 1296, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880276017, "dur": 5, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880276031, "dur": 38, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880276073, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880276109, "dur": 367, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880276478, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880276482, "dur": 715, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277200, "dur": 7, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277208, "dur": 106, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277317, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277388, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277389, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277435, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277469, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277473, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277781, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277783, "dur": 36, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880277820, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278074, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278076, "dur": 66, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278145, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278182, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278267, "dur": 226, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278505, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278508, "dur": 40, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278549, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880278551, "dur": 914, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880279466, "dur": 2, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880279760, "dur": 208, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880279970, "dur": 2, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880279973, "dur": 9793, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880289772, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880289775, "dur": 2634, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880292414, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880292594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880292596, "dur": 813, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880293425, "dur": 416, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880293842, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880293844, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880293926, "dur": 3734, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880298888, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880298890, "dur": 16604, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880315499, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880315501, "dur": 248, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880315751, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880315753, "dur": 574, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880316338, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880316344, "dur": 19816, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880336167, "dur": 345, "ph": "X", "name": "ProcessMessages 2312", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880336514, "dur": 93582, "ph": "X", "name": "ReadAsync 2312", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430105, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430109, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430158, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430213, "dur": 47, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430261, "dur": 347, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430611, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430651, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880430695, "dur": 2936, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880433640, "dur": 1082, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880434726, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880434728, "dur": 2747, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880437478, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880437482, "dur": 869, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880438559, "dur": 1052, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880439617, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880439620, "dur": 490, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880440112, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880440326, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880440327, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880440624, "dur": 321, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880440949, "dur": 1038, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880441990, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880441992, "dur": 328, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880442324, "dur": 943, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880443270, "dur": 968, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880444242, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880444329, "dur": 783, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880445121, "dur": 393, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880445519, "dur": 949, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880446471, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880446597, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880446602, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880446847, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880446849, "dur": 554, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880447413, "dur": 633, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880448051, "dur": 1060, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880449115, "dur": 483, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880449601, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880449752, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880449755, "dur": 1011, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880450768, "dur": 321, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880451092, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880451094, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880451329, "dur": 529, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880451862, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452055, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452108, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452247, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452249, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452314, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452439, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452481, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452555, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452639, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452746, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880452780, "dur": 249, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453032, "dur": 275, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453311, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453351, "dur": 180, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453532, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453572, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453574, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453614, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453663, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453697, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453785, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453887, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453930, "dur": 38, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880453971, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880454021, "dur": 508, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880454531, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880454532, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880454577, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880454622, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880454885, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455355, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455453, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455564, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455601, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455678, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455806, "dur": 31, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880455840, "dur": 395, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880456237, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880456239, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880456324, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889880456398, "dur": 4701315, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885157783, "dur": 368, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885158154, "dur": 3353, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885161518, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885161525, "dur": 3449, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885164979, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885164986, "dur": 232825, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885397830, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885397834, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885397904, "dur": 1, "ph": "X", "name": "ProcessMessages 5636", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885399327, "dur": 39, "ph": "X", "name": "ReadAsync 5636", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885399369, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885399406, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885399442, "dur": 29, "ph": "X", "name": "ProcessMessages 5012", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885399472, "dur": 3439, "ph": "X", "name": "ReadAsync 5012", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885402916, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885402920, "dur": 390, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885403312, "dur": 37, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885403350, "dur": 24838, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885428518, "dur": 22, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885428542, "dur": 2797, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885431345, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885431347, "dur": 124707, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556061, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556063, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556113, "dur": 22, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556136, "dur": 18, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556156, "dur": 19, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556176, "dur": 28, "ph": "X", "name": "ProcessMessages 6644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885556205, "dur": 3097, "ph": "X", "name": "ReadAsync 6644", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885559306, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885559309, "dur": 561, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885559871, "dur": 21, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885559894, "dur": 48, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885559946, "dur": 363, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 12884901888, "ts": 1752889885560310, "dur": 7210, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 2570, "ts": 1752889885607511, "dur": 1985, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 8589934592, "ts": 1752889880004509, "dur": 257492, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752889880262005, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 8589934592, "ts": 1752889880262011, "dur": 3478, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 2570, "ts": 1752889885609498, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 4294967296, "ts": 1752889879948912, "dur": 5621076, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752889879957481, "dur": 32771, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752889885570109, "dur": 5619, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752889885573104, "dur": 310, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 4294967296, "ts": 1752889885575790, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 39945, "tid": 2570, "ts": 1752889885609514, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752889880057876, "dur": 4627, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752889880062555, "dur": 18803, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752889880081456, "dur": 182, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752889880082155, "dur": 116535, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752889880199011, "dur": 1896, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752889880201373, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752889880201714, "dur": 1551, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752889880212311, "dur": 213, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752889880214104, "dur": 2536, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752889880220075, "dur": 33044, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752889880081645, "dur": 192134, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752889880273786, "dur": 5286909, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752889885560893, "dur": 502, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752889880081536, "dur": 192266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880273806, "dur": 10839, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880284645, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880285906, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880287108, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880288599, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880290075, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880291607, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880293132, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880294717, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880296164, "dur": 1656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880297820, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880299382, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880301016, "dur": 1645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880302661, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880304212, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880305594, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880306982, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880308293, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880309597, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880310832, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880312266, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880313641, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880314886, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880315410, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880315778, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880315886, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880316036, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880316401, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880317076, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752889880317541, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880320019, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880320631, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752889880320940, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880321053, "dur": 2905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880323958, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880324405, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752889880324846, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880325001, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880325942, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880326157, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_98EA1327509A07AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752889880326224, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880326326, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752889880326766, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880326934, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880327703, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880327779, "dur": 105159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880432942, "dur": 4716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880437659, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880438052, "dur": 3400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880441452, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880441599, "dur": 3582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880445182, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880445255, "dur": 4664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880449920, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880450008, "dur": 5618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752889880455626, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880455787, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880455906, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880455964, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752889880456017, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880456120, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880456227, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880456293, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889880456793, "dur": 5103348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752889885560156, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752889885560145, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752889885560230, "dur": 387, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752889885560620, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880081537, "dur": 192272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880273847, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752889880274024, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880274390, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880274817, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880275321, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880275471, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880275594, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880275729, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880275840, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880275924, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880276045, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880276155, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880276236, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880276345, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880276503, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880276598, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880276705, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880276879, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880276979, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880277169, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880277362, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880277453, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880277522, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752889880277917, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880278001, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752889880278213, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752889880278264, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880278393, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880278486, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880278554, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880278654, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880278737, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752889880278943, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279038, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279156, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279264, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279493, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279615, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279708, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880279876, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752889880280043, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880280122, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880280328, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880280419, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880280511, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880280590, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880280851, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880281143, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880281272, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880281467, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880281567, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880281640, "dur": 2433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880284073, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880285281, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880286548, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880287785, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880289290, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880290847, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880292383, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880294019, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880295588, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880297288, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880298911, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880300399, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880302091, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880303686, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880305144, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880306456, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880307746, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880309098, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880310297, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880311574, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880312909, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880314240, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880314477, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880315756, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880315862, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880316044, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880316394, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880317069, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880317655, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880317785, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880318807, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880319150, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880319323, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880319656, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880319911, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880321210, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880321565, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880321703, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880322232, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880322383, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880323363, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880323743, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880323927, "dur": 1924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880325851, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880326359, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880326512, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880326718, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880327474, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752889880327729, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880327869, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880328282, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880328425, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880328526, "dur": 104417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880432944, "dur": 5000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880437944, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880438097, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880438194, "dur": 3061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880441255, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880441373, "dur": 4949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880446322, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880446397, "dur": 4147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880450544, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880450630, "dur": 5751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752889880456381, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880456457, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752889880457210, "dur": 5103451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880081537, "dur": 192288, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880273856, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752889880274082, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880274396, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880274818, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880275308, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880275450, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880275592, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880275730, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880275801, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880275898, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880276008, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880276122, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880276195, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880276366, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880276567, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880276645, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880276738, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880276868, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880276968, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880277066, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880277431, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880277496, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880277555, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752889880278026, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752889880278310, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880278377, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880278484, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880278563, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880278740, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880278924, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279014, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279099, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279178, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279279, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279441, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279552, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279662, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880279862, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752889880279999, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880280075, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880280253, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880280351, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880280447, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880280572, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880280804, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752889880281093, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880281199, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880281270, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880281347, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880281519, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880281617, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880283029, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880284272, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880285446, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880286708, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880288102, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880289584, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880291208, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880292702, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880294257, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880295828, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880297434, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880299030, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880300534, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880302214, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880303711, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880305172, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880306398, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880307676, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880309059, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880310284, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880311553, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880312820, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880314213, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880314399, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880315015, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880315413, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880315816, "dur": 151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880315967, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880316049, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880316428, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880317092, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880317504, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880317583, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889880318879, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880319152, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889880320363, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880320595, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880320648, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880321022, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880321099, "dur": 2958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889880324057, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880324309, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880324492, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880324844, "dur": 1988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889880326833, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880327032, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880327230, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880327427, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889880328144, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880328226, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880328336, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880328392, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880328492, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889880328729, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880328878, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752889880328956, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889885157318, "dur": 54, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889880329372, "dur": 4828045, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889885161611, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752889885160857, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889885162123, "dur": 93, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889885162240, "dur": 266854, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752889885430112, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752889885429884, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752889885432046, "dur": 202, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889885556942, "dur": 241, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752889885432264, "dur": 124928, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752889885560144, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752889885560136, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752889885560222, "dur": 403, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752889880081542, "dur": 192294, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880273846, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880274823, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880275276, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880275390, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880275610, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880275735, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880275995, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880276091, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880276170, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880276256, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880276336, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880276419, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880276582, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880276676, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880276854, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880276920, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880276999, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752889880277168, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880277378, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880277536, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752889880277998, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880278093, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752889880278331, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880278438, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880278624, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880278723, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880278816, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880278868, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880278941, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279024, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279112, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279214, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279319, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279495, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279610, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279701, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279763, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279837, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880279993, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280065, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280129, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280313, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280407, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280497, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280575, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280748, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880280829, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880281148, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880281246, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880281331, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880281449, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880281555, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880281651, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880283103, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880284441, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880285660, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880286883, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880288309, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880289802, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880291381, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880292940, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880294512, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880294729, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880294835, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880296376, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880298047, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880299553, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880301278, "dur": 1766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880303045, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880304484, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880305796, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880307122, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880308458, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880309769, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880310968, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880312330, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880313682, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880314930, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880315472, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880315918, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880316046, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880316404, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880317103, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880317737, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880318900, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880319308, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_BB27E5BA9B36011C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880319374, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880319562, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880319935, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880319992, "dur": 1349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880321341, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880321885, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880321942, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880322297, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880322392, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880324069, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880324545, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880324614, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880324876, "dur": 2241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880327117, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880327315, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880327465, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752889880327705, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880327790, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880328205, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880328451, "dur": 104483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880432935, "dur": 2119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880435054, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880435207, "dur": 6659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880441866, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880441979, "dur": 5311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880447291, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880447351, "dur": 4446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880451798, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752889880452004, "dur": 5122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752889880457197, "dur": 5103479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880081543, "dur": 192309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880273857, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880274829, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880275369, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880275496, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880275628, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880275754, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880275813, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880275926, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880276036, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880276138, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880276333, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880276417, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880276591, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880276693, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880276833, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880276883, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880276997, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880277082, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880277342, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880277441, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880277831, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752889880278075, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752889880278309, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880278406, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880278491, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880278599, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752889880278771, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880278859, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752889880279153, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279254, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279344, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279468, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279591, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279685, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279741, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279931, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880279982, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880280057, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880280283, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880280374, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880280472, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880280556, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880280634, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752889880280821, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880281139, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880281281, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880281443, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880281540, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880281751, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880283269, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880284541, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880285778, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880287009, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880288615, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880290109, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880291636, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880293194, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880294807, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880296412, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880298062, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880299763, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880301396, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880303109, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880304511, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880305835, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880307119, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880308442, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880309718, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880310916, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880312421, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880313796, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880314803, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880315839, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880316052, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880316419, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880317085, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880317368, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880317475, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880318607, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880319222, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880319296, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_AEDA0D01FA0B3E02.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880319391, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880319832, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880320021, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880322563, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880322718, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880323681, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880323952, "dur": 2144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880326097, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880326405, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752889880326801, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880327594, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880327692, "dur": 105229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880432922, "dur": 8112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880441035, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880441095, "dur": 4815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880445910, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880446039, "dur": 4320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880450359, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880450447, "dur": 6082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752889880456585, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752889880457361, "dur": 5103307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880081548, "dur": 192313, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880273871, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880274802, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880275269, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880275417, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880275566, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880275730, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880275845, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880275921, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880276021, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880276123, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880276181, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880276318, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880276418, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880276557, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880276707, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880276852, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880276908, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880277027, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880277185, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880277330, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880277406, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752889880277560, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880277868, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880277949, "dur": 14805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880292754, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880293227, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880293301, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880293519, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880295066, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880296623, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880298285, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880299924, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880301577, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880303229, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880304713, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880306039, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880307297, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880308660, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880309923, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880311201, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880312429, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880313787, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880315124, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880315414, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880315839, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880315977, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880316051, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880316432, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880317105, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880317359, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880317459, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880317939, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880318890, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880319221, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880319333, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880319736, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880320278, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880321566, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880321922, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_38895AFAB811F11B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880322043, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880322134, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880322448, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880322526, "dur": 1282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880323808, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880324213, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880325563, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880325680, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752889880325907, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880326040, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880327341, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880327571, "dur": 105338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880432912, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880435276, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880435598, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880439228, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880439563, "dur": 3610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880443173, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880443282, "dur": 4213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880447496, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880447719, "dur": 4428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880452147, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752889880452218, "dur": 5009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752889880457275, "dur": 5103381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880081558, "dur": 192318, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880273883, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880274816, "dur": 542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880275363, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880275472, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880275655, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880275917, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880275987, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880276081, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880276158, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880276248, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880276368, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880276420, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880276611, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880276710, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880276855, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880276920, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880277132, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880277241, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880277333, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880277386, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880277477, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752889880277939, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880278087, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752889880278311, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880278368, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880278444, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880278636, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752889880278799, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880278864, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880278962, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279040, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279247, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752889880279405, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279517, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279636, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279714, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279917, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880279970, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280045, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Wx.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752889880280180, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280268, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280386, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280480, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280548, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280644, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880280838, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880281157, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880281246, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880281490, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880281575, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880282976, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880284497, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880285748, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880286981, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880288588, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880290082, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880291604, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880293145, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880294856, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880296342, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880297942, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880299498, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880301119, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880302768, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880304206, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880305556, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880307007, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880308341, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880309672, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880310876, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880312230, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880313554, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880314808, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880315480, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880315838, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880315988, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880316389, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880317089, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880317656, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880318799, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880319281, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_4404C737633913B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880319350, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880319416, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880319788, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880320243, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880320330, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880321851, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880322109, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880322498, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880322892, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880322962, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880324370, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880324700, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880325679, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880326116, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_F4378826A9C5CD14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880326195, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880326288, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752889880326821, "dur": 789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880327611, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880327825, "dur": 105129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880432955, "dur": 7525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880440482, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880440680, "dur": 3627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880444307, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880444370, "dur": 3789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880448159, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752889880448240, "dur": 4391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880452667, "dur": 4643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752889880457350, "dur": 5103336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880081625, "dur": 192260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880273891, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880274821, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880275242, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880275340, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880275451, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880275613, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880275837, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880275916, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880275984, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880276089, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880276176, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880276316, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880276516, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880276616, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880276718, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880276866, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880276966, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880277049, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752889880277306, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880277415, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752889880277619, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880277834, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752889880278071, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880278149, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752889880278478, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880278592, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880278653, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880278726, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752889880278931, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880279058, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752889880279233, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880279326, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880279510, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880279621, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880279718, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880279819, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280018, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280087, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280243, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280337, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280435, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280534, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280614, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880280876, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880281154, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880281249, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880281453, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880281559, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880281635, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880283065, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880284306, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880285506, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880286744, "dur": 1352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880288096, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880289566, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880291179, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880292670, "dur": 1546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880294216, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880295710, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880297331, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880298901, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880300430, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880302061, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880303567, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880305036, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880306316, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880307595, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880308984, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880310180, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880311439, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880312742, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880314135, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880314590, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880316011, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880316414, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880317080, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880317498, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880317561, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880318436, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880318695, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880318861, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880318939, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880319069, "dur": 1457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880320526, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880320823, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880321162, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880321405, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880321671, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880321731, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880322244, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880322415, "dur": 1319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880323734, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880324035, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880324135, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880324645, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880324775, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880325913, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880326244, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Editor.ref.dll_DAC173EE5EC432FE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880326324, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880326613, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880326667, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880327260, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752889880327444, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880328073, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880328286, "dur": 104627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880432913, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880435553, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880435847, "dur": 3416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880439263, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880439337, "dur": 2916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880442253, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880442372, "dur": 3527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880445900, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880446021, "dur": 4351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880450372, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880450494, "dur": 5546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752889880456040, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880456192, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880456248, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880456476, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752889880457271, "dur": 5103410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880081634, "dur": 192259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880273898, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880274818, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880275366, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880275474, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880275618, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880275781, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880275851, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880276030, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880276132, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880276206, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880276269, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880276327, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880276417, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880276565, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880276656, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880276736, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880276924, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880277038, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752889880277234, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880277380, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880277623, "dur": 16327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880293950, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880294331, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880294431, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880294517, "dur": 2950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880297467, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880297534, "dur": 18570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880316104, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880316445, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880316522, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880316805, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880317071, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880317312, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880317440, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880318931, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880319355, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880319520, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752889880320063, "dur": 1191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880321254, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880321462, "dur": 1689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1752889880323197, "dur": 832, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880430886, "dur": 751, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880324501, "dur": 107157, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1752889880432907, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880435396, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880435744, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880438343, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880438460, "dur": 4475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880442935, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880443057, "dur": 4300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880447357, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889880447458, "dur": 4166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880451678, "dur": 5051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752889880456782, "dur": 4946889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752889885403688, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752889885403675, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752889885403830, "dur": 440, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752889885404277, "dur": 156375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880081641, "dur": 192259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880273904, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880274829, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880275417, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880275494, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880275600, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880275807, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880275881, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880276011, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880276092, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880276162, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880276344, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880276407, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880276568, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880276671, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880276857, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880276918, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880276990, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752889880277250, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880277339, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880277439, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880277506, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880277586, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880277851, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278038, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278163, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278386, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752889880278547, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278614, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278767, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278848, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880278933, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279128, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279224, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279312, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279504, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279656, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279733, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279829, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880279965, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280034, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280106, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280259, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280361, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280463, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280549, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280662, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880280780, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752889880280904, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880281161, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880281262, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880281440, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880281514, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880281599, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880282986, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880284300, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880285498, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880286772, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880288092, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880289557, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880291167, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880292664, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880294193, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880295693, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880297311, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880298940, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880300454, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880302175, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880303646, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880305115, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880306389, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880307688, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880309053, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880310280, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880311600, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880312869, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880314224, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880314477, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880315806, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880315897, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880316046, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880316424, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880317077, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880317406, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880317515, "dur": 1174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880318689, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880319166, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_4D635C4BE46FE4CD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880319267, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880319821, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880319930, "dur": 1335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880321265, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880321443, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_905C1C40FD7BA469.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880321504, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880321567, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880321817, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880322393, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880322509, "dur": 1294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880323803, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880324130, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880325262, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880325505, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880326954, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880327166, "dur": 1713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880328880, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752889880328976, "dur": 103974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880432950, "dur": 4837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880437788, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880437901, "dur": 6136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880444039, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880444196, "dur": 4644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880448840, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880448907, "dur": 4014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752889880454165, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880454642, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880455831, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752889880455884, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880456101, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880456270, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880456622, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889880456686, "dur": 4704359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889885161560, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752889885161050, "dur": 3313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752889885165458, "dur": 363, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889885398644, "dur": 1783, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752889885165854, "dur": 234592, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752889885403680, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752889885403666, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752889885403889, "dur": 399, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752889885404289, "dur": 156402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752889885567336, "dur": 701, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 2570, "ts": 1752889885611530, "dur": 5416, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 2570, "ts": 1752889885617125, "dur": 2221, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 2570, "ts": 1752889885604675, "dur": 16466, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}