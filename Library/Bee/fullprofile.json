{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 39945, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 39945, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 39945, "tid": 3070, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 39945, "tid": 3070, "ts": 1752935509986707, "dur": 43, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509986777, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 39945, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509624747, "dur": 12365, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509637112, "dur": 348146, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509637202, "dur": 75, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509637292, "dur": 119051, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509756363, "dur": 57, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509756421, "dur": 739, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757179, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757181, "dur": 21, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757214, "dur": 20, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757254, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757256, "dur": 26, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757301, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757302, "dur": 19, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757335, "dur": 16, "ph": "X", "name": "ReadAsync 1355", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757360, "dur": 24, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757387, "dur": 50, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757438, "dur": 1, "ph": "X", "name": "ProcessMessages 1545", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757440, "dur": 57, "ph": "X", "name": "ReadAsync 1545", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757503, "dur": 3, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757507, "dur": 55, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757564, "dur": 1, "ph": "X", "name": "ProcessMessages 1597", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757565, "dur": 65, "ph": "X", "name": "ReadAsync 1597", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757632, "dur": 1, "ph": "X", "name": "ProcessMessages 1825", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757634, "dur": 42, "ph": "X", "name": "ReadAsync 1825", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757678, "dur": 1, "ph": "X", "name": "ProcessMessages 1521", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757680, "dur": 36, "ph": "X", "name": "ReadAsync 1521", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757717, "dur": 1, "ph": "X", "name": "ProcessMessages 1129", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757719, "dur": 24, "ph": "X", "name": "ReadAsync 1129", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757746, "dur": 25, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757801, "dur": 30, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757833, "dur": 1, "ph": "X", "name": "ProcessMessages 1987", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757834, "dur": 32, "ph": "X", "name": "ReadAsync 1987", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757868, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757870, "dur": 39, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757910, "dur": 1, "ph": "X", "name": "ProcessMessages 1337", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757911, "dur": 31, "ph": "X", "name": "ReadAsync 1337", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757943, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757945, "dur": 34, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509757982, "dur": 69, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758052, "dur": 1, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758054, "dur": 56, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758113, "dur": 48, "ph": "X", "name": "ReadAsync 1829", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758163, "dur": 1, "ph": "X", "name": "ProcessMessages 1554", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758165, "dur": 26, "ph": "X", "name": "ReadAsync 1554", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758193, "dur": 29, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758224, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758248, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758270, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758291, "dur": 36, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758329, "dur": 21, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758352, "dur": 20, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758373, "dur": 36, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758412, "dur": 19, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758447, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758449, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758475, "dur": 19, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758502, "dur": 39, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758543, "dur": 19, "ph": "X", "name": "ReadAsync 1076", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758564, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758586, "dur": 32, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758620, "dur": 23, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758645, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758669, "dur": 39, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758710, "dur": 71, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758782, "dur": 5, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758787, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758810, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758833, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758854, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758894, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758917, "dur": 31, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758951, "dur": 21, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758974, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509758996, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759017, "dur": 22, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759041, "dur": 50, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759093, "dur": 17, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759112, "dur": 37, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759151, "dur": 29, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759182, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759201, "dur": 55, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759258, "dur": 34, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759295, "dur": 26, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759323, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759345, "dur": 23, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759370, "dur": 39, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759412, "dur": 33, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759447, "dur": 19, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759468, "dur": 48, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759517, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759519, "dur": 31, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759552, "dur": 32, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759586, "dur": 32, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759619, "dur": 24, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759645, "dur": 31, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759678, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759712, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759777, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759799, "dur": 18, "ph": "X", "name": "ReadAsync 1354", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759819, "dur": 29, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759850, "dur": 19, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759871, "dur": 41, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759919, "dur": 25, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759946, "dur": 20, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509759969, "dur": 34, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760005, "dur": 23, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760029, "dur": 32, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760064, "dur": 23, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760088, "dur": 37, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760127, "dur": 24, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760153, "dur": 32, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760187, "dur": 25, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760214, "dur": 28, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760244, "dur": 41, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760287, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760310, "dur": 53, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760365, "dur": 33, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760400, "dur": 22, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760424, "dur": 30, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760456, "dur": 43, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760501, "dur": 20, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760523, "dur": 30, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760555, "dur": 22, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760579, "dur": 24, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760604, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760626, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760647, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760666, "dur": 37, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760705, "dur": 35, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760743, "dur": 33, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760777, "dur": 30, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760809, "dur": 23, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760834, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760868, "dur": 23, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760899, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760920, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760952, "dur": 33, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509760987, "dur": 32, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761021, "dur": 33, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761055, "dur": 2, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761058, "dur": 22, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761082, "dur": 22, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761106, "dur": 32, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761139, "dur": 31, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761173, "dur": 25, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761200, "dur": 27, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761229, "dur": 55, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761286, "dur": 24, "ph": "X", "name": "ReadAsync 1149", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761312, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761331, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761367, "dur": 87, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761456, "dur": 27, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761485, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761504, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761530, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761554, "dur": 42, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761599, "dur": 18, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761619, "dur": 34, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761655, "dur": 27, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761684, "dur": 21, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761724, "dur": 41, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761767, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761769, "dur": 33, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761804, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761805, "dur": 21, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761828, "dur": 33, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761864, "dur": 22, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761887, "dur": 30, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761919, "dur": 29, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761950, "dur": 24, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509761991, "dur": 27, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762020, "dur": 26, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762049, "dur": 44, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762095, "dur": 49, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762146, "dur": 1, "ph": "X", "name": "ProcessMessages 1288", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762148, "dur": 34, "ph": "X", "name": "ReadAsync 1288", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762184, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762186, "dur": 51, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762239, "dur": 1, "ph": "X", "name": "ProcessMessages 1119", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762241, "dur": 26, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762270, "dur": 35, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762308, "dur": 31, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762341, "dur": 24, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762367, "dur": 33, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762402, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762404, "dur": 30, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762436, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762437, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762460, "dur": 45, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762508, "dur": 19, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762529, "dur": 40, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762571, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762574, "dur": 40, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762615, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762617, "dur": 47, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762666, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762667, "dur": 31, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762700, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762701, "dur": 54, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762756, "dur": 1, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762758, "dur": 30, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762789, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762791, "dur": 25, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762818, "dur": 27, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762849, "dur": 26, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762877, "dur": 40, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762918, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762920, "dur": 44, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762965, "dur": 1, "ph": "X", "name": "ProcessMessages 1181", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509762967, "dur": 40, "ph": "X", "name": "ReadAsync 1181", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763009, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763011, "dur": 57, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763069, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763071, "dur": 28, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763102, "dur": 82, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763186, "dur": 1, "ph": "X", "name": "ProcessMessages 1827", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763188, "dur": 37, "ph": "X", "name": "ReadAsync 1827", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763228, "dur": 29, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763260, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763262, "dur": 48, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763311, "dur": 1, "ph": "X", "name": "ProcessMessages 1347", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763313, "dur": 50, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763365, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763367, "dur": 68, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763437, "dur": 1, "ph": "X", "name": "ProcessMessages 1457", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763447, "dur": 60, "ph": "X", "name": "ReadAsync 1457", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763509, "dur": 39, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763550, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763551, "dur": 35, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763588, "dur": 121, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763712, "dur": 1, "ph": "X", "name": "ProcessMessages 2351", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763714, "dur": 29, "ph": "X", "name": "ReadAsync 2351", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763745, "dur": 37, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763784, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763786, "dur": 39, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763863, "dur": 48, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763913, "dur": 2, "ph": "X", "name": "ProcessMessages 2082", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763915, "dur": 25, "ph": "X", "name": "ReadAsync 2082", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763943, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763945, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763968, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509763995, "dur": 28, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764025, "dur": 50, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764077, "dur": 1, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764079, "dur": 48, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764128, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764130, "dur": 28, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764160, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764228, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764229, "dur": 100, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764331, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764333, "dur": 26, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764361, "dur": 63, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764426, "dur": 68, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764498, "dur": 98, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764597, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764599, "dur": 33, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764633, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764635, "dur": 66, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764704, "dur": 84, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764790, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764792, "dur": 38, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764833, "dur": 66, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764901, "dur": 36, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764940, "dur": 21, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764963, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509764993, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765019, "dur": 87, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765108, "dur": 1, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765110, "dur": 23, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765135, "dur": 30, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765168, "dur": 68, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765238, "dur": 53, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765294, "dur": 21, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765318, "dur": 29, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765349, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765350, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765378, "dur": 31, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765411, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765412, "dur": 31, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765446, "dur": 24, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765473, "dur": 59, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765535, "dur": 36, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765574, "dur": 44, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765620, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765621, "dur": 44, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765666, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765668, "dur": 36, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765705, "dur": 1, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765707, "dur": 51, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765760, "dur": 1, "ph": "X", "name": "ProcessMessages 1211", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765762, "dur": 45, "ph": "X", "name": "ReadAsync 1211", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765810, "dur": 29, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765841, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765861, "dur": 63, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765926, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765928, "dur": 27, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765957, "dur": 24, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509765984, "dur": 54, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766040, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766041, "dur": 86, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766129, "dur": 1, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766132, "dur": 30, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766164, "dur": 48, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766215, "dur": 130, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766347, "dur": 2, "ph": "X", "name": "ProcessMessages 2120", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766350, "dur": 45, "ph": "X", "name": "ReadAsync 2120", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766396, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766398, "dur": 70, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766469, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766471, "dur": 35, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766508, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766509, "dur": 23, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766534, "dur": 38, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766582, "dur": 42, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766626, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766627, "dur": 58, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766687, "dur": 1, "ph": "X", "name": "ProcessMessages 1054", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766689, "dur": 73, "ph": "X", "name": "ReadAsync 1054", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766764, "dur": 1, "ph": "X", "name": "ProcessMessages 1813", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766766, "dur": 64, "ph": "X", "name": "ReadAsync 1813", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766831, "dur": 1, "ph": "X", "name": "ProcessMessages 1841", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766833, "dur": 51, "ph": "X", "name": "ReadAsync 1841", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766886, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766888, "dur": 26, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509766917, "dur": 105, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767024, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767025, "dur": 91, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767117, "dur": 1, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767119, "dur": 73, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767204, "dur": 1, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767205, "dur": 30, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767239, "dur": 91, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767333, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767335, "dur": 64, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767400, "dur": 1, "ph": "X", "name": "ProcessMessages 1441", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767403, "dur": 80, "ph": "X", "name": "ReadAsync 1441", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767485, "dur": 1, "ph": "X", "name": "ProcessMessages 1423", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767488, "dur": 38, "ph": "X", "name": "ReadAsync 1423", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767528, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767530, "dur": 37, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767568, "dur": 169, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767739, "dur": 1, "ph": "X", "name": "ProcessMessages 1320", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767741, "dur": 41, "ph": "X", "name": "ReadAsync 1320", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767783, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767785, "dur": 29, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767817, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767818, "dur": 103, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767924, "dur": 56, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509767982, "dur": 60, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768045, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768048, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768096, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768169, "dur": 324, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768495, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768497, "dur": 100, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768600, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768638, "dur": 125, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768766, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768815, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509768934, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769036, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769132, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769133, "dur": 98, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769234, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769236, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769295, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769330, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769332, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769363, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769466, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769554, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769624, "dur": 112, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769739, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769818, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769820, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509769887, "dur": 132, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770022, "dur": 91, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770127, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770171, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770256, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770337, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770379, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770426, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770477, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770577, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770579, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770636, "dur": 85, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770725, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770859, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770861, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770898, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509770900, "dur": 183, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771086, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771139, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771180, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771222, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771276, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771278, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771314, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771355, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771357, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771459, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771460, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771505, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771506, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771544, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771657, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771692, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771731, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771789, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771826, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771868, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771937, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771939, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509771980, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772016, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772046, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772087, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772124, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772188, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772223, "dur": 52, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772277, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772279, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772318, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772369, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772413, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772415, "dur": 45, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772462, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772498, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772534, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772584, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772621, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772667, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772734, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772735, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772774, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772775, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772815, "dur": 31, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772848, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772904, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772937, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509772962, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773026, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773091, "dur": 36, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773140, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773178, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773249, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773286, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773327, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773329, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773364, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773437, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773445, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773493, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773537, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773572, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773612, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773649, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773704, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773705, "dur": 97, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773805, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773852, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773893, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773932, "dur": 8, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773941, "dur": 37, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509773980, "dur": 381, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509774362, "dur": 81, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509774445, "dur": 85, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509774533, "dur": 698, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509775233, "dur": 16052, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509791291, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509791294, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509791414, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509791417, "dur": 731, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509792153, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509792306, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509792446, "dur": 3023, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509795472, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509795474, "dur": 9786, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509805266, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509805270, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509805331, "dur": 400, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509805734, "dur": 399, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509806138, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509806141, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509806203, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509806372, "dur": 612, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509806986, "dur": 249, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509807244, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509807249, "dur": 349, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509807601, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509807792, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509807907, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509808057, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509808058, "dur": 357, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509808419, "dur": 235, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509808655, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509808799, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509808897, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809081, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809275, "dur": 240, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809518, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809650, "dur": 143, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809796, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809868, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809877, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509809992, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509810242, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509810432, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509810510, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509810654, "dur": 239, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509810897, "dur": 271, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509811171, "dur": 123, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509811298, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509811461, "dur": 131, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509811595, "dur": 374, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509811972, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812027, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812095, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812242, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812305, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812348, "dur": 246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812598, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812751, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509812754, "dur": 654, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509813411, "dur": 176, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509813591, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509813593, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509813665, "dur": 580, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509814248, "dur": 299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509814548, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509814557, "dur": 521, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509815080, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509815084, "dur": 122, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509815209, "dur": 495, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509815707, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509815895, "dur": 270, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816170, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816304, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816465, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816554, "dur": 18, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816575, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816812, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509816963, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817153, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817156, "dur": 181, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817341, "dur": 223, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817568, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817772, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817812, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509817944, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818076, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818118, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818120, "dur": 61, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818183, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818224, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818313, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818365, "dur": 92, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818460, "dur": 108, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818570, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818739, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818828, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509818852, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819086, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819167, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819220, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819353, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819413, "dur": 344, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819810, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819811, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819847, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509819849, "dur": 237, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820089, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820141, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820231, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820478, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820524, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820618, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820941, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509820943, "dur": 118680, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939634, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939638, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939678, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939747, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939750, "dur": 58, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939812, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939838, "dur": 59, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939901, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939949, "dur": 25, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509939976, "dur": 3950, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509943934, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509943938, "dur": 2114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509946056, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509946058, "dur": 962, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509947022, "dur": 956, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509948021, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509948024, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509948095, "dur": 418, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509948516, "dur": 1622, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509950143, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509950145, "dur": 554, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509950703, "dur": 1115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509951821, "dur": 1220, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509953043, "dur": 704, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509953750, "dur": 1078, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509954835, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509954839, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509954966, "dur": 474, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509955444, "dur": 751, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509956199, "dur": 1972, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509958174, "dur": 6205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509964392, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509964400, "dur": 825, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509965230, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509965232, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509965532, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509965535, "dur": 850, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509966388, "dur": 331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509966722, "dur": 283, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509967008, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509967011, "dur": 194, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509967207, "dur": 2334, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509969546, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509969548, "dur": 359, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509969911, "dur": 1088, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971002, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971065, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971123, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971173, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971417, "dur": 159, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971579, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971581, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971653, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971654, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971761, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971953, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509971955, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972030, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972031, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972081, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972290, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972353, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972540, "dur": 155, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972697, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972698, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972751, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972787, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972907, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509972994, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973077, "dur": 111, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973199, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973237, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973281, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973319, "dur": 70, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973392, "dur": 94, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973488, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973491, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973534, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973577, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973645, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973759, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973799, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973905, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973981, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509973982, "dur": 135, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974120, "dur": 122, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974243, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974245, "dur": 71, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974319, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974365, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974490, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974524, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509974632, "dur": 784, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509975418, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509975423, "dur": 6196, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509981625, "dur": 19, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509981654, "dur": 1145, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509982805, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 42949672960, "ts": 1752935509982810, "dur": 2444, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509986782, "dur": 936, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 38654705664, "ts": 1752935509624552, "dur": 63, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 38654705664, "ts": 1752935509624615, "dur": 12497, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 39945, "tid": 38654705664, "ts": 1752935509637114, "dur": 101, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509987720, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 39945, "tid": 1, "ts": 1752935508338469, "dur": 19723, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752935508358204, "dur": 55930, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 39945, "tid": 1, "ts": 1752935508414137, "dur": 72676, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509987733, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 39945, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508338352, "dur": 33154, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508371508, "dur": 524130, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508371644, "dur": 245, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508371935, "dur": 22, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508371958, "dur": 4617, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376585, "dur": 3, "ph": "X", "name": "ProcessMessages 4189", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376590, "dur": 32, "ph": "X", "name": "ReadAsync 4189", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376625, "dur": 82, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376734, "dur": 3, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376750, "dur": 48, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376800, "dur": 2, "ph": "X", "name": "ProcessMessages 2478", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376805, "dur": 92, "ph": "X", "name": "ReadAsync 2478", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376910, "dur": 1, "ph": "X", "name": "ProcessMessages 2579", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376912, "dur": 41, "ph": "X", "name": "ReadAsync 2579", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376958, "dur": 1, "ph": "X", "name": "ProcessMessages 1566", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508376960, "dur": 74, "ph": "X", "name": "ReadAsync 1566", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377036, "dur": 1, "ph": "X", "name": "ProcessMessages 2314", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377038, "dur": 52, "ph": "X", "name": "ReadAsync 2314", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377127, "dur": 14, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377145, "dur": 46, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377211, "dur": 1, "ph": "X", "name": "ProcessMessages 2985", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377214, "dur": 50, "ph": "X", "name": "ReadAsync 2985", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377265, "dur": 1, "ph": "X", "name": "ProcessMessages 2425", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377301, "dur": 98, "ph": "X", "name": "ReadAsync 2425", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377542, "dur": 2, "ph": "X", "name": "ProcessMessages 2179", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377546, "dur": 37, "ph": "X", "name": "ReadAsync 2179", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377585, "dur": 2, "ph": "X", "name": "ProcessMessages 4554", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508377588, "dur": 10057, "ph": "X", "name": "ReadAsync 4554", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508387787, "dur": 7, "ph": "X", "name": "ProcessMessages 8169", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508387795, "dur": 166, "ph": "X", "name": "ReadAsync 8169", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508388077, "dur": 4, "ph": "X", "name": "ProcessMessages 8191", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508388082, "dur": 37, "ph": "X", "name": "ReadAsync 8191", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508388122, "dur": 2040, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508390169, "dur": 33, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508390205, "dur": 493, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508390764, "dur": 1, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508390789, "dur": 363, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391155, "dur": 3, "ph": "X", "name": "ProcessMessages 4124", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391159, "dur": 434, "ph": "X", "name": "ReadAsync 4124", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391604, "dur": 2, "ph": "X", "name": "ProcessMessages 2801", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391607, "dur": 83, "ph": "X", "name": "ReadAsync 2801", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391692, "dur": 1, "ph": "X", "name": "ProcessMessages 1899", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391695, "dur": 58, "ph": "X", "name": "ReadAsync 1899", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391757, "dur": 50, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391809, "dur": 1, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391812, "dur": 68, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391882, "dur": 1, "ph": "X", "name": "ProcessMessages 1244", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391884, "dur": 61, "ph": "X", "name": "ReadAsync 1244", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508391951, "dur": 54, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392012, "dur": 1, "ph": "X", "name": "ProcessMessages 1190", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392015, "dur": 70, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392087, "dur": 1, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392089, "dur": 41, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392161, "dur": 1, "ph": "X", "name": "ProcessMessages 1315", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392163, "dur": 39, "ph": "X", "name": "ReadAsync 1315", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392203, "dur": 1, "ph": "X", "name": "ProcessMessages 1439", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392205, "dur": 39, "ph": "X", "name": "ReadAsync 1439", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392261, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392264, "dur": 42, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392307, "dur": 1, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392330, "dur": 27, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392359, "dur": 2, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392363, "dur": 66, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392439, "dur": 1, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392441, "dur": 26, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392469, "dur": 2, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392473, "dur": 35, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392509, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392511, "dur": 63, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392577, "dur": 22, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392614, "dur": 21, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392642, "dur": 30, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392674, "dur": 2, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392678, "dur": 46, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392726, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508392727, "dur": 417, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393165, "dur": 2, "ph": "X", "name": "ProcessMessages 4108", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393168, "dur": 77, "ph": "X", "name": "ReadAsync 4108", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393248, "dur": 2, "ph": "X", "name": "ProcessMessages 2376", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393254, "dur": 335, "ph": "X", "name": "ReadAsync 2376", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393686, "dur": 3, "ph": "X", "name": "ProcessMessages 4122", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393690, "dur": 80, "ph": "X", "name": "ReadAsync 4122", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393772, "dur": 2, "ph": "X", "name": "ProcessMessages 4192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508393858, "dur": 227, "ph": "X", "name": "ReadAsync 4192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394101, "dur": 2, "ph": "X", "name": "ProcessMessages 3237", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394105, "dur": 269, "ph": "X", "name": "ReadAsync 3237", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394376, "dur": 1, "ph": "X", "name": "ProcessMessages 1883", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394379, "dur": 41, "ph": "X", "name": "ReadAsync 1883", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394422, "dur": 2, "ph": "X", "name": "ProcessMessages 4816", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394425, "dur": 36, "ph": "X", "name": "ReadAsync 4816", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394463, "dur": 49, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394605, "dur": 2, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394608, "dur": 68, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394680, "dur": 2, "ph": "X", "name": "ProcessMessages 2786", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394684, "dur": 106, "ph": "X", "name": "ReadAsync 2786", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394792, "dur": 2, "ph": "X", "name": "ProcessMessages 1555", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508394795, "dur": 653, "ph": "X", "name": "ReadAsync 1555", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508395449, "dur": 2, "ph": "X", "name": "ProcessMessages 2761", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508395452, "dur": 65, "ph": "X", "name": "ReadAsync 2761", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508395519, "dur": 1, "ph": "X", "name": "ProcessMessages 1598", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508395520, "dur": 398, "ph": "X", "name": "ReadAsync 1598", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508395922, "dur": 3, "ph": "X", "name": "ProcessMessages 4863", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396005, "dur": 45, "ph": "X", "name": "ReadAsync 4863", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396067, "dur": 2, "ph": "X", "name": "ProcessMessages 2012", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396072, "dur": 85, "ph": "X", "name": "ReadAsync 2012", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396160, "dur": 2, "ph": "X", "name": "ProcessMessages 2057", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396164, "dur": 72, "ph": "X", "name": "ReadAsync 2057", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396238, "dur": 1, "ph": "X", "name": "ProcessMessages 1936", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396240, "dur": 94, "ph": "X", "name": "ReadAsync 1936", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396336, "dur": 1, "ph": "X", "name": "ProcessMessages 1853", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396393, "dur": 41, "ph": "X", "name": "ReadAsync 1853", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396445, "dur": 2, "ph": "X", "name": "ProcessMessages 2362", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396448, "dur": 46, "ph": "X", "name": "ReadAsync 2362", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396531, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396534, "dur": 143, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396678, "dur": 2, "ph": "X", "name": "ProcessMessages 2656", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396681, "dur": 74, "ph": "X", "name": "ReadAsync 2656", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396757, "dur": 12, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396771, "dur": 158, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396930, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508396933, "dur": 77, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397011, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397013, "dur": 77, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397152, "dur": 29, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397185, "dur": 165, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397351, "dur": 1, "ph": "X", "name": "ProcessMessages 1557", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397353, "dur": 69, "ph": "X", "name": "ReadAsync 1557", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397423, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397431, "dur": 63, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397497, "dur": 96, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397595, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397596, "dur": 90, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397689, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397692, "dur": 48, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397742, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397743, "dur": 58, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397804, "dur": 46, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397853, "dur": 48, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397904, "dur": 84, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508397991, "dur": 124, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398116, "dur": 1, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398118, "dur": 47, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398168, "dur": 92, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398275, "dur": 52, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398330, "dur": 44, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398379, "dur": 58, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398440, "dur": 185, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398702, "dur": 3, "ph": "X", "name": "ProcessMessages 2289", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398715, "dur": 42, "ph": "X", "name": "ReadAsync 2289", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398788, "dur": 3, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508398792, "dur": 469, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508399319, "dur": 3, "ph": "X", "name": "ProcessMessages 2480", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508399324, "dur": 1397, "ph": "X", "name": "ReadAsync 2480", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508400762, "dur": 655, "ph": "X", "name": "ProcessMessages 5607", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508401420, "dur": 164, "ph": "X", "name": "ReadAsync 5607", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508401586, "dur": 11, "ph": "X", "name": "ProcessMessages 4445", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508401603, "dur": 527, "ph": "X", "name": "ReadAsync 4445", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402131, "dur": 2, "ph": "X", "name": "ProcessMessages 3677", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402134, "dur": 201, "ph": "X", "name": "ReadAsync 3677", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402337, "dur": 1, "ph": "X", "name": "ProcessMessages 1914", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402339, "dur": 47, "ph": "X", "name": "ReadAsync 1914", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402389, "dur": 175, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402569, "dur": 156, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402757, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402758, "dur": 124, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402884, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508402886, "dur": 1419, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404307, "dur": 4, "ph": "X", "name": "ProcessMessages 8181", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404324, "dur": 66, "ph": "X", "name": "ReadAsync 8181", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404397, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404400, "dur": 38, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404441, "dur": 1, "ph": "X", "name": "ProcessMessages 1343", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404444, "dur": 50, "ph": "X", "name": "ReadAsync 1343", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404511, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404512, "dur": 142, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404656, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404659, "dur": 253, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404914, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508404931, "dur": 2077, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407010, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407011, "dur": 361, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407375, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407377, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407413, "dur": 312, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407727, "dur": 255, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508407985, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508408029, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508408149, "dur": 192, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508408343, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508408500, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508410476, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508410529, "dur": 4, "ph": "X", "name": "ProcessMessages 1392", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508410534, "dur": 293, "ph": "X", "name": "ReadAsync 1392", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508410830, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508410917, "dur": 201, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508411120, "dur": 229, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508411352, "dur": 105, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508411459, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508411460, "dur": 229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508412648, "dur": 89, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508412742, "dur": 116, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508412860, "dur": 2, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508412864, "dur": 215, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413083, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413138, "dur": 170, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413311, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413313, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413379, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413381, "dur": 121, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413505, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413574, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413635, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413716, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413763, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413811, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413812, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508413966, "dur": 82, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414050, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414051, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414119, "dur": 134, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414254, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414256, "dur": 78, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414336, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414338, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414376, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414412, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414455, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414528, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414605, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414631, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414726, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414767, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414814, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414860, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414896, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508414940, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415012, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415048, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415050, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415104, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415105, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415140, "dur": 9, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415150, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415224, "dur": 107, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415333, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415373, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508415476, "dur": 20790, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508436271, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508436273, "dur": 1803, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508438080, "dur": 3607, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508441698, "dur": 19922, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508471927, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508471937, "dur": 183, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472122, "dur": 17, "ph": "X", "name": "ProcessMessages 1472", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472140, "dur": 76, "ph": "X", "name": "ReadAsync 1472", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472219, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472361, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472366, "dur": 530, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472900, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508472942, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508473089, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508473092, "dur": 283, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508473377, "dur": 489, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508473869, "dur": 460, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508474332, "dur": 474, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508474815, "dur": 675, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508475522, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508475524, "dur": 480, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508476006, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508476078, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508476080, "dur": 784, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508476869, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508476975, "dur": 747, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508477726, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508477735, "dur": 2205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508479944, "dur": 28, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508479973, "dur": 1099, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508481076, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508481078, "dur": 317, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508481398, "dur": 274, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508481676, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508481906, "dur": 609, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508482519, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508482658, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508482705, "dur": 558, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508483267, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508483270, "dur": 1012, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508484288, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508484290, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508484437, "dur": 683, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508485140, "dur": 328, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508485471, "dur": 295, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508485771, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508485774, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508485820, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508485822, "dur": 384, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508486208, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508486212, "dur": 255688, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508741910, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508741914, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508741960, "dur": 237, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508742200, "dur": 164, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508742367, "dur": 3855, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508746228, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508746231, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508746269, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508746290, "dur": 1241, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508747533, "dur": 143431, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508890974, "dur": 35, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508891010, "dur": 1113, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508892129, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 39945, "tid": 34359738368, "ts": 1752935508892132, "dur": 3495, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509987746, "dur": 450, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 39945, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 39945, "tid": 30064771072, "ts": 1752935508338099, "dur": 148721, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 39945, "tid": 30064771072, "ts": 1752935508486822, "dur": 62, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509988204, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 39945, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935508300662, "dur": 595462, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935508302885, "dur": 34386, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935508896146, "dur": 716447, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935509612793, "dur": 372753, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935509613592, "dur": 10659, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935509985554, "dur": 117, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 39945, "tid": 25769803776, "ts": 1752935509985572, "dur": 50, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509988215, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1752935508986737, "dur": 612463, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752935508987508, "dur": 193379, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752935509536471, "dur": 3333, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752935509539806, "dur": 59385, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752935509540694, "dur": 45025, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752935509604202, "dur": 888, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1752935509603939, "dur": 1300, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752935508371054, "dur": 124, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508371246, "dur": 2654, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508373909, "dur": 2372, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508376359, "dur": 124, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508378153, "dur": 9280, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508387478, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508388166, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752935508388254, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508388537, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508388719, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508388840, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508389108, "dur": 142, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508389749, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_9DFB00DF8CA609B9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508389935, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752935508390192, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508390325, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508390563, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752935508390621, "dur": 148, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752935508390822, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508390894, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508391198, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752935508391280, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752935508391477, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508391566, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508393540, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_98EA1327509A07AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508395286, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_C1F9C6FB58945FCF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752935508395950, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508399721, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752935508402376, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752935508404282, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752935508376491, "dur": 28158, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508404656, "dur": 82539, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508487282, "dur": 404520, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508891803, "dur": 50, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508891987, "dur": 105, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935508892113, "dur": 908, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752935508376404, "dur": 28262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508404710, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_10BEE7D50EF5D62A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508406955, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508407071, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508407411, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508407663, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508408164, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508408341, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508408666, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508408785, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508408890, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508409021, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508409118, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508409266, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508409363, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508409489, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508409596, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508409733, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508409836, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508409933, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508410006, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508410124, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508410216, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508410338, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508410459, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752935508411025, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508411284, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508411556, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508411723, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508411830, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508411906, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752935508412145, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508412237, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752935508412382, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508412492, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508412552, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752935508412620, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508412959, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508413154, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508413405, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508413618, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752935508413684, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508413838, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414030, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414145, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414271, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414401, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414514, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414665, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414783, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414889, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508414984, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508415078, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508415169, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508415282, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508415377, "dur": 1981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508417358, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508418871, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508420639, "dur": 2557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508423196, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508424830, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508426370, "dur": 2019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508428390, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508430042, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508431952, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508434618, "dur": 1875, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Connections/IUnitConnectionWidget.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752935508433717, "dur": 3212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508436930, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508438887, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508440596, "dur": 1894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508442491, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508444151, "dur": 2095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508446246, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508447458, "dur": 1954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508449412, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508451286, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508452221, "dur": 2089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508454311, "dur": 2259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508456571, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508458227, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508459085, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508460123, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508460239, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508460336, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508460543, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508461091, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508461452, "dur": 2197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935508463649, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508463827, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_9DFB00DF8CA609B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508463891, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508464006, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508464882, "dur": 2129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935508467012, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508467292, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508467438, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935508468817, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508469015, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508469150, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508469209, "dur": 1945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935508471155, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508471343, "dur": 1797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508473168, "dur": 2170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935508475339, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508475855, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508476063, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508476165, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752935508476520, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508477226, "dur": 8554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935508485780, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935508485872, "dur": 1311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508376412, "dur": 28260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508404756, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_ACAB605E29B4D244.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508406954, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508407114, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508407379, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508407743, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508407926, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508408237, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508408465, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508408967, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508409028, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508409138, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508409219, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508409347, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508409441, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508409575, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508409675, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508409824, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508409908, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508410001, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508410131, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508410234, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508410337, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508410453, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508410515, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752935508410613, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508410745, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752935508410805, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508410986, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508411235, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508411577, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508411638, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508411767, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508411888, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508411965, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752935508412156, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508412362, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508412458, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508412516, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752935508412609, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508412966, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508413191, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508413416, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508413645, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508413809, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414006, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414153, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414279, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414407, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414530, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414680, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414788, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414898, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508414992, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508415101, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508415185, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508415268, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508415374, "dur": 1995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508417370, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508418887, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508420680, "dur": 2559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508423239, "dur": 1640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508424879, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508426446, "dur": 1978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508428424, "dur": 1696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508430120, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508432034, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508434657, "dur": 2001, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.common@8.0.4/Path/Editor/IMGUI/GUIFramework/LayoutData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752935508433759, "dur": 3342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508437102, "dur": 1900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508439002, "dur": 1806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508440809, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Utilities/VSUsageUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752935508440809, "dur": 2411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508443220, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508445176, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508446900, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508448323, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508450293, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508451762, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508452958, "dur": 2741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508455700, "dur": 1686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508457386, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508458605, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508460237, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508460333, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508460583, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508461118, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508461411, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508461513, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508462390, "dur": 825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935508463216, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508463423, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935508463547, "dur": 7781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935508471328, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508471542, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935508474349, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508474855, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508474926, "dur": 1466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935508476393, "dur": 1285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508477681, "dur": 9290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935508486971, "dur": 144, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508376419, "dur": 28258, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508404679, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_1CB1CE8F85C1B187.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508406969, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508407107, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508407394, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508407550, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508407913, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508408063, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508408353, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508408636, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508408803, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508408879, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508409010, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508409067, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508409199, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508409285, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508409422, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508409511, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508409684, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508409764, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508409887, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508409991, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508410123, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508410208, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508410337, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508410395, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752935508410457, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508410654, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752935508410761, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508410833, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752935508410986, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508411245, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508411555, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508411679, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508411804, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752935508412603, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508412933, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508413181, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508413410, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508413589, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508413777, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508413977, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414122, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414245, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414396, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414519, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414668, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414798, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508414905, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508415006, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508415093, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508415199, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508415298, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508415412, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508417381, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508418905, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508420699, "dur": 2518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508423217, "dur": 1628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508424845, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508426352, "dur": 2016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508428369, "dur": 1684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508430053, "dur": 1931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508431985, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508434624, "dur": 1905, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.spriteshape@9.0.5/Runtime/External/LibTessDotNet/Mesh.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752935508433726, "dur": 3309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508437035, "dur": 1910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508438946, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508440672, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508442590, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508444259, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508446347, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508447563, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508449530, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508451377, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508452365, "dur": 2165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508454531, "dur": 2265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508456796, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508458457, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508460014, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508460355, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508460561, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508461093, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508461680, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508461772, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935508464314, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508464543, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Sprite.Editor.ref.dll_4D635C4BE46FE4CD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508464642, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508464793, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508465736, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508465849, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935508466836, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508467097, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508467162, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508468017, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935508469432, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508469711, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508469829, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508469900, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935508471602, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508471783, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508472103, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508472299, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935508472511, "dur": 9350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935508481861, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935508481979, "dur": 5175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508376429, "dur": 28264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508404695, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_AC0030664268B841.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508406938, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508407063, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508407389, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508407660, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508407883, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508408032, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508408311, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508408547, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508409044, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508409170, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508409265, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508409402, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508409477, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508409659, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508409750, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508409867, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508409972, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508410089, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508410221, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508410343, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508410426, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508410499, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508410621, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508410760, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508410834, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508410981, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508411218, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508411524, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508411620, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508411757, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508411836, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508411937, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752935508412540, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508412789, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508413117, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508413307, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508413497, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508413756, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508413944, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414093, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414223, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414358, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414467, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414620, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414744, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414844, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508414943, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508415062, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508415163, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508415260, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508415352, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508417130, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508418851, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508420590, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508423169, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508424823, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508426320, "dur": 2038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508428359, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508430033, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508431964, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508434628, "dur": 1925, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Events/TriggerCustomEventDescriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752935508433678, "dur": 3141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508436819, "dur": 2006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508438825, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508440565, "dur": 1979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508442545, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508444220, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508446320, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508447534, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508449504, "dur": 1862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508451366, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508452291, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508454457, "dur": 2301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508456761, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508458434, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508459464, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508460136, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508460239, "dur": 54, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508460338, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508460524, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508461341, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508461572, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508461641, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935508463507, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508463852, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_1A4EF4AE609B2576.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508463970, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508464084, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508464310, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508465019, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508465075, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935508466629, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508466769, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508467350, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508467445, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508467501, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935508468277, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508468502, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935508470009, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508470199, "dur": 4900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935508475100, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508475364, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508475738, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508475806, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752935508475896, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508476413, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508476989, "dur": 7191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508484184, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935508484459, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935508484824, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935508485080, "dur": 2094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508376439, "dur": 28260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508404701, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_590AF6BBC64FB042.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508406967, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508407079, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508407385, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508407539, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508407970, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508408174, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508408589, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508408751, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508408881, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508409008, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508409060, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508409189, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508409284, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508409409, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508409497, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508409694, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508409815, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508409940, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508410067, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508410149, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508410288, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508410386, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752935508410454, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508410558, "dur": 1640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508412211, "dur": 21369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935508433581, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508433827, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508433943, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508434610, "dur": 1818, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.common@8.0.4/Editor/TextureGenerator/TextureGeneratorHelper.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752935508434013, "dur": 3346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508437366, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508439214, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508440947, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508442794, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508444586, "dur": 1965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508446551, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508447777, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508449741, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508451480, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508452512, "dur": 2205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508454717, "dur": 2232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508456949, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508458550, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508460087, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508460356, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508460591, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508461337, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508462535, "dur": 1564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935508464099, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508464285, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_BB27E5BA9B36011C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508464419, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508464837, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508464915, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935508465326, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935508467998, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508468470, "dur": 3507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935508471977, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508472198, "dur": 9570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935508481768, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935508481834, "dur": 5353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508376448, "dur": 28256, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508404708, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508406994, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508407362, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508407519, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508407818, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508407999, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508408288, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508408492, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508409039, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508409170, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508409250, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508409388, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508409460, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508409633, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508409731, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508409864, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508409952, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508410077, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508410193, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508410313, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508410428, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508410577, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752935508410793, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508410946, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508411134, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508411407, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752935508412717, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508413014, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508413276, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508413434, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508413689, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508413848, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414059, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414168, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414307, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414422, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414546, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414697, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414818, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508414926, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508415039, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508415139, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508415242, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508415344, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508415438, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508417427, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508418953, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508420822, "dur": 2479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508423301, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508424890, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508426451, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508428532, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508430220, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508432098, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508434643, "dur": 1955, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.common@8.0.4/Path/Editor/EditorTool/PathComponentEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752935508433801, "dur": 3372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508437173, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508439043, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508440823, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508442753, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508444505, "dur": 1977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508446483, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508447624, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508449596, "dur": 1849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508451445, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508452471, "dur": 2235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508454707, "dur": 2286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508456993, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508458658, "dur": 1634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508460335, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508460555, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508461101, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508461495, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508461558, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935508462175, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508462418, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508462472, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508462652, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935508463154, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508463386, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_AEDA0D01FA0B3E02.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508463508, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508463714, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935508465179, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508465238, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935508465862, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508465980, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935508468265, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508468476, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508468535, "dur": 5069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935508473605, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508474037, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508474188, "dur": 3549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935508477737, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935508477893, "dur": 9317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508376457, "dur": 28255, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508404716, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508406907, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508407058, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508407386, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508407654, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508407970, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508408181, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508408698, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508408790, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508408846, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508408919, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508409028, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508409110, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508409230, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508409324, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508409448, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508409578, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508409727, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508409803, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508409938, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508410031, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508410140, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508410249, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508410367, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508410508, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752935508410963, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508411119, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508411456, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508411588, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752935508413051, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508413270, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508413472, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508413707, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508413871, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414075, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414182, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414327, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414433, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414562, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414702, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414813, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508414908, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508415015, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508415117, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508415204, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508415297, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508415385, "dur": 2621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508418006, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508419385, "dur": 2141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508421527, "dur": 2356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508423884, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508425314, "dur": 1853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508427167, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508429024, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508430752, "dur": 1981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508432734, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508434640, "dur": 1972, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.2d.animation@9.1.3/Runtime/SpriteLib/SpriteLibraryAsset.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752935508434177, "dur": 3529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508437706, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508439509, "dur": 1808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508441317, "dur": 1823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508443140, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508445039, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508446815, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508448171, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508450194, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508451708, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508452910, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508455648, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508457389, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508458145, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508458765, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508460253, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508460341, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508460549, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508461087, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508461398, "dur": 8652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935508470050, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508470537, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508472066, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508472320, "dur": 7972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935508480293, "dur": 921, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508481260, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935508481343, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508481527, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935508482511, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935508482746, "dur": 4419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508376466, "dur": 28301, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508404770, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_029C9F9B746E930E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508406888, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508407046, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508407385, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508407652, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508407857, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508408092, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508408413, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508408610, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508408853, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508408997, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508409050, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508409170, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508409247, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508409386, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508409463, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508409632, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508409715, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508409841, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508409922, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508410026, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508410131, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508410243, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508410367, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508410504, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752935508410729, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508410898, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508411080, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752935508411186, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508411429, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752935508411493, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752935508412627, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508412994, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508413164, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508413375, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508413659, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508413799, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508413999, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414130, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414255, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414389, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414495, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414665, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414773, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414877, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508414973, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508415071, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508415166, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508415275, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508415364, "dur": 1752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508417116, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508418823, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508420552, "dur": 2494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508423047, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508424778, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508426274, "dur": 2024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508428299, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508429979, "dur": 1884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508431863, "dur": 1735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508434652, "dur": 1974, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/Codebase/LiteralWidget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752935508433599, "dur": 3163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508436811, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508436994, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508438881, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508440661, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508442561, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508444242, "dur": 2073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508446315, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508447538, "dur": 1950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508449489, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508451361, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508452312, "dur": 2150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508454463, "dur": 2221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508456685, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508458346, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508459137, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508459719, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508460345, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508460529, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508461153, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508462072, "dur": 2458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935508464530, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508464682, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508464789, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508465063, "dur": 3512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935508468575, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508468847, "dur": 2044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935508470892, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508471146, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508471430, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508471838, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508471991, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_C1F9C6FB58945FCF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508472129, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508472526, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508472666, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508472774, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508473121, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508473409, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508473508, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508473965, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935508474679, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508474797, "dur": 4921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935508479718, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935508480143, "dur": 6988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508376476, "dur": 28251, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508404733, "dur": 2164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WeixinMiniGameModule.dll_8D721F1FAAA26EB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508406897, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508407041, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508407425, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508407763, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508407965, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508408465, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508408679, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508409038, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508409152, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508409232, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508409363, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508409451, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508409610, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508409709, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508409842, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508409931, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508410015, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508410133, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508410229, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508410337, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508410548, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508410647, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508410789, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752935508410844, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508411072, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508411378, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508411605, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508411731, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508411844, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508411982, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508412064, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752935508412532, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752935508412599, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752935508412778, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508413054, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508413285, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508413451, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752935508413502, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508413753, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508413977, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414103, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414228, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414368, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414478, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414633, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414755, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414860, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508414961, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508415065, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508415161, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508415254, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508415354, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508415472, "dur": 1969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508417441, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508418922, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508420751, "dur": 2149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508422900, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508424739, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508426183, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508428206, "dur": 1686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508429892, "dur": 1927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508431819, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508434615, "dur": 1821, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Flow/Framework/InputSystemWidget.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752935508433560, "dur": 3083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508436651, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752935508436709, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508436989, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508438957, "dur": 1791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508440748, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508442689, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508444432, "dur": 2029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508446462, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508447629, "dur": 1988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508449618, "dur": 1815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508451433, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508452442, "dur": 2203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508454646, "dur": 2265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508456911, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508458529, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508460116, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508460358, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508460587, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508461279, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508461836, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508462676, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508462772, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508463627, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508463905, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508464054, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508465115, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508466276, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508466449, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508466568, "dur": 3809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508470377, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508470583, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508470683, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508470742, "dur": 11859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508482630, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508482854, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508484089, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508484167, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508484278, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508485391, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508485470, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508485773, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935508485855, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935508486174, "dur": 61, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935508486253, "dur": 404600, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935508376487, "dur": 28258, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508404749, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508406938, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508407296, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508407446, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508407998, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508408185, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508408585, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508408774, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508408898, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508409025, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508409096, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508409219, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508409300, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508409429, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508409541, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508409699, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508409788, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508409936, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508410042, "dur": 1930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508411994, "dur": 22476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935508434470, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508434665, "dur": 1974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508436639, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508436773, "dur": 4723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508441496, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508441634, "dur": 18821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935508460522, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508460609, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935508461088, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508461355, "dur": 4108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935508465464, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508465601, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508465668, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935508466432, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935508467608, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508467775, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1752935508469282, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508469342, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508734913, "dur": 11423, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935508470077, "dur": 277169, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 0, "ts": 1752935508894282, "dur": 500, "ph": "X", "name": "ProfilerWriteOutput"}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752935509636869, "dur": 119935, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935509756811, "dur": 137, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935509757006, "dur": 106, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935509757118, "dur": 10722, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935509767848, "dur": 214612, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935509982713, "dur": 575, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752935509757046, "dur": 10808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509767856, "dur": 2491, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509770347, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WeixinMiniGame.Extensions.dll_1D1BF8A95F58BF65.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509770528, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509770657, "dur": 3744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509774402, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509774470, "dur": 17446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509791917, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509792097, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_5505C94DC5916443.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509792191, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509792263, "dur": 3092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509795398, "dur": 9726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509805233, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509805300, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509805622, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509805932, "dur": 1773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509807705, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509807927, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752935509808714, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509810205, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509810389, "dur": 3377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752935509813843, "dur": 985, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509939450, "dur": 444, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509814834, "dur": 125081, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752935509941170, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509943660, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509943900, "dur": 4096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509947997, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509948097, "dur": 4202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509952300, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509952395, "dur": 6925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509959320, "dur": 5065, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509964400, "dur": 5441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509969842, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752935509969969, "dur": 5352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752935509975387, "dur": 7054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509757049, "dur": 10812, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509768004, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FBCCBA9AC9EE6927.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509768150, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509768251, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_08243EED80CC95EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509768435, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_6A8CB7B82A3E90DE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509768609, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509768723, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D5EAD268E0EC5239.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509768915, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509769063, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_271592D3FFE898D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509769209, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509769291, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_87F49AF8674209D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509769468, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509769572, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_032E8EDC34C89430.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509769700, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509769796, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2B334F8835AF6424.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509769969, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509770109, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_78B0E271327A0593.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509770320, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509770404, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509770505, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509770616, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509770774, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509770861, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_68744BCC50269B8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509770986, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771123, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771233, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771341, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771437, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_075CE2BECC3D8030.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509771576, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771679, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771790, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509771909, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772005, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772144, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772234, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772301, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772433, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772539, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772639, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772730, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772833, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509772925, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773031, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773115, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773220, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773295, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773372, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773473, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773551, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773664, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773767, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509773888, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509775155, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509776338, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509777640, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509778856, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509779836, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509780915, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509781971, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509783388, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509784591, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509785808, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509786977, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509788498, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509789722, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509791046, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509792427, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509793660, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509794761, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509795938, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509797265, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509798576, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509800083, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509801185, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509802259, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509802843, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509804116, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509804800, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509804975, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509805219, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509805751, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509806250, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509806307, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509809412, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509809608, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509809798, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509810437, "dur": 6834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509817272, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509817510, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_43307AC03DF2DC88.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509817592, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509817812, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752935509818296, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509818419, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509819283, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509819362, "dur": 121855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509941225, "dur": 4797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509946022, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509946108, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509948483, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509948606, "dur": 4000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509952606, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509952696, "dur": 5400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/WxEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509958096, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509958433, "dur": 8417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509966851, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509967019, "dur": 6943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752935509973962, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509974196, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752935509974255, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752935509974437, "dur": 8035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509757058, "dur": 10817, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509768019, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509768113, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_18EBAF39B978915D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509768264, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509768368, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_FFDF51565BF6A8CB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509768545, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509768636, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8683BA4093CA54B5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509768815, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509768952, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_6ECB19202D6ACCD5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509769115, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509769184, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_107F49989C0D2F71.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509769313, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509769408, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FB2ED4B44220D690.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509769583, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509769682, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11CA34CDBE0AFA42.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509769838, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509769984, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_639192E50A1EBAFF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509770191, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509770321, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_4268874FD77EBE43.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509770456, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509770578, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.FontABTool.dll_A8D6756CD557BC67.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509770731, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509770843, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_F8748C83AC7516C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509770977, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771067, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771204, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771316, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771430, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F00A6A4F4D887EAD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509771576, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771683, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771781, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509771915, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772011, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772149, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772237, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772322, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772459, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772554, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772650, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772741, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772871, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509772968, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773067, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773163, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773254, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773337, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773426, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773520, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773622, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773730, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773832, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509773922, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509775166, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509776350, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509777642, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509778846, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509779828, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509780919, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509781986, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509783401, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509784600, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509785803, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509786958, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509788486, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509789715, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509791023, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509792360, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509793638, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509794733, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509795911, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509797228, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509798540, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509800050, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509801163, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509802234, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509802802, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509804061, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509804773, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509804952, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509805204, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509805634, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509806215, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509806324, "dur": 1710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509808034, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509808322, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509808436, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509808903, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509811327, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509811498, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509811554, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509812154, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509812509, "dur": 1994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509814503, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509814657, "dur": 1671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509816328, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509816485, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509817876, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509818009, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Editor.ref.dll_4EE54C667A43BDA0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752935509818110, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509819286, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509819379, "dur": 121830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509941211, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509943661, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509943874, "dur": 3956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509947830, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509947945, "dur": 5682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509953628, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509953762, "dur": 12805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509966569, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509966723, "dur": 6404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752935509973127, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509973437, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509973546, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752935509973613, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509973752, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752935509973803, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509973938, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509974026, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752935509974084, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509974160, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1752935509974211, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509974379, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752935509974594, "dur": 7860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509757065, "dur": 10831, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509767969, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_E245B2D927F719A3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509768052, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5D857657B7203958.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509768208, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509768297, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_6E1CABD82B26B38A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509768413, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509768524, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E8D7D6B69E9EC8C4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509768676, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509768785, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2C0AF280917D0DFE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509768981, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509769098, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_833103AEB6460CFB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509769245, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509769336, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_B550DB0C00FE63B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509769500, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509769613, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_27C2732208C76231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509769749, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509769857, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InfinityModule.dll_EF6DA8CDDA47BBC2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509770017, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509770163, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A6C1161EE7A28E83.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509770325, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509770411, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509770519, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509770662, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509770780, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextureManagerModule.dll_8EFDA1792482222C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509770944, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771028, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771179, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771287, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771393, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771487, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771600, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771745, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771858, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509771965, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772092, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772200, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772290, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772398, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772522, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772605, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772705, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772794, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509772901, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773000, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773100, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773206, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773301, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773393, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773467, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773544, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773655, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773759, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773847, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509773930, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509775192, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509776378, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509777678, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509778897, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509779870, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509780956, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509782051, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509783437, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509784660, "dur": 1184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509785845, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509787002, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509788524, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509789739, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509791093, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509792437, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509793684, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509794777, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509795965, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509797246, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509798564, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509800063, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509801173, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509802255, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509802800, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509804077, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509804542, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509804946, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509805199, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509805764, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509806218, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509806290, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509807794, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509808016, "dur": 1741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509809757, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509809928, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509809987, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509810528, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509810815, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509811191, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509812042, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509812103, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509812561, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509812696, "dur": 6257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509818953, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752935509819118, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509819252, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509819730, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509819794, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509820109, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509820166, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509820393, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752935509820771, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752935509820481, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752935509820894, "dur": 160652, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509757074, "dur": 10853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509768000, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF06859476283FA1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509768116, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509768233, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C03C087BF148DCCC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509768359, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509768477, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_C646ABD51827012F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509768640, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509768770, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_654313562FE3E238.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509768921, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509769058, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1E713E19D59EAA56.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509769213, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509769308, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_2E7101732861F60C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509769462, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509769576, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5145FF9C838A1121.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509769721, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509769806, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_93C49F35C550D630.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509769993, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509770089, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_92C3B2D91548A5E2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509770304, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509770392, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509770511, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509770614, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509770774, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509770884, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_F3E3BB6C3BDB23C5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509770987, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771125, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771253, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771395, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771491, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771601, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771704, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771817, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509771922, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772032, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772164, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772258, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772362, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772507, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772604, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772714, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772816, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509772917, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509773040, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509773131, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509773233, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509773311, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509773394, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509773501, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509774145, "dur": 169, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752935509774314, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509775582, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509776744, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509778043, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509779151, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509780174, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509781206, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509782422, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509783666, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509784942, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509786082, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509787302, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509788868, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509790106, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509791499, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509792708, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509793968, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509795148, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509796279, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509797605, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509798956, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509800394, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509801435, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509802573, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509803859, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509804833, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509804971, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509805195, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509805626, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509805891, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509805953, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509807486, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509807697, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509808524, "dur": 1675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509810200, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509810366, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_EF3566CB9F379C74.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509810490, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509810601, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509810950, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509811036, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509813336, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509813591, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509813661, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509814020, "dur": 1819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509815840, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509816026, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509816254, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509816313, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752935509817106, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509817185, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509818998, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509819164, "dur": 124514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509943683, "dur": 4097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509947782, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509947967, "dur": 3710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509951677, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509951777, "dur": 4373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509956151, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509956228, "dur": 10713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509966941, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509967079, "dur": 5819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752935509972899, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509973056, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752935509973109, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509973234, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752935509973298, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509973381, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752935509973434, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509973560, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509973679, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752935509973742, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509973903, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509974030, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509974205, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752935509974259, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509974408, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752935509975402, "dur": 7072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509757083, "dur": 10871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509767988, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FF9F4AC49A694A13.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509768063, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509768167, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0006BE0615971DBB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509768306, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509768395, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E09FDFD0E104C53C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509768502, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509768617, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_3E9B83CF04469B97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509768813, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509768929, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8DFF39933165F0EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509769112, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509769173, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_25BD72EE1C73FE39.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509769317, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509769449, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_16B2DEC63A871351.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509769605, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509769676, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_713E0AE68C490F8B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509769839, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509769980, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1F4454B1C957A6DD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509770201, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509770316, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_A8B1067660384A1D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509770476, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509770590, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509770731, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509770833, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_54ADCE96D0094B97.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509771027, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771135, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771242, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771348, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771463, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_E610D5AE7C1AE650.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509771630, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771701, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771834, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509771943, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772058, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772174, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772272, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772379, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772483, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772577, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772666, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772745, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772855, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509772948, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773051, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773148, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773278, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773359, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773451, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773553, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773672, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773777, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509773861, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509775147, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509776294, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509777596, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509778796, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509779776, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509780849, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509781905, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509783333, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509784536, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509785761, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509786914, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509788434, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509789687, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509790969, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509792332, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509793620, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509794728, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509795902, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509797216, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509798523, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509799989, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509801131, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509802198, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509803407, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509804764, "dur": 134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509804959, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509805217, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509805662, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509806375, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509807767, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509807995, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509808668, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509811196, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509811431, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509812304, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509813330, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509813540, "dur": 3381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509816921, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509817320, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.Editor.ref.dll_C1F9C6FB58945FCF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509817436, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509817606, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509817774, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752935509818289, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509819269, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509819336, "dur": 121846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509941195, "dur": 4725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509945920, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509946020, "dur": 6916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509952937, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509953063, "dur": 13192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509966256, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509966421, "dur": 4462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752935509970884, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509970998, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509971058, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509971116, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509971351, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509971598, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509971703, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509972262, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509972402, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752935509972458, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509972660, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752935509972721, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509972867, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973020, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973099, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752935509973149, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973269, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752935509973319, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973519, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973676, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973826, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509973958, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509974094, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509974236, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509974307, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752935509974380, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752935509974472, "dur": 7961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509757090, "dur": 10923, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509768014, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DC674D4816AA7383.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509768198, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509768288, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F54F49BC03770ADB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509768422, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509768549, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_640C3BB82361B58C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509768672, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509768748, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_EC6594AB74462260.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509768905, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509769051, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CE164B77641A9E54.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509769213, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509769310, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4D9F37645136F40C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509769494, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509769593, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_AD53B0866CB02980.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509769723, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509769823, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_D487AE49ABCEAA80.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509770023, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509770179, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OpenHarmonyJSModule.dll_58E9D0FC83734EDF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509770339, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509770451, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509770571, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_80F2FE2EFD1109AE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509770732, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509770850, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_054C0D360A245797.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509771010, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509771143, "dur": 3981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509775177, "dur": 15725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509790902, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509791170, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_EAE6938FA8533185.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509791249, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509791352, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509792626, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509793816, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509795036, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509796130, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509797455, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509798791, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509800273, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509801340, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509802448, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509803241, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509804573, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509804944, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509805195, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509805631, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509806191, "dur": 5637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509811829, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509812031, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509812662, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509815619, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509815803, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752935509816110, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509816181, "dur": 2511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509818692, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509818769, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509818831, "dur": 122339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509941174, "dur": 2487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509943661, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509943792, "dur": 6819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509950612, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509950738, "dur": 3978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509954716, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509954855, "dur": 12297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509967152, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509967250, "dur": 5006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752935509972257, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509972433, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509972561, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752935509972622, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509972742, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509972811, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973026, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973134, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509973198, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973441, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973559, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509973609, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973734, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973828, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.BurstCompatibilityGen.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509973879, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509973959, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509974017, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509974150, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509974204, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509974323, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752935509974397, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752935509974493, "dur": 7947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509757099, "dur": 10863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509767972, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_43EE1E04AD72778D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509768041, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509768141, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A1606A1CEFF3BFA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509768307, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509768407, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_BD4AF41A2C57C34A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509768486, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509768607, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_32AC299D160D4558.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509768779, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509768885, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_90EB9C5EE79D90D5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509769087, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509769179, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D6375AC59926C96F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509769341, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509769451, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_82DD1290973DAA0A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509769624, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509769686, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F8D8DE3502F5351.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509769832, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509769955, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_53DEA5B11540F485.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509770144, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509770276, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_85B7FD10392C25E5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509770415, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509770533, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509770692, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509770819, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E2029A9517246AE3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509770954, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771083, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771210, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771329, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771422, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_4F50087D7CBB79FC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509771603, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771711, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771838, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509771953, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772070, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772183, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772283, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772384, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772494, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772582, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772680, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772773, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772882, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509772987, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773085, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773203, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773305, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773401, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773481, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773591, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773698, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773812, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509773908, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509775229, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509776389, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509777663, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509778724, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509779697, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509780781, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509781825, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509783246, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509784418, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509785680, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509786805, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509788314, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509789626, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509790847, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509792248, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752935509792298, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509792383, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509793658, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509794740, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509795917, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509797234, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509798538, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509800031, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509801153, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509802232, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509803131, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509804497, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509804973, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509805187, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509805635, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509805993, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509806950, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509807136, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509807326, "dur": 2708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509810035, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509810199, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509810654, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509810994, "dur": 1888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509812882, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509813053, "dur": 1931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509814984, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509815144, "dur": 1337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509816481, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509816635, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509818538, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509818652, "dur": 1769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509820423, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752935509820571, "dur": 121877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509942454, "dur": 7589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509950044, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509950168, "dur": 5370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509955538, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509955642, "dur": 9744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509965386, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509965506, "dur": 3901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509969408, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752935509969512, "dur": 5809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752935509975363, "dur": 7060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509757106, "dur": 10867, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509767985, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_00AEED0C6C4B1C5C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509768083, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509768202, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_9C35595FAC430666.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509768303, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509768388, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A76066EDE70828F6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509768545, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509768630, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AA2EC7D0AE738BA2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509768841, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509768983, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_672DEE7C89FDBF70.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509769140, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509769245, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_5F2793B20190FFEB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509769367, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509769498, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_426B10DE4A2273BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509769652, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509769725, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8C3772E74C8FDB15.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509769909, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509770016, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CDA3B62D0035E414.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509770225, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509770339, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7436A9F6B31C8F40.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509770488, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509770603, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_99AF08D3D11EB297.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509770774, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509770872, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_63A18CD40B90EA6B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509770983, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771111, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771225, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771333, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771427, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A238586F3694DF70.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509771598, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771657, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771776, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771888, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509771987, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772102, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772218, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772296, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772424, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772522, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772627, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772721, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772824, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509772921, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773019, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773117, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773223, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773315, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773400, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773476, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773581, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773685, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773795, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509773872, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509775157, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509776326, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509777637, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509778841, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509779807, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509780900, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509781954, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509783350, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509784565, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509785792, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509786966, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509788480, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509789701, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509791017, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509792391, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509793652, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509794756, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509795925, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509797239, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509798535, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509800016, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509801145, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509802223, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509803451, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509804833, "dur": 149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509804983, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509805191, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509805658, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509806133, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509806524, "dur": 1656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509808180, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509808359, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509808806, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509809086, "dur": 1783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509810869, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509811007, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Aseprite.Common.ref.dll_38895AFAB811F11B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509811217, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509811333, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509812312, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509812370, "dur": 1759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509814129, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509814289, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509815095, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509815254, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509817984, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509818078, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509818236, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509818464, "dur": 1267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509819731, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752935509819799, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509820056, "dur": 121147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509941223, "dur": 8506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509949729, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509949821, "dur": 5037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509954858, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509954954, "dur": 10165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509965120, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509965301, "dur": 7407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752935509972708, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509972901, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509972971, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752935509973027, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509973217, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509973367, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509973480, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509973603, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752935509973660, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509973810, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509973958, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509974087, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509974177, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752935509974243, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509974391, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752935509975382, "dur": 7073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509757114, "dur": 10863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509768060, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.HMISimulatorModule.dll_D2B8ECA9541202EF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509768207, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509768270, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B20E237429D8B28C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509768378, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509768456, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CC31E5060D46B483.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509768625, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509768696, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidAppViewModule.dll_67FB5D4225F372B1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509768877, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509769023, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A5A265FE0ABA914E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509769166, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509769260, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AFFF79A28E9D9296.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509769432, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509769530, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_173F666A2C2EDE65.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509769668, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509769761, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_9C0C4E16ADAA7CA2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509769948, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509770049, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_833280D5BBB60BF8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509770280, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509770375, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DAED3E325CAC8CE3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509770529, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509770626, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.OcclusionCullingModule.dll_7593262EC6F4553D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509770773, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509770866, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AutoStreamingModule.dll_4F19E354E89BD82D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509771037, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771165, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_9243E6EC6BA5C023.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509771312, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771409, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771504, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771630, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771704, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771803, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509771933, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772035, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772160, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772247, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772341, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772482, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772565, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772671, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772752, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772846, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509772937, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773047, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773141, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773289, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773390, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773463, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773537, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773648, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773753, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509773846, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509775153, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509776317, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509777625, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509778835, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509779794, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509780847, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509781889, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509783300, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509784468, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509785723, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509786865, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509788377, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509789649, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509790895, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509792333, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509792415, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509793669, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509794770, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509795959, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509797244, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509798557, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509800043, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509801168, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509802240, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509802804, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509804074, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509804673, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509804878, "dur": 51, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509804965, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509805189, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509805767, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509806250, "dur": 2991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509809242, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509809360, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509811551, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509811681, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509812243, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509812552, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509812606, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509813766, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509813924, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509814370, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509816713, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509816820, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752935509817018, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/4700b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509818950, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509819021, "dur": 122171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509941198, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509943662, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509943855, "dur": 3082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Wx.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509946937, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509947025, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509950607, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509950762, "dur": 4576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509955338, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509955452, "dur": 10590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509966042, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752935509966117, "dur": 8409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/4700b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752935509974570, "dur": 7867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752935509984457, "dur": 471, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 39945, "tid": 3070, "ts": 1752935509988318, "dur": 30, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 39945, "tid": 3070, "ts": 1752935509988489, "dur": 3296, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 39945, "tid": 3070, "ts": 1752935509992143, "dur": 107185, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 39945, "tid": 3070, "ts": 1752935509988403, "dur": 86, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509991818, "dur": 324, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935510099460, "dur": 640, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 39945, "tid": 3070, "ts": 1752935509986755, "dur": 113403, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}