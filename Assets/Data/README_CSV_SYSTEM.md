# CSV数据管理系统使用指南

## 🎯 系统概述

CSV数据管理系统为"盲盒纪元"提供了完整的外部数据配置方案，支持：
- ✅ **CSV文件编辑和导入** - 策划友好的数据编辑
- ✅ **ScriptableObject运行时配置** - 高性能的数据存储
- ✅ **JSON平衡参数配置** - 灵活的参数调整
- ✅ **数据验证和热更新** - 安全可靠的数据管理
- ✅ **与现有系统完全兼容** - 无缝集成现有代码

## 📁 目录结构

```
Assets/Data/
├── CSV/                        # CSV源文件（完全可用）
│   ├── BlindBoxes.csv         # 盲盒配置
│   ├── Items.csv              # 物品数据
│   ├── Achievements.csv       # 成就配置
│   ├── AchievementConditions.csv # 成就条件
│   └── AchievementRewards.csv # 成就奖励
├── JSON/                       # JSON配置文件
│   ├── GameBalance.json       # 游戏平衡参数
│   └── MetaGameConfig.json    # 元游戏配置
├── ScriptableObjects/          # 生成的SO文件
│   ├── BlindBoxDataConfig.asset # 盲盒和物品配置SO
│   └── AchievementConfig.asset # 成就配置SO
└── README_CSV_SYSTEM.md       # 本文档
```

## 🔧 使用流程

### **1. 编辑CSV文件**

#### **盲盒配置 (BlindBoxes.csv)**
```csv
id,name,description,boxType,requiredSocialClass,basePrice,lootTable
basic_survival,基础生存盲盒,维持基本生存的必需品,Basic,0,50,empty_box:0.35;expired_food:0.20;clean_water:0.10
```

**字段说明**:
- `id`: 唯一标识符（暂时保留，但主要使用boxType）
- `name`: 显示名称
- `description`: 描述文本
- `boxType`: 盲盒类型 (Basic/Life/Premium/Mystery/BlackMarket等)
- `requiredSocialClass`: 需要的社会阶层等级
- `basePrice`: 基础价格
- `lootTable`: 掉落表 (格式: itemId:probability;itemId:probability)

#### **物品数据 (Items.csv)**
```csv
id,name,description,contentType,rarity,value,isConsumable
empty_box,空盒,什么都没有,Empty,Common,0,true
clean_water,净水,干净的饮用水,Water,Common,8,true
```

**字段说明**:
- `id`: 唯一标识符
- `name`: 物品名称
- `description`: 物品描述
- `contentType`: 内容类型 (Empty/Food/Water/Clothing/Medicine/Technology等)
- `rarity`: 稀有度 (Common/Uncommon/Rare/Epic/Legendary)
- `value`: 物品价值（可以为负数表示负面效果）
- `isConsumable`: 是否可消耗

#### **成就配置 (Achievements.csv)**
```csv
id,name,description,category,rarity,isHidden,iconPath,conditions,rewards
first_week,初来乍到,生存第一周,Survival,Common,false,icons/survival_week,DaysSurvived:7,Resource:Credits:100
```

**字段说明**:
- `id`: 唯一标识符
- `name`: 成就名称
- `description`: 成就描述
- `category`: 成就类别 (Survival/BlindBox/Social/Story/Economic)
- `rarity`: 稀有度 (Common/Uncommon/Rare/Epic/Legendary)
- `isHidden`: 是否隐藏成就
- `iconPath`: 图标路径
- `conditions`: 条件（在单独的CSV中详细定义）
- `rewards`: 奖励（在单独的CSV中详细定义）

#### **成就条件 (AchievementConditions.csv)**
```csv
achievementId,conditionType,value,trackerId,attributeName,resourceType,description
first_week,DaysSurvived,7,,,,"生存7天"
```

#### **成就奖励 (AchievementRewards.csv)**
```csv
achievementId,rewardType,value,resourceType,attributeName,title,eventId,description
first_week,Resource,100,Credits,,,,获得100信用点
```

### **2. 导入CSV数据**

#### **使用Unity菜单**
1. 在Unity编辑器中选择 `Tools > CSV Import > Import All CSV Data`
2. 或者选择具体的导入选项：
   - `Import BlindBox Data` - 导入盲盒数据
   - `Import Item Data` - 导入物品数据
   - `Import Achievement Data` - 导入成就数据

#### **自动生成ScriptableObject**
导入完成后，系统会在 `Assets/Data/ScriptableObjects/` 目录下生成或更新：
- `BlindBoxDataConfig.asset` - 包含盲盒和物品配置
- `AchievementConfig.asset` - 包含成就和进度追踪器配置

### **3. 配置GameDataSystem**

#### **在场景中添加GameDataSystem**
1. 创建空GameObject，命名为"GameDataSystem"
2. 添加`GameDataSystem`组件
3. 在Inspector中配置引用：
   - `Blind Box Data Config` → 拖入BlindBoxDataConfig.asset
   - `Achievement Config` → 拖入AchievementConfig.asset
   - `Game Balance Json` → 拖入GameBalance.json
   - `Meta Game Config Json` → 拖入MetaGameConfig.json

### **2. 编辑JSON配置文件**

#### **游戏平衡配置 (GameBalance.json)**
```json
{
  "fraudAlgorithm": {
    "maxProbabilityDeviation": 0.3,
    "socialClassBias": [0.7, 0.8, 1.0, 1.2, 1.5],
    "addictionThreshold": 75.0
  },
  "socialClass": {
    "classes": [...],
    "promotionRequirements": [...]
  }
}
```

#### **元游戏配置 (MetaGameConfig.json)**
```json
{
  "awakeningMultipliers": {
    "Death": 2.0,
    "Rebellion": 12.0
  },
  "phases": [...],
  "progression": {...}
}
```

### **4. 在代码中使用数据**

#### **获取数据系统实例**
```csharp
var dataSystem = GameDataSystem.Instance;
if (dataSystem != null && dataSystem.IsDataLoaded())
{
    // 使用数据系统
}
```

#### **获取盲盒和物品数据**
```csharp
// 获取盲盒配置
var basicBox = dataSystem.GetBlindBoxConfig(BlindBoxType.Basic);
if (basicBox != null)
{
    Debug.Log($"基础盲盒: {basicBox.name}, 价格: {basicBox.basePrice}");
}

// 获取所有盲盒配置
var allBoxes = dataSystem.GetAllBlindBoxConfigs();

// 获取物品数据
var waterItem = dataSystem.GetItemData("clean_water");
if (waterItem != null)
{
    Debug.Log($"净水: {waterItem.name}, 价值: {waterItem.value}");
}

// 获取指定类型的物品
var foodItems = dataSystem.GetItemsByType(BlindBoxContentType.Food);

// 获取成就数据
var firstWeekAchievement = dataSystem.GetAchievement("first_week");
var allAchievements = dataSystem.GetAllAchievements();

// 获取指定类别的成就
var survivalAchievements = dataSystem.GetAchievementsByCategory(AchievementCategory.Survival);

// 获取进度追踪器
var tracker = dataSystem.GetProgressTracker("days_survived");
```

#### **获取配置数据**
```csharp
// 获取游戏平衡配置
var gameBalance = dataSystem.GetGameBalance();
if (gameBalance?.fraudAlgorithm != null)
{
    float maxDeviation = gameBalance.fraudAlgorithm.maxProbabilityDeviation;
}

// 获取元游戏配置
var metaConfig = dataSystem.GetMetaGameConfig();
if (metaConfig?.awakeningMultipliers != null)
{
    float deathMultiplier = metaConfig.awakeningMultipliers["Death"];
}
```

#### **在BlindBoxSystem中使用**
```csharp
// BlindBoxSystem会自动使用GameDataSystem的数据
var blindBoxSystem = new BlindBoxSystem(GameDataSystem.Instance);

// 开盒时会使用CSV配置的数据
var result = blindBoxSystem.OpenBox(BlindBoxType.Basic, playerData);

// AchievementSystem会自动使用GameDataSystem的数据
var achievementSystem = new AchievementSystem(GameDataSystem.Instance);

// 更新成就进度
achievementSystem.UpdateProgress("days_survived", 7f, playerData);
```

## 🔄 热更新支持

### **重新加载数据**
```csharp
// 重新加载所有数据
GameDataSystem.Instance.ReloadData();
```

### **运行时更新**
1. 修改JSON文件
2. 调用ReloadData()方法

## 📊 JSON配置详解

### **GameBalance.json结构**
```json
{
  "fraudAlgorithm": {
    "maxProbabilityDeviation": 0.3,
    "socialClassBias": [0.7, 0.8, 1.0, 1.2, 1.5],
    "addictionThreshold": 75.0,
    "manipulationIntensity": 1.2,
    "detectionSensitivity": 3
  },
  "socialClass": {
    "classes": [
      {
        "level": 0,
        "name": "底层民众",
        "creditMultiplier": 0.8,
        "privilegeBonus": 0.0
      }
    ]
  }
}
```

### **MetaGameConfig.json结构**
```json
{
  "awakeningMultipliers": {
    "Death": 2.0,
    "Rebellion": 12.0,
    "Transcendence": 15.0
  },
  "phases": [
    {
      "id": "unconscious",
      "name": "无意识阶段",
      "requiredAwakening": 0.0
    }
  ]
}
```

## 🛠️ 开发工具

### **CSV编辑器推荐**
- **Excel/WPS**: 最常用，支持公式和数据验证
- **Google Sheets**: 在线协作，实时同步
- **LibreOffice Calc**: 免费开源选择
- **Visual Studio Code**: 支持CSV语法高亮

### **JSON编辑器推荐**
- **Visual Studio Code**: 支持JSON语法高亮和验证
- **任何文本编辑器**: JSON格式简单易读

### **Unity编辑器集成**
系统提供了完整的Unity编辑器集成：
- `Tools > CSV Import` 菜单 - 一键导入CSV数据
- 自动ScriptableObject生成和更新
- 实时数据验证和错误提示

### **调试工具**
使用`SimpleCSVExample`组件进行测试：
1. 添加到场景中的任意GameObject
2. 使用右键菜单的测试选项：
   - "测试数据系统" - 全面测试所有数据
   - "重新加载数据" - 热更新数据
   - "显示数据统计" - 查看数据概览
   - "测试系统集成" - 测试与BlindBoxSystem的集成
3. 查看Console输出的详细信息

## ⚠️ 注意事项

### **CSV格式要求**
- 使用UTF-8编码保存
- 第一行必须是字段名
- 字段间用逗号分隔
- 包含逗号的文本需要用引号包围
- 概率值应为0-1之间的小数

### **JSON格式要求**
- 使用UTF-8编码保存
- 严格遵循JSON语法
- 字符串需要用双引号包围
- 数组和对象格式正确

### **数据一致性**
- 确保CSV中的itemId在Items.csv中存在
- 掉落表概率总和建议接近1.0
- 物品价值可以为负数（表示负面效果）

### **性能考虑**
- CSV导入只在编辑器中进行
- 运行时使用ScriptableObject，性能优秀
- 大量数据建议分批加载

### **版本控制**
- CSV文件应纳入版本控制
- ScriptableObject文件也应纳入版本控制
- JSON配置文件建议纳入版本控制

## 🚀 扩展指南

### **添加新的CSV数据类型**
1. 在现有的数据结构中添加新字段
2. 在`CSVDataImporter.cs`中添加解析逻辑
3. 更新CSV文件格式
4. 在`GameDataSystem.cs`中添加访问接口

### **扩展JSON配置**
1. 在`JsonDataStructures.cs`中扩展配置结构
2. 更新JSON文件格式
3. 在`GameDataSystem.cs`中添加加载逻辑

### **自定义验证规则**
1. 在`CSVDataImporter.cs`中添加数据验证
2. 检查ID唯一性、概率总和等
3. 提供详细的错误信息

---

## 🎉 总结

CSV数据管理系统为"盲盒纪元"提供了：
- 📝 **策划友好** - 使用Excel编辑CSV，无需编程知识
- 🔧 **开发高效** - 一键导入，自动生成ScriptableObject
- 🚀 **性能优秀** - 运行时使用SO，加载快速
- 🔄 **支持热更** - 可运行时重新加载数据
- 🛡️ **数据安全** - 完整的验证和错误处理
- 🔗 **完全兼容** - 与现有BlindBoxSystem无缝集成

### **立即可用的功能**
✅ **CSV编辑** - 修改BlindBoxes.csv、Items.csv和成就相关CSV
✅ **一键导入** - Tools > CSV Import > Import All CSV Data
✅ **实时测试** - 使用SimpleCSVExample组件验证
✅ **系统集成** - BlindBoxSystem和AchievementSystem自动使用CSV数据
✅ **热更新** - 运行时重新加载配置
✅ **成就管理** - 完整的成就系统数据配置

**现在你可以轻松管理游戏的所有盲盒、物品和成就配置数据了！** ✨🎮🏆
