achievementId,conditionType,value,trackerId,attributeName,resourceType,description
first_week,DaysSurvived,7,,,,"生存7天"
month_survivor,DaysSurvived,30,,,,"生存30天"
year_survivor,DaysSurvived,365,,,,"生存365天"
box_opener,BoxesOpened,1,,,,"开启1个盲盒"
box_addict,BoxesOpened,100,,,,"开启100个盲盒"
box_master,BoxesOpened,1000,,,,"开启1000个盲盒"
lucky_streak,ConsecutiveRareItems,5,,,,"连续获得5个稀有物品"
empty_collector,EmptyBoxesReceived,50,,,,"获得50个空盒"
social_climber,SocialClass,1,,,,"达到工蜂阶层"
elite_member,SocialClass,4,,,,"达到精英阶层"
rebel_heart,BoxRefusals,10,,,,"拒绝开盒10次"
system_hacker,SystemHacks,3,,,,"成功破解系统3次"
truth_seeker,TruthsDiscovered,1,,,,"发现1个真相碎片"
awakened_one,AwakeningValue,100,,,,"觉醒值达到100"
transcendent,EndingReached,1,,,,"达到超越结局"
manipulated_victim,ManipulationCount,50,,,,"被操控50次"
perfect_citizen,SocialScore,1000,,,,"社会积分达到1000"
resource_hoarder,ResourceValue,10000,,,Credits,"拥有10000信用点"
diamond_collector,ResourceValue,100,,,Diamond,"拥有100颗钻石"
survivor_instinct,MinHealthMaintained,10,,health,,"健康值从未低于10"
energy_master,HighEnergyDays,30,,energy,,"精力值保持在80以上30天"
moral_guardian,MinMoralityMaintained,70,,morality,,"道德值从未低于70"
humanity_keeper,MinHumanityMaintained,90,,humanity,,"人性值保持在90以上"
addiction_victim,AttributeValue,90,,dependence,,"依存度达到90"
pollution_survivor,HighPollutionSurvival,80,,pollution,,"污染度达到80仍然生存"
social_butterfly,AttributeValue,90,,social,,"社交值达到90"
lucky_charm,AttributeValue,80,,luck,,"幸运值达到80"
first_death,DeathCount,1,,,,"第一次死亡"
death_defier,DeathCount,10,,,,"死亡10次"
ending_collector,UniqueEndings,5,,,,"达成5个不同结局"
