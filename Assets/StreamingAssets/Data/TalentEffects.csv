talentId,effectType,targetType,value,duration,stringValue,description
GamblerGene,ProbabilityModifier,,0.1,,gambler_gene,"赌徒基因概率修正"
GamblerGene,AttributeChange,Luck,2,,,"幸运值+2"
SocialMaster,AttributeChange,Social,20,,,"社交值+20"
SocialMaster,SpecialAbility,,,promotion_bonus,"晋升奖励"
RichKid,ResourceChange,Credits,1000,,,"信用点+1000"
RichKid,ResourceChange,Diamond,5,,,"钻石+5"
HackerGenius,SpecialAbility,,,probability_hack,"概率破解"
HackerGenius,StatusImmunity,SystemTracking,,,,"系统追踪免疫"
UnluckyOne,AttributeChange,Luck,-3,,,"幸运值-3"
UnluckyOne,SpecialAbility,,,rare_event_trigger,"稀有事件触发"
Workaholic,SpecialAbility,,,work_efficiency_boost,"工作效率提升"
Workaholic,ResourceChange,Credits,50,,,"信用点+50"
Workaholic,AttributeChange,Energy,-10,,,"精力上限-10"
MadGenius,AttributeChange,Cognition,15,,,"认知值+15"
MadGenius,SpecialAbility,,,genius_insight,"天才洞察"
MadGenius,SpecialAbility,,,mental_instability,"精神不稳定"
MadGenius,StatusImmunity,CognitiveDamage,,,,"认知损害免疫"
ImmuneVariant,StatusImmunity,Sickness,,,,"疾病免疫"
ImmuneVariant,StatusImmunity,DataCorruption,,,,"数据损坏免疫"
ImmuneVariant,StatusImmunity,CognitiveDamage,,,,"认知损害免疫"
ImmuneVariant,AttributeChange,Health,20,,,"生命值+20"
BlindBoxIntuition,SpecialAbility,,,box_content_preview,"盲盒内容预览"
BlindBoxIntuition,SpecialAbility,,,trap_avoidance,"陷阱规避"
BlindBoxIntuition,ProbabilityModifier,,0.25,,positive_outcome,"正面结果概率提升"
BlindBoxIntuition,AttributeChange,Luck,10,,,"幸运值+10"
SystemRebel,StatusImmunity,SystemTracking,,,,"系统追踪免疫"
SystemRebel,AttributeChange,Morality,5,,,"道德值+5"
SystemRebel,SpecialAbility,,,system_resistance,"系统抗性"
LuckyCharm,AttributeChange,Luck,5,,,"幸运值+5"
LuckyCharm,ProbabilityModifier,,0.05,,lucky_boost,"幸运提升"
DataMiner,SpecialAbility,,,data_analysis,"数据分析"
DataMiner,AttributeChange,Cognition,5,,,"认知值+5"
SurvivalInstinct,SpecialAbility,,,auto_protection,"自动保护"
SurvivalInstinct,AttributeChange,Health,15,,,"生命值+15"
MemoryKeeper,StatusImmunity,CognitiveDamage,,,,"认知损害免疫"
MemoryKeeper,StatusImmunity,MemoryCorruption,,,,"记忆损坏免疫"
MemoryKeeper,AttributeChange,Cognition,10,,,"认知值+10"
ResourceMaster,SpecialAbility,,,resource_efficiency,"资源效率"
ResourceMaster,ResourceChange,Credits,200,,,"信用点+200"
