talentId,conditionType,targetType,targetValue,minValue,maxValue,stringValue,description
GamblerG<PERSON>,BoxesOpened,,20,,,,"开盒数量达到20"
SocialMaster,AttributeThreshold,Social,70,,,,"社交值达到70"
HackerGenius,AttributeThreshold,Cognition,80,,,,"认知值达到80"
HackerGenius,SpecialEvent,,,,,system_glitch,"触发系统故障事件"
UnluckyOne,,,,,,,,"无条件(开局天赋)"
RichKid,,,,,,,,"无条件(开局天赋)"
Workaholic,DaysSurvived,,10,,,,"生存10天"
Workaholic,SpecialEvent,,,,,work_overtime,"加班事件"
MadGenius,AttributeThreshold,Cognition,90,,,,"认知值达到90"
MadGenius,AttributeThreshold,Humanity,30,,,,"人性值低于30"
ImmuneVariant,SpecialEvent,,,,,virus_infection,"感染病毒事件"
ImmuneVariant,AttributeThreshold,Humanity,60,,,,"人性值达到60"
BlindBoxIntuition,BoxesOpened,,100,,,,"开盒数量达到100"
BlindBoxIntuition,AttributeThreshold,Luck,75,,,,"幸运值达到75"
BlindBoxIntuition,SpecialEvent,,,,,perfect_streak,"完美连击事件"
SystemRebel,AttributeThreshold,Morality,80,,,,"道德值达到80"
SystemRebel,SpecialEvent,,,,,system_oppression,"系统压迫事件"
LuckyCharm,AttributeThreshold,Luck,50,,,,"幸运值达到50"
DataMiner,AttributeThreshold,Cognition,70,,,,"认知值达到70"
DataMiner,SpecialEvent,,,,,data_discovery,"数据发现事件"
SurvivalInstinct,DaysSurvived,,30,,,,"生存30天"
SurvivalInstinct,SpecialEvent,,,,,near_death,"濒死体验事件"
MemoryKeeper,AttributeThreshold,Cognition,60,,,,"认知值达到60"
MemoryKeeper,SpecialEvent,,,,,memory_attack,"记忆攻击事件"
ResourceMaster,ResourceThreshold,Credits,1000,,,,"信用点达到1000"
