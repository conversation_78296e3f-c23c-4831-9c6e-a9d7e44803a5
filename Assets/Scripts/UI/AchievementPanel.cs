using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using System.Collections.Generic;
using System.Linq;
using BoxOfFate.Game.Core;

/// <summary>
/// 成就面板 - 显示成就列表和进度
/// </summary>
public class AchievementPanel : UIBase
{
    [Header("UI组件")]
    public Button closeButton;
    public TextMeshProUGUI titleText;
    public TextMeshProUGUI progressText;

    [Header("分类标签")]
    public Transform categoryTabsContainer;
    public Button allCategoryButton;
    public Button survivalCategoryButton;
    public Button blindBoxCategoryButton;
    public Button socialCategoryButton;
    public Button storyCategoryButton;

    [Header("成就列表")]
    public Transform achievementListContainer;
    public ScrollRect achievementScrollRect;

    [Header("成就详情")]
    public TextMeshProUGUI achievementNameText;
    public TextMeshProUGUI achievementDescriptionText;
    public TextMeshProUGUI achievementProgressText;
    public TextMeshProUGUI achievementRewardsText;
    public Image achievementIcon;
    public Image achievementRarityBorder;

    private GameLogic gameLogic;
    private AchievementCategory currentCategory = AchievementCategory.Survival;
    private List<Achievement> allAchievements = new List<Achievement>();
    private List<GameObject> achievementObjects = new List<GameObject>();
    private Achievement selectedAchievement;

    public override void OnInit()
    {
        gameLogic = GameLogic.Instance;
        InitializeUIReferences();
        BindButtonEvents();
    }

    public override void OnShow()
    {
        LoadAchievements();
        RefreshAchievementList();
        RefreshProgressInfo();
    }

    /// <summary>
    /// 初始化UI组件引用
    /// </summary>
    private void InitializeUIReferences()
    {
        closeButton = transform.Get<Button>("Header/BtnClose");
        titleText = transform.Get<TextMeshProUGUI>("Header/Title");
        progressText = transform.Get<TextMeshProUGUI>("Header/Progress");

        categoryTabsContainer = transform.Get<Transform>("CategoryTabs");
        allCategoryButton = transform.Get<Button>("CategoryTabs/AllBtn");
        survivalCategoryButton = transform.Get<Button>("CategoryTabs/SurvivalBtn");
        blindBoxCategoryButton = transform.Get<Button>("CategoryTabs/BlindBoxBtn");
        socialCategoryButton = transform.Get<Button>("CategoryTabs/SocialBtn");
        storyCategoryButton = transform.Get<Button>("CategoryTabs/StoryBtn");

        achievementListContainer = transform.Get<Transform>("Content/AchievementList/Viewport/Content");
        achievementScrollRect = transform.Get<ScrollRect>("Content/AchievementList");

        achievementNameText = transform.Get<TextMeshProUGUI>("Content/AchievementDetails/Name");
        achievementDescriptionText = transform.Get<TextMeshProUGUI>("Content/AchievementDetails/Description");
        achievementProgressText = transform.Get<TextMeshProUGUI>("Content/AchievementDetails/Progress");
        achievementRewardsText = transform.Get<TextMeshProUGUI>("Content/AchievementDetails/Rewards");
        achievementIcon = transform.Get<Image>("Content/AchievementDetails/Icon");
        achievementRarityBorder = transform.Get<Image>("Content/AchievementDetails/RarityBorder");
    }

    /// <summary>
    /// 绑定按钮事件
    /// </summary>
    private void BindButtonEvents()
    {
        closeButton?.onClick.AddListener(OnCloseClicked);
        
        allCategoryButton?.onClick.AddListener(() => OnCategoryChanged(AchievementCategory.Survival)); // 使用Survival作为All
        survivalCategoryButton?.onClick.AddListener(() => OnCategoryChanged(AchievementCategory.Survival));
        blindBoxCategoryButton?.onClick.AddListener(() => OnCategoryChanged(AchievementCategory.BlindBox));
        socialCategoryButton?.onClick.AddListener(() => OnCategoryChanged(AchievementCategory.Social));
        storyCategoryButton?.onClick.AddListener(() => OnCategoryChanged(AchievementCategory.Story));
    }

    /// <summary>
    /// 加载成就数据
    /// </summary>
    private void LoadAchievements()
    {
        if (gameLogic?.achievementSystem == null) return;

        allAchievements = gameLogic.GetAchievements();
    }

    /// <summary>
    /// 刷新成就列表
    /// </summary>
    private void RefreshAchievementList()
    {
        // 清除现有成就项
        foreach (var obj in achievementObjects)
        {
            if (obj != null) DestroyImmediate(obj);
        }
        achievementObjects.Clear();

        // 筛选当前分类的成就
        var filteredAchievements = allAchievements.Where(a => a.category == currentCategory).ToList();
        
        // 按解锁状态和稀有度排序
        filteredAchievements = filteredAchievements
            .OrderByDescending(a => a.isUnlocked)
            .ThenBy(a => a.rarity)
            .ToList();

        // 创建成就项
        foreach (var achievement in filteredAchievements)
        {
            CreateAchievementItem(achievement);
        }
    }

    /// <summary>
    /// 创建成就项
    /// </summary>
    private void CreateAchievementItem(Achievement achievement)
    {
        GameObject item = CreateAchievementObject(achievement.name, achievementListContainer);
        achievementObjects.Add(item);

        // 设置成就信息
        var nameText = item.transform.Get<TextMeshProUGUI>("Name");
        var statusText = item.transform.Get<TextMeshProUGUI>("Status");
        var progressBar = item.transform.Get<Slider>("ProgressBar");
        var button = item.GetComponent<Button>();

        if (nameText != null)
            nameText.text = achievement.name;

        if (statusText != null)
        {
            statusText.text = achievement.isUnlocked ? "已解锁" : "未解锁";
            statusText.color = achievement.isUnlocked ? Color.green : Color.gray;
        }

        if (progressBar != null)
        {
            float progress = GetAchievementProgress(achievement);
            progressBar.value = progress;
            progressBar.fillRect.GetComponent<Image>().color = GetRarityColor(achievement.rarity);
        }

        if (button != null)
        {
            button.onClick.AddListener(() => OnAchievementSelected(achievement));
        }

        // 设置背景颜色
        var image = item.GetComponent<Image>();
        if (image != null)
        {
            Color bgColor = achievement.isUnlocked ? 
                GetRarityColor(achievement.rarity) : 
                new Color(0.3f, 0.3f, 0.3f, 0.5f);
            bgColor.a = 0.3f;
            image.color = bgColor;
        }
    }

    /// <summary>
    /// 创建成就对象
    /// </summary>
    private GameObject CreateAchievementObject(string name, Transform parent)
    {
        GameObject obj = new GameObject(name);
        obj.transform.SetParent(parent, false);

        RectTransform rect = obj.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 80);
        
        Image image = obj.AddComponent<Image>();
        Button button = obj.AddComponent<Button>();
        
        VerticalLayoutGroup layout = obj.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(10, 10, 5, 5);
        layout.spacing = 5;

        // 成就名称
        GameObject nameTextGO = new GameObject("Name");
        nameTextGO.transform.SetParent(obj.transform, false);
        TextMeshProUGUI nameText = nameTextGO.AddComponent<TextMeshProUGUI>();
        nameText.fontSize = 16;
        nameText.color = Color.white;

        // 状态文本
        GameObject statusTextGO = new GameObject("Status");
        statusTextGO.transform.SetParent(obj.transform, false);
        TextMeshProUGUI statusText = statusTextGO.AddComponent<TextMeshProUGUI>();
        statusText.fontSize = 12;
        statusText.color = Color.gray;

        // 进度条
        GameObject progressBarGO = new GameObject("ProgressBar");
        progressBarGO.transform.SetParent(obj.transform, false);
        
        RectTransform progressRect = progressBarGO.AddComponent<RectTransform>();
        progressRect.sizeDelta = new Vector2(0, 10);
        
        Slider progressBar = progressBarGO.AddComponent<Slider>();
        progressBar.minValue = 0f;
        progressBar.maxValue = 1f;
        progressBar.value = 0f;

        // 创建进度条的背景和填充
        GameObject background = new GameObject("Background");
        background.transform.SetParent(progressBarGO.transform, false);
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.2f, 0.2f, 0.2f, 0.5f);
        
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;

        GameObject fillArea = new GameObject("Fill Area");
        fillArea.transform.SetParent(progressBarGO.transform, false);
        
        RectTransform fillAreaRect = fillArea.AddComponent<RectTransform>();
        fillAreaRect.anchorMin = Vector2.zero;
        fillAreaRect.anchorMax = Vector2.one;
        fillAreaRect.offsetMin = Vector2.zero;
        fillAreaRect.offsetMax = Vector2.zero;

        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(fillArea.transform, false);
        Image fillImage = fill.AddComponent<Image>();
        fillImage.color = Color.green;
        
        RectTransform fillRect = fill.GetComponent<RectTransform>();
        fillRect.anchorMin = Vector2.zero;
        fillRect.anchorMax = Vector2.one;
        fillRect.offsetMin = Vector2.zero;
        fillRect.offsetMax = Vector2.zero;

        progressBar.fillRect = fillRect;

        return obj;
    }

    /// <summary>
    /// 成就选择事件
    /// </summary>
    private void OnAchievementSelected(Achievement achievement)
    {
        selectedAchievement = achievement;
        DisplayAchievementDetails(achievement);
    }

    /// <summary>
    /// 显示成就详情
    /// </summary>
    private void DisplayAchievementDetails(Achievement achievement)
    {
        if (achievementNameText != null)
            achievementNameText.text = achievement.name;

        if (achievementDescriptionText != null)
            achievementDescriptionText.text = achievement.description;

        if (achievementProgressText != null)
        {
            float progress = GetAchievementProgress(achievement);
            achievementProgressText.text = achievement.isUnlocked ? 
                $"已解锁 ({achievement.unlockedDate:yyyy-MM-dd})" : 
                $"进度: {progress:P0}";
        }

        if (achievementRewardsText != null)
            achievementRewardsText.text = GetRewardsText(achievement);

        if (achievementIcon != null)
            achievementIcon.color = GetCategoryColor(achievement.category);

        if (achievementRarityBorder != null)
            achievementRarityBorder.color = GetRarityColor(achievement.rarity);
    }

    /// <summary>
    /// 分类改变事件
    /// </summary>
    private void OnCategoryChanged(AchievementCategory category)
    {
        currentCategory = category;
        RefreshAchievementList();
        UpdateCategoryButtons();
    }

    /// <summary>
    /// 更新分类按钮状态
    /// </summary>
    private void UpdateCategoryButtons()
    {
        // 重置所有按钮颜色
        var buttons = new[] { allCategoryButton, survivalCategoryButton, blindBoxCategoryButton, socialCategoryButton, storyCategoryButton };
        foreach (var btn in buttons)
        {
            if (btn != null)
            {
                var colors = btn.colors;
                colors.normalColor = Color.white;
                btn.colors = colors;
            }
        }

        // 高亮当前选中的按钮
        Button selectedButton = currentCategory switch
        {
            AchievementCategory.Survival => survivalCategoryButton,
            AchievementCategory.BlindBox => blindBoxCategoryButton,
            AchievementCategory.Social => socialCategoryButton,
            AchievementCategory.Story => storyCategoryButton,
            _ => allCategoryButton
        };

        if (selectedButton != null)
        {
            var colors = selectedButton.colors;
            colors.normalColor = Color.yellow;
            selectedButton.colors = colors;
        }
    }

    /// <summary>
    /// 刷新进度信息
    /// </summary>
    private void RefreshProgressInfo()
    {
        if (progressText == null) return;

        int totalAchievements = allAchievements.Count;
        int unlockedAchievements = allAchievements.Count(a => a.isUnlocked);
        
        progressText.text = $"成就进度: {unlockedAchievements}/{totalAchievements} ({(float)unlockedAchievements / totalAchievements:P0})";
    }

    /// <summary>
    /// 获取成就进度
    /// </summary>
    private float GetAchievementProgress(Achievement achievement)
    {
        if (achievement.isUnlocked) return 1f;
        
        if (gameLogic?.achievementSystem != null && gameLogic?.playerData != null)
        {
            return gameLogic.achievementSystem.GetAchievementProgress(achievement.id, gameLogic.playerData);
        }
        
        return 0f;
    }

    /// <summary>
    /// 获取奖励文本
    /// </summary>
    private string GetRewardsText(Achievement achievement)
    {
        if (achievement.rewards == null || achievement.rewards.Count == 0)
            return "无奖励";

        string rewardsText = "奖励:\n";
        foreach (var reward in achievement.rewards)
        {
            rewardsText += GetRewardText(reward) + "\n";
        }
        
        return rewardsText;
    }

    /// <summary>
    /// 获取单个奖励文本
    /// </summary>
    private string GetRewardText(GameEffect reward)
    {
        return reward.type switch
        {
            GameEffectType.ResourceChange => $"• {reward.resourceType}: +{reward.value:F0}",
            GameEffectType.AttributeChange => $"• {reward.attributeType}: +{reward.value:F0}",
            GameEffectType.Talent => $"• 天赋: {reward.talentType}",
            GameEffectType.UnlockEvent => $"• 解锁事件: {reward.stringValue}",
            GameEffectType.Title => $"• 称号: {reward.stringValue}",
            _ => "• 未知奖励"
        };
    }

    /// <summary>
    /// 关闭按钮点击
    /// </summary>
    private async void OnCloseClicked()
    {
        await UISystem.Close(UIName.ACHIEVEMENT_PANEL);
    }

    // 辅助方法
    private Color GetRarityColor(AchievementRarity rarity)
    {
        return rarity switch
        {
            AchievementRarity.Common => Color.white,
            AchievementRarity.Uncommon => Color.green,
            AchievementRarity.Rare => Color.blue,
            AchievementRarity.Epic => Color.magenta,
            AchievementRarity.Legendary => Color.yellow,
            _ => Color.gray
        };
    }

    private Color GetCategoryColor(AchievementCategory category)
    {
        return category switch
        {
            AchievementCategory.Survival => Color.green,
            AchievementCategory.BlindBox => Color.magenta,
            AchievementCategory.Social => Color.blue,
            AchievementCategory.Story => Color.yellow,
            AchievementCategory.Special => Color.red,
            _ => Color.white
        };
    }
}
