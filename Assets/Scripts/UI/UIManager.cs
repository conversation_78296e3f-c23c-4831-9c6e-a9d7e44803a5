using UnityEngine;
using BoxOfFate.Game;
using BoxOfFate.UI;

/// <summary>
/// UI管理器 - 统一管理游戏UI的显示和交互
/// </summary>
public class UIManager : MonoBehaviour
{
    [Header("UI设置")]
    public bool autoInitialize = true;
    public bool showDebugInfo = false;

    private GameLogic gameLogic;
    private static UIManager _instance;

    public static UIManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<UIManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("UIManager");
                    _instance = go.AddComponent<UIManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);

            if (autoInitialize)
            {
                Initialize();
            }
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }

    /// <summary>
    /// 初始化UI管理器
    /// </summary>
    public void Initialize()
    {
        gameLogic = GameLogic.Instance;

        // 订阅游戏事件
        SubscribeToGameEvents();


        Debug.Log("UI管理器初始化完成");
    }

    /// <summary>
    /// 订阅游戏事件
    /// </summary>
    private void SubscribeToGameEvents()
    {
        if (gameLogic != null && gameLogic.gameFlowController != null)
        {
            gameLogic.gameFlowController.OnGameEnded += OnGameEnded;
        }

        if (gameLogic != null && gameLogic.eventManager != null)
        {
            gameLogic.eventManager.OnEventTriggered += OnEventTriggered;
        }
    }


    /// <summary>
    /// 显示提示信息
    /// </summary>
    public async void ShowSplashScreen()
    {
        await UISystem.Open<SplashPanel>(UIName.SPLASH_PANEL);
    }


    /// <summary>
    /// 开始游戏
    /// </summary>
    public async void StartGame()
    {
        if (gameLogic == null) return;

        // 尝试加载游戏
        bool loadSuccess = gameLogic.LoadGame();



        if (loadSuccess)
        {

            if (showDebugInfo)
            {
                Debug.Log("游戏继续");
            }
        }
        else
        {
            // 显示加载失败提示
            gameLogic.StartNewGame();
        }

        await UISystem.Open<GamePanel>(UIName.GAME_PANEL);
    }

    /// <summary>
    /// 暂停游戏
    /// </summary>
    public async void PauseGame()
    {
        await UISystem.Open<PausePopup>(UIName.PAUSE_POPUP);
    }

    /// <summary>
    /// 显示设置
    /// </summary>
    public async void ShowSettings()
    {
        await UISystem.Open<SettingsPanel>(UIName.SETTINGS_PANEL);
    }

    /// <summary>
    /// 显示排行榜
    /// </summary>
    public async void ShowRanking()
    {
        await UISystem.Open<RankingPanel>(UIName.RANKING_PANEL);
    }


    /// <summary>
    /// 显示工作面板
    /// </summary>
    public async void ShowWorkPanel()
    {
        await UISystem.Open<WorkPanel>(UIName.WORK_PANEL);
    }

    /// <summary>
    /// 显示黑市面板
    /// </summary>
    public async void ShowBlackMarketPanel()
    {
        await UISystem.Open<BlackMarketPanel>(UIName.BLACK_MARKET_PANEL);
    }

    /// <summary>
    /// 显示特权面板
    /// </summary>
    public async void ShowPrivilegePanel()
    {
        await UISystem.Open<PrivilegePanel>(UIName.PRIVILEGE_PANEL);
    }

    /// <summary>
    /// 显示晋升面板
    /// </summary>
    public async void ShowPromotionPanel()
    {
        await UISystem.Open<PromotionPanel>(UIName.PROMOTION_PANEL);
    }

    /// <summary>
    /// 显示成就面板
    /// </summary>
    public async void ShowAchievementPanel()
    {
        await UISystem.Open<AchievementPanel>(UIName.ACHIEVEMENT_PANEL);
    }

    /// <summary>
    /// 显示盲盒结果
    /// </summary>
    public async void ShowBlindBoxResult(BlindBoxResult result)
    {
        var param = new BlindBoxResultParam(result);
        await UISystem.Open<BlindBoxResultPopup>(UIName.BLIND_BOX_RESULT_POPUP, param);
    }

    /// <summary>
    /// 显示工作结果
    /// </summary>
    public async void ShowWorkResult(WorkResult result)
    {
        var param = new WorkResultParam(result);
        await UISystem.Open<WorkResultPopup>(UIName.WORK_RESULT_POPUP, param);
    }

    /// <summary>
    /// 显示事件对话框
    /// </summary>
    public async void ShowEventDialog(GameEvent gameEvent)
    {
        var param = new EventDialogParam(gameEvent);
        await UISystem.Open<EventDialog>(UIName.EVENT_DIALOG, param);
    }

    /// <summary>
    /// 显示简单事件对话框
    /// </summary>
    public async void ShowEventDialog(string title, string content)
    {
        // 创建一个简单的GameEvent
        var gameEvent = new GameEvent
        {
            id = "simple_event",
            title = title,
            description = content,
            type = BoxOfFate.Game.EventType.System
        };

        var param = new EventDialogParam(gameEvent);
        await UISystem.Open<EventDialog>(UIName.EVENT_DIALOG, param);
    }

    /// <summary>
    /// 显示游戏结束面板
    /// </summary>
    public async void ShowGameOverPanel(GameEndType endType, string message)
    {
        var statistics = gameLogic != null ? gameLogic.GetGameStatistics() : null;
        var param = new GameOverParam(endType, message, statistics);
        await UISystem.Open<GameOverPanel>(UIName.GAME_OVER_PANEL, param);
    }

    /// <summary>
    /// 显示提示信息
    /// </summary>
    public async void ShowAlert(string message, string title = null)
    {
        var param = new AlertParam(message, title ?? "提示");
        await UISystem.Open<Alert>(UIName.ALERT, param);
    }

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    public async void ShowConfirm(string message, System.Action onConfirm, System.Action onCancel = null, string title = "确认")
    {
        var param = new AlertParam(message, title, onConfirm, onCancel);
        await UISystem.Open<Alert>(UIName.ALERT, param);
    }

    /// <summary>
    /// 关闭所有面板
    /// </summary>
    public async System.Threading.Tasks.Task CloseAllPanels()
    {
        // 关闭所有可能打开的面板
        var panelsToClose = new[]
        {
            UIName.GAME_PANEL,
            UIName.WORK_PANEL,
            UIName.BLACK_MARKET_PANEL,
            UIName.PRIVILEGE_PANEL,
            UIName.PROMOTION_PANEL,
            UIName.ACHIEVEMENT_PANEL,
            UIName.SETTINGS_PANEL,
            UIName.RANKING_PANEL,
            UIName.PAUSE_POPUP,
            UIName.BLIND_BOX_RESULT_POPUP,
            UIName.WORK_RESULT_POPUP,
            UIName.EVENT_DIALOG,
            UIName.GAME_OVER_PANEL
        };

        foreach (var panelName in panelsToClose)
        {
            try
            {
                await UISystem.Close(panelName);
            }
            catch
            {
                // 忽略关闭失败的面板
            }
        }
    }

    /// <summary>
    /// 游戏结束事件处理
    /// </summary>
    private void OnGameEnded(GameEndType endType, string message)
    {
        ShowGameOverPanel(endType, message);
    }

    /// <summary>
    /// 事件触发处理
    /// </summary>
    private void OnEventTriggered(GameEvent gameEvent)
    {
        ShowEventDialog(gameEvent);
    }

    /// <summary>
    /// 退出游戏
    /// </summary>
    public void QuitGame()
    {
        // 保存游戏
        if (gameLogic != null)
        {
            gameLogic.SaveGame();
        }

        // 退出应用
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }

    private void OnDestroy()
    {
        // 取消事件订阅
        if (gameLogic != null && gameLogic.gameFlowController != null)
        {
            gameLogic.gameFlowController.OnGameEnded -= OnGameEnded;
        }

        if (gameLogic != null && gameLogic.eventManager != null)
        {
            gameLogic.eventManager.OnEventTriggered -= OnEventTriggered;
        }
    }

    private void Update()
    {
        // 处理快捷键
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            HandleEscapeKey();
        }

        if (showDebugInfo && Input.GetKeyDown(KeyCode.F1))
        {
            ShowDebugInfo();
        }
    }

    /// <summary>
    /// 处理ESC键
    /// </summary>
    private void HandleEscapeKey()
    {
        // 如果游戏面板打开，显示暂停菜单
        if (UISystem.IsOpen(UIName.GAME_PANEL))
        {
            PauseGame();
        }
    }

    /// <summary>
    /// 显示调试信息
    /// </summary>
    private void ShowDebugInfo()
    {
        if (gameLogic != null && gameLogic.playerData != null)
        {
            var playerData = gameLogic.playerData;
            string debugInfo = $"调试信息:\n" +
                             $"生存天数: {playerData.daysSurvived}\n" +
                             $"社会阶层: {playerData.socialClass}\n" +
                             $"信用点: {playerData.resources.credits:F0}\n" +
                             $"生命值: {playerData.attributes.health:F0}\n" +
                             $"精力值: {playerData.attributes.energy:F0}";

            ShowAlert(debugInfo, "调试信息");
        }
    }

    /// <summary>
    /// 显示消息提示
    /// </summary>
    public void ShowMessage(string message)
    {
        Debug.Log($"[UIManager] Message: {message}");
        // TODO: 实现消息提示UI
    }

    /// <summary>
    /// 显示真相发现弹窗
    /// </summary>
    public async void ShowTruthDiscovery(string title, string content, bool isKeyTruth = false)
    {
        try
        {
            var popup = await UISystem.Open<TruthDiscoveryPopup>(UIName.TRUTH_DISCOVERY_POPUP);
            if (popup != null)
            {
                popup.SetTruthContent(title, content, isKeyTruth);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[UIManager] 显示真相发现弹窗失败: {e.Message}");
        }
    }

    /// <summary>
    /// 显示功能解锁弹窗
    /// </summary>
    public async void ShowFeatureUnlock(string featureId)
    {
        try
        {
            var popup = await UISystem.Open<FeatureUnlockPopup>(UIName.FEATURE_UNLOCK_POPUP);
            if (popup != null)
            {
                popup.SetUnlockContent(featureId, "Meta Feature Unlocked!");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[UIManager] 显示功能解锁弹窗失败: {e.Message}");
        }
    }

    /// <summary>
    /// 显示元游戏进度面板
    /// </summary>
    public async void ShowMetaGamePanel()
    {
        try
        {
            await UISystem.Open<MetaGamePanel>(UIName.META_GAME_PANEL);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[UIManager] 显示元游戏面板失败: {e.Message}");
        }
    }

    public void NextDay()
    {
        gameLogic.gameFlowController.NextDay();
    }
}
