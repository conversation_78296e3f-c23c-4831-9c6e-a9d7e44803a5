using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using System.Collections.Generic;
using BoxOfFate.Game.Core;

/// <summary>
/// 黑市面板 - 显示黑市商品和交易
/// </summary>
public class BlackMarketPanel : UIBase
{
    [Header("UI组件")]
    public Button closeButton;
    public TextMeshProUGUI titleText;
    public TextMeshProUGUI accessStatusText;
    public TextMeshProUGUI riskLevelText;
    public TextMeshProUGUI reputationText;

    [Header("商品列表")]
    public Transform itemListContainer;
    public GameObject itemPrefab;
    public ScrollRect itemScrollRect;

    [Header("商品详情")]
    public TextMeshProUGUI itemNameText;
    public TextMeshProUGUI itemDescriptionText;
    public TextMeshProUGUI itemPriceText;
    public TextMeshProUGUI itemEffectsText;
    public Button buyButton;
    public Image itemIcon;

    [Header("商贩信息")]
    public TextMeshProUGUI merchantNameText;
    public TextMeshProUGUI merchantDescriptionText;
    public Image merchantIcon;

    private GameLogic gameLogic;
    private BlackMarketAccess marketAccess;
    private BlackMarketListing selectedListing;
    private List<GameObject> itemObjects = new List<GameObject>();

    public override void OnInit()
    {
        gameLogic = GameLogic.Instance;
        InitializeUIReferences();
        BindButtonEvents();
    }

    public override void OnShow()
    {
        AccessBlackMarket();
    }

    /// <summary>
    /// 初始化UI组件引用
    /// </summary>
    private void InitializeUIReferences()
    {
        closeButton = transform.Get<Button>("Header/CloseBtn");
        titleText = transform.Get<TextMeshProUGUI>("Header/Title");
        accessStatusText = transform.Get<TextMeshProUGUI>("Status/AccessStatus");
        riskLevelText = transform.Get<TextMeshProUGUI>("Status/RiskLevel");
        reputationText = transform.Get<TextMeshProUGUI>("Status/Reputation");

        itemListContainer = transform.Get<Transform>("Content/ItemList/Viewport/Content");
        itemScrollRect = transform.Get<ScrollRect>("Content/ItemList");

        itemNameText = transform.Get<TextMeshProUGUI>("Content/ItemDetails/ItemName");
        itemDescriptionText = transform.Get<TextMeshProUGUI>("Content/ItemDetails/Description");
        itemPriceText = transform.Get<TextMeshProUGUI>("Content/ItemDetails/Price");
        itemEffectsText = transform.Get<TextMeshProUGUI>("Content/ItemDetails/Effects");
        buyButton = transform.Get<Button>("Content/ItemDetails/BuyBtn");
        itemIcon = transform.Get<Image>("Content/ItemDetails/Icon");

        merchantNameText = transform.Get<TextMeshProUGUI>("Content/MerchantInfo/Name");
        merchantDescriptionText = transform.Get<TextMeshProUGUI>("Content/MerchantInfo/Description");
        merchantIcon = transform.Get<Image>("Content/MerchantInfo/Icon");
    }

    /// <summary>
    /// 绑定按钮事件
    /// </summary>
    private void BindButtonEvents()
    {
        closeButton?.onClick.AddListener(OnCloseClicked);
        buyButton?.onClick.AddListener(OnBuyClicked);
    }

    /// <summary>
    /// 访问黑市
    /// </summary>
    private void AccessBlackMarket()
    {
        if (gameLogic == null) return;

        marketAccess = gameLogic.AccessBlackMarket();
        
        if (marketAccess.canAccess)
        {
            DisplayMarketInfo();
            DisplayItemList();
        }
        else
        {
            DisplayAccessDenied();
        }
    }

    /// <summary>
    /// 显示市场信息
    /// </summary>
    private void DisplayMarketInfo()
    {
        if (accessStatusText != null)
            accessStatusText.text = "访问状态: <color=#00FF00>已连接</color>";

        if (riskLevelText != null)
        {
            string riskColor = GetRiskColor(marketAccess.currentRiskLevel);
            riskLevelText.text = $"风险等级: <color={riskColor}>{GetRiskLevelText(marketAccess.currentRiskLevel)}</color>";
        }

        if (reputationText != null)
            reputationText.text = $"声誉: {marketAccess.playerReputation:F0}";
    }

    /// <summary>
    /// 显示访问被拒
    /// </summary>
    private void DisplayAccessDenied()
    {
        if (accessStatusText != null)
            accessStatusText.text = $"访问状态: <color=#FF0000>被拒绝</color>";

        if (titleText != null)
            titleText.text = "黑市 - 访问被拒";

        // 显示拒绝原因
        if (itemNameText != null)
            itemNameText.text = "无法访问";

        if (itemDescriptionText != null)
            itemDescriptionText.text = marketAccess.reason;

        // 隐藏其他UI元素
        if (itemListContainer != null)
            itemListContainer.gameObject.SetActive(false);

        if (buyButton != null)
            buyButton.gameObject.SetActive(false);
    }

    /// <summary>
    /// 显示商品列表
    /// </summary>
    private void DisplayItemList()
    {
        // 清除现有商品
        foreach (var item in itemObjects)
        {
            if (item != null) DestroyImmediate(item);
        }
        itemObjects.Clear();

        // 创建商品项
        foreach (var listing in marketAccess.availableListings)
        {
            CreateItemUI(listing);
        }
    }

    /// <summary>
    /// 创建商品UI
    /// </summary>
    private void CreateItemUI(BlackMarketListing listing)
    {
        GameObject itemObj = CreateItemObject(listing.item.name, itemListContainer);
        itemObjects.Add(itemObj);

        // 设置商品信息
        var nameText = itemObj.transform.Get<TextMeshProUGUI>("ItemName");
        var priceText = itemObj.transform.Get<TextMeshProUGUI>("Price");
        var merchantText = itemObj.transform.Get<TextMeshProUGUI>("Merchant");
        var button = itemObj.GetComponent<Button>();

        if (nameText != null)
            nameText.text = listing.item.name;

        if (priceText != null)
            priceText.text = $"{listing.price:F0} 信用点";

        if (merchantText != null)
            merchantText.text = listing.merchant.name;

        if (button != null)
        {
            button.onClick.AddListener(() => OnItemSelected(listing));
            button.interactable = CanAfford(listing);
        }

        // 设置颜色
        var image = itemObj.GetComponent<Image>();
        if (image != null)
        {
            image.color = CanAfford(listing) ? 
                new Color(0.3f, 0.2f, 0.5f, 0.8f) : 
                new Color(0.3f, 0.3f, 0.3f, 0.5f);
        }
    }

    /// <summary>
    /// 创建商品对象
    /// </summary>
    private GameObject CreateItemObject(string name, Transform parent)
    {
        GameObject item = new GameObject(name);
        item.transform.SetParent(parent, false);

        RectTransform rect = item.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 80);
        
        Image image = item.AddComponent<Image>();
        Button button = item.AddComponent<Button>();
        
        VerticalLayoutGroup layout = item.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(10, 10, 5, 5);
        layout.spacing = 5;

        // 商品名称
        GameObject nameTextGO = new GameObject("ItemName");
        nameTextGO.transform.SetParent(item.transform, false);
        TextMeshProUGUI nameText = nameTextGO.AddComponent<TextMeshProUGUI>();
        nameText.fontSize = 16;
        nameText.color = Color.white;

        // 价格
        GameObject priceTextGO = new GameObject("Price");
        priceTextGO.transform.SetParent(item.transform, false);
        TextMeshProUGUI priceText = priceTextGO.AddComponent<TextMeshProUGUI>();
        priceText.fontSize = 14;
        priceText.color = Color.yellow;

        // 商贩
        GameObject merchantTextGO = new GameObject("Merchant");
        merchantTextGO.transform.SetParent(item.transform, false);
        TextMeshProUGUI merchantText = merchantTextGO.AddComponent<TextMeshProUGUI>();
        merchantText.fontSize = 12;
        merchantText.color = Color.gray;

        return item;
    }

    /// <summary>
    /// 商品选择事件
    /// </summary>
    private void OnItemSelected(BlackMarketListing listing)
    {
        selectedListing = listing;
        DisplayItemDetails(listing);
        DisplayMerchantInfo(listing.merchant);
    }

    /// <summary>
    /// 显示商品详情
    /// </summary>
    private void DisplayItemDetails(BlackMarketListing listing)
    {
        var item = listing.item;

        if (itemNameText != null)
            itemNameText.text = item.name;

        if (itemDescriptionText != null)
            itemDescriptionText.text = item.description;

        if (itemPriceText != null)
            itemPriceText.text = $"价格: {listing.price:F0} 信用点";

        if (itemEffectsText != null)
            itemEffectsText.text = GetItemEffectsText(item);

        if (buyButton != null)
            buyButton.interactable = CanAfford(listing);

        // 设置商品图标颜色
        if (itemIcon != null)
        {
            itemIcon.color = GetItemTypeColor(item.contentType);
        }
    }

    /// <summary>
    /// 显示商贩信息
    /// </summary>
    private void DisplayMerchantInfo(BlackMarketMerchant merchant)
    {
        if (merchantNameText != null)
            merchantNameText.text = merchant.name;

        if (merchantDescriptionText != null)
            merchantDescriptionText.text = merchant.description;

        if (merchantIcon != null)
            merchantIcon.color = GetMerchantTypeColor(merchant.type);
    }

    /// <summary>
    /// 检查是否买得起
    /// </summary>
    private bool CanAfford(BlackMarketListing listing)
    {
        if (gameLogic?.playerData == null) return false;
        return gameLogic.playerData.resources.credits >= listing.price;
    }

    /// <summary>
    /// 购买按钮点击
    /// </summary>
    private void OnBuyClicked()
    {
        if (selectedListing == null || gameLogic == null) return;

        // 这里应该调用黑市系统的购买方法
        // 暂时简化处理
        var playerData = gameLogic.playerData;
        if (playerData.resources.credits >= selectedListing.price)
        {
            playerData.resources.ConsumeResource(ResourceType.Credits, selectedListing.price);
            
            // 应用物品效果
            ApplyItemEffects(selectedListing.item);
            
            Debug.Log($"购买成功: {selectedListing.item.name}");
            
            // 刷新界面
            AccessBlackMarket();
        }
    }

    /// <summary>
    /// 应用物品效果
    /// </summary>
    private void ApplyItemEffects(BlindBoxItem item)
    {
        var playerData = gameLogic.playerData;
        
        foreach (GameEffect effect in item.effects)
        {
            effect.Apply(playerData);
        }
    }

    /// <summary>
    /// 关闭按钮点击
    /// </summary>
    private async void OnCloseClicked()
    {
        await UISystem.Close(UIName.BLACK_MARKET_PANEL);
    }

    // 辅助方法
    private string GetRiskColor(float riskLevel)
    {
        if (riskLevel < 0.3f) return "#00FF00";      // 绿色
        if (riskLevel < 0.6f) return "#FFFF00";      // 黄色
        if (riskLevel < 0.8f) return "#FF8800";      // 橙色
        return "#FF0000";                            // 红色
    }

    private string GetRiskLevelText(float riskLevel)
    {
        if (riskLevel < 0.3f) return "低";
        if (riskLevel < 0.6f) return "中";
        if (riskLevel < 0.8f) return "高";
        return "极高";
    }

    private string GetItemEffectsText(BlindBoxItem item)
    {
        string effects = "效果:\n";
        foreach (GameEffect effect in item.effects)
        {
            effects += effect.GetDisplayText() + "\n";
        }
        return effects;
    }

    private Color GetItemTypeColor(BlindBoxContentType contentType)
    {
        return contentType switch
        {
            BlindBoxContentType.Medicine => Color.blue,
            BlindBoxContentType.Technology => Color.magenta,
            BlindBoxContentType.Currency => Color.yellow,
            BlindBoxContentType.Memory => Color.cyan,
            _ => Color.white
        };
    }

    private Color GetMerchantTypeColor(BlackMarketMerchantType merchantType)
    {
        return merchantType switch
        {
            BlackMarketMerchantType.ShadowDealer => Color.gray,
            BlackMarketMerchantType.DataBroker => Color.cyan,
            BlackMarketMerchantType.MemoryTrader => Color.magenta,
            BlackMarketMerchantType.SystemHacker => Color.red,
            _ => Color.white
        };
    }
}
