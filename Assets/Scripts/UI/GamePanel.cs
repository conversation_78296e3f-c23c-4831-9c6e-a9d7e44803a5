using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using System.Collections.Generic;

/// <summary>
/// 游戏主界面 - 显示玩家状态和核心操作
/// </summary>
public class GamePanel : UIBase
{
    [Header("顶部状态栏")]
    public TextMeshProUGUI daysSurvivedText;
    public TextMeshProUGUI socialClassText;
    public TextMeshProUGUI creditsText;
    public TextMeshProUGUI healthText;
    public TextMeshProUGUI energyText;
    public Button SettingsButton;

    [Header("左侧属性面板")]
    public TextMeshProUGUI luckText;
    public TextMeshProUGUI socialText;
    public TextMeshProUGUI dependenceText;
    public TextMeshProUGUI cognitionText;
    public TextMeshProUGUI pollutionText;
    public TextMeshProUGUI humanityText;
    public TextMeshProUGUI moralityText;

    [Header("盲盒区域")]
    public Button basicBoxButton;
    public Button lifeBoxButton;
    public Button premiumBoxButton;
    public Button mysteryBoxButton;

    [Header("底部功能按钮")]
    public Button workButton;
    public Button blackMarketButton;
    public Button privilegeButton;
    public Button promotionButton;
    public Button achievementButton;
    public Button nextDayButton;

    [Header("右侧信息面板")]
    public TextMeshProUGUI gameLogText;
    public ScrollRect logScrollRect;

    private GameLogic gameLogic;
    private List<string> gameLogs = new List<string>();

    public override void OnInit()
    {
        gameLogic = GameLogic.Instance;
        
        // 初始化UI组件引用
        InitializeUIReferences();
        
        // 绑定按钮事件
        BindButtonEvents();
        
        // 订阅游戏事件
        SubscribeToGameEvents();
        
        // 初始化显示
        RefreshUI();
    }

    public override void OnShow()
    {
        RefreshUI();
        AddGameLog("游戏界面已打开");
    }

    /// <summary>
    /// 初始化UI组件引用
    /// </summary>
    private void InitializeUIReferences()
    {
        // 顶部状态栏
        daysSurvivedText = transform.Get<TextMeshProUGUI>("TopStatusBar/DaysSurvived");
        socialClassText = transform.Get<TextMeshProUGUI>("TopStatusBar/SocialClass");
        creditsText = transform.Get<TextMeshProUGUI>("TopStatusBar/Credits");
        healthText = transform.Get<TextMeshProUGUI>("TopStatusBar/Health");
        energyText = transform.Get<TextMeshProUGUI>("TopStatusBar/Energy");
        SettingsButton = transform.Get<Button>("TopStatusBar/BtnSettings");

        // 左侧属性面板
        luckText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Luck/Value");
        socialText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Social/Value");
        dependenceText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Dependence/Value");
        cognitionText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Cognition/Value");
        pollutionText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Pollution/Value");
        humanityText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Humanity/Value");
        moralityText = transform.Get<TextMeshProUGUI>("AttributePanel/AttributeContainer/Morality/Value");

        // 盲盒按钮
        basicBoxButton = transform.Get<Button>("Central/BlindBoxArea/BasicBox");
        lifeBoxButton = transform.Get<Button>("Central/BlindBoxArea/LifeBox");
        premiumBoxButton = transform.Get<Button>("Central/BlindBoxArea/PremiumBox");
        mysteryBoxButton = transform.Get<Button>("Central/BlindBoxArea/MysteryBox");

        // 底部功能按钮
        workButton = transform.Get<Button>("Bottom/BtnWork");
        blackMarketButton = transform.Get<Button>("Bottom/BtnBlackMarket");
        privilegeButton = transform.Get<Button>("Bottom/BtnPrivilege");
        promotionButton = transform.Get<Button>("Bottom/BtnPromotion");
        achievementButton = transform.Get<Button>("Bottom/BtnAchievement");
        nextDayButton = transform.Get<Button>("Bottom/BtnNextDay");

        // 右侧信息面板
        gameLogText = transform.Get<TextMeshProUGUI>("RightInfoPanel/LogContainer/Viewport/Content/LogText");
        logScrollRect = transform.Get<ScrollRect>("RightInfoPanel/LogContainer");
    }

    /// <summary>
    /// 绑定按钮事件
    /// </summary>
    private void BindButtonEvents()
    {
        SettingsButton?.onClick.AddListener(OnSettingsClicked);
        // 盲盒按钮
        basicBoxButton?.onClick.AddListener(() => OnBlindBoxClicked(BlindBoxType.Basic));
        lifeBoxButton?.onClick.AddListener(() => OnBlindBoxClicked(BlindBoxType.Life));
        premiumBoxButton?.onClick.AddListener(() => OnBlindBoxClicked(BlindBoxType.Premium));
        mysteryBoxButton?.onClick.AddListener(() => OnBlindBoxClicked(BlindBoxType.Mystery));

        // 功能按钮
        workButton?.onClick.AddListener(OnWorkClicked);
        blackMarketButton?.onClick.AddListener(OnBlackMarketClicked);
        privilegeButton?.onClick.AddListener(OnPrivilegeClicked);
        promotionButton?.onClick.AddListener(OnPromotionClicked);
        achievementButton?.onClick.AddListener(OnAchievementClicked);
        nextDayButton?.onClick.AddListener(OnNextDayClicked);
    }

    /// <summary>
    /// 订阅游戏事件
    /// </summary>
    private void SubscribeToGameEvents()
    {
        if (gameLogic?.blindBoxSystem != null)
        {
            gameLogic.blindBoxSystem.OnBoxOpened += OnBoxOpenedEvent;
        }
    }

    /// <summary>
    /// 刷新UI显示
    /// </summary>
    private void RefreshUI()
    {
        if (gameLogic?.playerData == null) return;

        var playerData = gameLogic.playerData;

        // 更新顶部状态栏
        UpdateTopStatusBar(playerData);
        
        // 更新属性面板
        UpdateAttributePanel(playerData);
        
        // 更新按钮状态
        UpdateButtonStates(playerData);
    }

    /// <summary>
    /// 更新顶部状态栏
    /// </summary>
    private void UpdateTopStatusBar(PlayerData playerData)
    {
        if (daysSurvivedText != null)
            daysSurvivedText.text = $"第{playerData.daysSurvived}天";
        
        if (socialClassText != null)
            socialClassText.text = GetSocialClassName(playerData.socialClass);
        
        if (creditsText != null)
            creditsText.text = $"信用点: {playerData.resources.credits:F0}";
        
        if (healthText != null)
            healthText.text = $"生命: {playerData.attributes.health:F0}/100";
        
        if (energyText != null)
            energyText.text = $"精力: {playerData.attributes.energy:F0}/100";
    }

    /// <summary>
    /// 更新属性面板
    /// </summary>
    private void UpdateAttributePanel(PlayerData playerData)
    {
        if (luckText != null)
            luckText.text = playerData.attributes.luck.ToString("F1");
        
        if (socialText != null)
            socialText.text = playerData.attributes.social.ToString("F0");
        
        if (dependenceText != null)
            dependenceText.text = playerData.attributes.dependence.ToString("F0");
        
        if (cognitionText != null)
            cognitionText.text = playerData.attributes.cognition.ToString("F0");
        
        if (pollutionText != null)
            pollutionText.text = playerData.attributes.pollution.ToString("F0");
        
        if (humanityText != null)
            humanityText.text = playerData.attributes.humanity.ToString("F0");
        
        if (moralityText != null)
            moralityText.text = playerData.attributes.morality.ToString("F0");
    }

    /// <summary>
    /// 更新按钮状态
    /// </summary>
    private void UpdateButtonStates(PlayerData playerData)
    {
        // 根据玩家资源和状态更新按钮可用性
        if (basicBoxButton != null)
            basicBoxButton.interactable = playerData.resources.credits >= 50f;
        
        if (lifeBoxButton != null)
            lifeBoxButton.interactable = playerData.resources.credits >= 100f;
        
        if (premiumBoxButton != null)
            premiumBoxButton.interactable = playerData.resources.credits >= 500f;
        
        if (mysteryBoxButton != null)
            mysteryBoxButton.interactable = playerData.resources.diamond >= 1f;

        // 功能按钮状态
        if (workButton != null)
            workButton.interactable = playerData.attributes.energy >= 20f;
        
        if (blackMarketButton != null)
            blackMarketButton.interactable = playerData.attributes.cognition >= 30f;
        
        if (privilegeButton != null)
            privilegeButton.interactable = playerData.socialClass != SocialClass.Parasite;
        
        if (promotionButton != null)
            promotionButton.interactable = CanPromote(playerData);
    }

    /// <summary>
    /// 检查是否可以晋升
    /// </summary>
    private bool CanPromote(PlayerData playerData)
    {
        if (playerData.socialClass == SocialClass.Chosen) return false;
        
        var targetClass = playerData.socialClass == SocialClass.Parasite ? 
            SocialClass.Worker : SocialClass.Chosen;
        
        return gameLogic.socialClassManager?.CanPromoteToClass(playerData, targetClass) ?? false;
    }

    /// <summary>
    /// 获取社会阶层名称
    /// </summary>
    private string GetSocialClassName(SocialClass socialClass)
    {
        return socialClass switch
        {
            SocialClass.Parasite => "蛀虫",
            SocialClass.Worker => "工蜂",
            SocialClass.Chosen => "神选者",
            _ => "未知"
        };
    }

    /// <summary>
    /// 添加游戏日志
    /// </summary>
    private void AddGameLog(string message)
    {
        gameLogs.Add($"[{System.DateTime.Now:HH:mm:ss}] {message}");
        
        // 限制日志数量
        if (gameLogs.Count > 50)
        {
            gameLogs.RemoveAt(0);
        }
        
        // 更新显示
        if (gameLogText != null)
        {
            gameLogText.text = string.Join("\n", gameLogs);
        }
        
        // 滚动到底部
        if (logScrollRect != null)
        {
            Canvas.ForceUpdateCanvases();
            logScrollRect.verticalNormalizedPosition = 0f;
        }
    }

    private void OnSettingsClicked()
    {
        UIManager.Instance.ShowSettings();
    }

    // 按钮事件处理
    private void OnBlindBoxClicked(BlindBoxType boxType)
    {
        if (gameLogic == null) return;

        var result = gameLogic.OpenBlindBox(boxType);
        if (result != null)
        {
            // 显示开盒结果弹窗
            UIManager.Instance.ShowBlindBoxResult(result);

            AddGameLog($"开启{boxType}盲盒，获得：{result.item.name}");
            RefreshUI();
        }
    }

    private void OnWorkClicked()
    {
        UIManager.Instance.ShowWorkPanel();
    }

    private void OnBlackMarketClicked()
    {
        UIManager.Instance.ShowBlackMarketPanel();
    }

    private void OnPrivilegeClicked()
    {
        UIManager.Instance.ShowPrivilegePanel();
    }

    private void OnPromotionClicked()
    {
        UIManager.Instance.ShowPromotionPanel();
    }

    private void OnAchievementClicked()
    {
        UIManager.Instance.ShowAchievementPanel();
    }

    private void OnNextDayClicked()
    {
        UIManager.Instance.NextDay();
    }

    // 游戏事件处理
    private void OnBoxOpenedEvent(BlindBoxResult result)
    {
        RefreshUI();
    }

    private void Update()
    {
        // 定期刷新UI（每秒一次）
        if (Time.time % 1f < Time.deltaTime)
        {
            RefreshUI();
        }
    }

    public override void OnClose()
    {
        // 取消事件订阅
        if (gameLogic?.blindBoxSystem != null)
        {
            gameLogic.blindBoxSystem.OnBoxOpened -= OnBoxOpenedEvent;
        }
    }
}

/// <summary>
/// 盲盒结果弹窗参数
/// </summary>
public class BlindBoxResultParam
{
    public BlindBoxResult result;

    public BlindBoxResultParam(BlindBoxResult result)
    {
        this.result = result;
    }
}
