using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using System.Threading.Tasks;

namespace BoxOfFate.UI
{
    /// <summary>
    /// 元游戏进度面板 - 显示跨游戏局的进度信息
    /// </summary>
    public class MetaGamePanel : UIBase
    {
        [Header("基础信息")]
        public TextMeshProUGUI gamesPlayedText;
        public TextMeshProUGUI totalPlayTimeText;
        public TextMeshProUGUI totalBoxesText;
        public TextMeshProUGUI maxSurvivalText;

        [Header("觉醒系统")]
        public Slider awakeningSlider;
        public TextMeshProUGUI awakeningLevelText;
        public TextMeshProUGUI currentPhaseText;
        public Image phaseIcon;
        public Color[] phaseColors = new Color[5];

        [Header("真相发现")]
        public Transform truthContainer;
        public GameObject truthItemPrefab;
        public TextMeshProUGUI truthCountText;

        [Header("功能解锁")]
        public Transform featureContainer;
        public GameObject featureItemPrefab;
        public TextMeshProUGUI featureCountText;

        [Header("结局记录")]
        public Transform endingContainer;
        public GameObject endingItemPrefab;
        public TextMeshProUGUI endingCountText;

        [Header("统计图表")]
        public Transform statisticsContainer;
        public TextMeshProUGUI fraudDetectionText;
        public TextMeshProUGUI systemUnderstandingText;

        [Header("按钮")]
        public Button closeButton;
        public Button resetProgressButton;
        public Button exportDataButton;

        private MetaGameProgress currentProgress;

        public override void OnOpen(object param = null)
        {
            base.OnOpen();
            
            if (MetaGameSystem.Instance != null)
            {
                currentProgress = MetaGameSystem.Instance.GetProgress();
                RefreshUI();
            }

            // 绑定事件
            closeButton?.onClick.AddListener(OnCloseClicked);
            resetProgressButton?.onClick.AddListener(OnResetProgressClicked);
            exportDataButton?.onClick.AddListener(OnExportDataClicked);
        }

        public override void OnClose()
        {
            // 解绑事件
            closeButton?.onClick.RemoveAllListeners();
            resetProgressButton?.onClick.RemoveAllListeners();
            exportDataButton?.onClick.RemoveAllListeners();
            
            base.OnClose();
        }

        /// <summary>
        /// 刷新UI显示
        /// </summary>
        public void RefreshUI()
        {
            if (currentProgress == null) return;

            RefreshBasicInfo();
            RefreshAwakeningInfo();
            RefreshTruthList();
            RefreshFeatureList();
            RefreshEndingList();
            RefreshStatistics();
        }

        /// <summary>
        /// 刷新基础信息
        /// </summary>
        private void RefreshBasicInfo()
        {
            if (gamesPlayedText != null)
                gamesPlayedText.text = $"游戏次数: {currentProgress.totalGamesPlayed}";

            if (totalPlayTimeText != null)
            {
                var hours = Mathf.FloorToInt(currentProgress.totalPlayTimeHours);
                var minutes = Mathf.FloorToInt((currentProgress.totalPlayTimeHours - hours) * 60);
                totalPlayTimeText.text = $"总游戏时间: {hours}小时{minutes}分钟";
            }

            if (totalBoxesText != null)
                totalBoxesText.text = $"总开盒数: {currentProgress.totalLifetimeBoxes}";

            if (maxSurvivalText != null)
                maxSurvivalText.text = $"最长生存: {currentProgress.maxDaysSurvived}天";
        }

        /// <summary>
        /// 刷新觉醒信息
        /// </summary>
        private void RefreshAwakeningInfo()
        {
            if (awakeningSlider != null)
            {
                awakeningSlider.value = currentProgress.awakeningLevel / 100f;
            }

            if (awakeningLevelText != null)
            {
                awakeningLevelText.text = $"{currentProgress.awakeningLevel:F1}%";
            }

            var phase = currentProgress.GetCurrentPhase();
            if (currentPhaseText != null)
            {
                currentPhaseText.text = GetPhaseDisplayName(phase);
            }

            if (phaseIcon != null && phaseColors.Length >= 5)
            {
                phaseIcon.color = phaseColors[(int)phase];
            }
        }

        /// <summary>
        /// 刷新真相列表
        /// </summary>
        private void RefreshTruthList()
        {
            if (truthContainer == null || truthItemPrefab == null) return;

            // 清空现有项目
            foreach (Transform child in truthContainer)
            {
                Destroy(child.gameObject);
            }

            // 创建真相项目
            var truthConfigs = MetaGameSystem.Instance.truthConfigs;
            foreach (var truthId in currentProgress.discoveredTruths)
            {
                var config = truthConfigs.Find(t => t.truthId == truthId);
                if (config != null)
                {
                    CreateTruthItem(config);
                }
            }

            if (truthCountText != null)
            {
                truthCountText.text = $"已发现真相: {currentProgress.discoveredTruths.Count}/{truthConfigs.Count}";
            }
        }

        /// <summary>
        /// 创建真相项目
        /// </summary>
        private void CreateTruthItem(TruthFragmentConfig config)
        {
            var item = Instantiate(truthItemPrefab, truthContainer);
            
            var titleText = item.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
            if (titleText != null)
                titleText.text = config.title;

            var contentText = item.transform.Find("Content")?.GetComponent<TextMeshProUGUI>();
            if (contentText != null)
                contentText.text = config.content;

            var keyIcon = item.transform.Find("KeyIcon");
            if (keyIcon != null)
                keyIcon.gameObject.SetActive(config.isKeyTruth);

            var awakeningText = item.transform.Find("AwakeningValue")?.GetComponent<TextMeshProUGUI>();
            if (awakeningText != null)
                awakeningText.text = $"+{config.awakeningValue:F1}";
        }

        /// <summary>
        /// 刷新功能列表
        /// </summary>
        private void RefreshFeatureList()
        {
            if (featureContainer == null || featureItemPrefab == null) return;

            // 清空现有项目
            foreach (Transform child in featureContainer)
            {
                Destroy(child.gameObject);
            }

            // 创建功能项目
            var featureConfigs = MetaGameSystem.Instance.featureConfigs;
            foreach (var config in featureConfigs)
            {
                CreateFeatureItem(config.Value, currentProgress.HasFeature(config.Key));
            }

            if (featureCountText != null)
            {
                featureCountText.text = $"已解锁功能: {currentProgress.unlockedFeatures.Count}/{featureConfigs.Count}";
            }
        }

        /// <summary>
        /// 创建功能项目
        /// </summary>
        private void CreateFeatureItem(MetaFeature config, bool isUnlocked)
        {
            var item = Instantiate(featureItemPrefab, featureContainer);
            
            var nameText = item.transform.Find("Name")?.GetComponent<TextMeshProUGUI>();
            if (nameText != null)
                nameText.text = config.name;

            var descText = item.transform.Find("Description")?.GetComponent<TextMeshProUGUI>();
            if (descText != null)
                descText.text = config.description;

            var statusIcon = item.transform.Find("StatusIcon")?.GetComponent<Image>();
            if (statusIcon != null)
            {
                statusIcon.color = isUnlocked ? Color.green : Color.gray;
            }

            var requirementText = item.transform.Find("Requirement")?.GetComponent<TextMeshProUGUI>();
            if (requirementText != null && !isUnlocked)
            {
                requirementText.text = GetRequirementText(config);
            }
            else if (requirementText != null)
            {
                requirementText.text = "已解锁";
            }

            // 设置整体透明度
            var canvasGroup = item.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
            {
                canvasGroup.alpha = isUnlocked ? 1f : 0.6f;
            }
        }

        /// <summary>
        /// 获取需求文本
        /// </summary>
        private string GetRequirementText(MetaFeature config)
        {
            var requirements = new List<string>();

            foreach (var item in config.unlockConditions)
            {
                requirements.Add(item.GetDisplayText());
            }

            return string.Join(", ", requirements);
        }

        /// <summary>
        /// 刷新结局列表
        /// </summary>
        private void RefreshEndingList()
        {
            if (endingContainer == null || endingItemPrefab == null) return;

            // 清空现有项目
            foreach (Transform child in endingContainer)
            {
                Destroy(child.gameObject);
            }

            // 创建结局项目
            foreach (var ending in currentProgress.endingCounts)
            {
                CreateEndingItem(ending.Key, ending.Value);
            }

            if (endingCountText != null)
            {
                endingCountText.text = $"达成结局: {currentProgress.endingCounts.Count}";
            }
        }

        /// <summary>
        /// 创建结局项目
        /// </summary>
        private void CreateEndingItem(string endingType, int count)
        {
            var item = Instantiate(endingItemPrefab, endingContainer);
            
            var nameText = item.transform.Find("Name")?.GetComponent<TextMeshProUGUI>();
            if (nameText != null)
                nameText.text = GetEndingDisplayName(endingType);

            var countText = item.transform.Find("Count")?.GetComponent<TextMeshProUGUI>();
            if (countText != null)
                countText.text = $"×{count}";
        }

        /// <summary>
        /// 刷新统计信息
        /// </summary>
        private void RefreshStatistics()
        {
            if (fraudDetectionText != null)
            {
                fraudDetectionText.text = $"欺诈检测: {currentProgress.manipulationDetections} 次";
            }

            if (systemUnderstandingText != null)
            {
                systemUnderstandingText.text = $"系统理解: {currentProgress.systemUnderstandingLevel:F1}%";
            }
        }

        /// <summary>
        /// 获取阶段显示名称
        /// </summary>
        private string GetPhaseDisplayName(MetaGamePhase phase)
        {
            switch (phase)
            {
                case MetaGamePhase.Innocent: return "天真期";
                case MetaGamePhase.Suspicious: return "怀疑期";
                case MetaGamePhase.Aware: return "觉醒期";
                case MetaGamePhase.Rebellious: return "反抗期";
                case MetaGamePhase.Enlightened: return "超越期";
                default: return "未知";
            }
        }

        /// <summary>
        /// 获取结局显示名称
        /// </summary>
        private string GetEndingDisplayName(string endingType)
        {
            switch (endingType)
            {
                case "Death": return "死亡";
                case "TimeUp": return "时间耗尽";
                case "SystemAssimilation": return "系统同化";
                case "Rebellion": return "反抗";
                case "Transcendence": return "超越";
                case "Sacrifice": return "牺牲";
                case "TrueEnding": return "真结局";
                default: return endingType;
            }
        }

        private async void OnCloseClicked()
        {
            await UISystem.Close(UIName.META_GAME_PANEL);
        }

        private void OnResetProgressClicked()
        {
            UIManager.Instance.ShowConfirm(
                "确定要重置所有元游戏进度吗？这将清除所有觉醒值、真相发现和功能解锁！",
                () =>
                {
                    MetaGameSystem.Instance.ResetProgress();
                    RefreshUI();
                });
        }

        private void OnExportDataClicked()
        {
            // 导出数据功能
            var json = JsonUtility.ToJson(currentProgress, true);
            GUIUtility.systemCopyBuffer = json;
            UIManager.Instance.ShowMessage("进度数据已复制到剪贴板");
        }
    }
}
