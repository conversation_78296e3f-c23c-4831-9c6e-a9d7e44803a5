using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using BoxOfFate.Game;

namespace BoxOfFate.UI
{
    /// <summary>
    /// 真相发现弹窗
    /// </summary>
    public class TruthDiscoveryPopup : UIBase
    {
        [Header("UI组件")]
        public TextMeshProUGUI titleText;
        public TextMeshProUGUI contentText;
        public Button confirmButton;
        public Image backgroundImage;
        public CanvasGroup canvasGroup;

        [<PERSON><PERSON>("动画设置")]
        public float fadeInDuration = 0.5f;
        public float displayDuration = 3f;
        public float fadeOutDuration = 0.5f;

        [Header("视觉效果")]
        public ParticleSystem discoveryEffect;
        public AudioClip discoverySound;
        public Color[] truthColors = new Color[3]; // 不同重要程度的颜色

        private string truthTitle;
        private string truthContent;
        private bool isKeyTruth;

        public override void OnOpen(object param = null)
        {
            base.OnOpen();
            
            confirmButton?.onClick.AddListener(OnConfirmClicked);
            
            // 初始化透明度
            if (canvasGroup != null)
                canvasGroup.alpha = 0f;
            
            PlayDiscoveryAnimation();
        }

        public override void OnClose()
        {
            confirmButton?.onClick.RemoveAllListeners();
            base.OnClose();
        }

        /// <summary>
        /// 设置真相内容
        /// </summary>
        public void SetTruthContent(string title, string content, bool isKey = false)
        {
            truthTitle = title;
            truthContent = content;
            isKeyTruth = isKey;

            if (titleText != null)
                titleText.text = truthTitle;

            if (contentText != null)
                contentText.text = truthContent;

            // 根据重要程度设置颜色
            if (backgroundImage != null && truthColors.Length >= 3)
            {
                int colorIndex = isKeyTruth ? 2 : 1;
                backgroundImage.color = truthColors[colorIndex];
            }
        }

        /// <summary>
        /// 播放发现动画
        /// </summary>
        private void PlayDiscoveryAnimation()
        {
            // 播放音效
            // TODO: 集成音频系统
            // if (discoverySound != null && AudioManager.Instance != null)
            // {
            //     AudioManager.Instance.PlaySFX(discoverySound);
            // }

            // 播放粒子效果
            if (discoveryEffect != null)
            {
                discoveryEffect.Play();
            }

            // 淡入动画
            if (canvasGroup != null)
            {
                canvasGroup.DOFade(1f, fadeInDuration)
                    .SetEase(Ease.OutQuart)
                    .OnComplete(() =>
                    {
                        // 自动关闭（如果没有手动关闭）
                        DOVirtual.DelayedCall(displayDuration, () =>
                        {
                            if (gameObject.activeInHierarchy)
                            {
                                OnConfirmClicked();
                            }
                        });
                    });
            }

            // 标题动画
            if (titleText != null)
            {
                titleText.transform.localScale = Vector3.zero;
                titleText.transform.DOScale(1f, fadeInDuration * 0.8f)
                    .SetEase(Ease.OutBack)
                    .SetDelay(fadeInDuration * 0.2f);
            }

            // 内容动画
            if (contentText != null)
            {
                contentText.transform.localScale = Vector3.zero;
                contentText.transform.DOScale(1f, fadeInDuration * 0.8f)
                    .SetEase(Ease.OutBack)
                    .SetDelay(fadeInDuration * 0.4f);
            }
        }

        private async void OnConfirmClicked()
        {
            // 淡出动画
            if (canvasGroup != null)
            {
                canvasGroup.DOFade(0f, fadeOutDuration)
                    .SetEase(Ease.InQuart)
                    .OnComplete(async () =>
                    {
                        await UISystem.Close(UIName.TRUTH_DISCOVERY_POPUP);
                    });
            }
            else
            {
                await UISystem.Close(UIName.TRUTH_DISCOVERY_POPUP);
            }
        }
    }

    /// <summary>
    /// 功能解锁弹窗
    /// </summary>
    public class FeatureUnlockPopup : UIBase
    {
        [Header("UI组件")]
        public TextMeshProUGUI featureNameText;
        public TextMeshProUGUI unlockMessageText;
        public Image featureIcon;
        public Button confirmButton;
        public CanvasGroup canvasGroup;

        [Header("动画设置")]
        public float animationDuration = 1f;

        [Header("视觉效果")]
        public ParticleSystem unlockEffect;
        public AudioClip unlockSound;

        private string featureId;
        private string unlockMessage;

        public override void OnOpen(object param = null)
        {
            base.OnOpen();
            
            confirmButton?.onClick.AddListener(OnConfirmClicked);
            
            if (canvasGroup != null)
                canvasGroup.alpha = 0f;
            
            PlayUnlockAnimation();
        }

        public override void OnClose()
        {
            confirmButton?.onClick.RemoveAllListeners();
            base.OnClose();
        }

        /// <summary>
        /// 设置解锁内容
        /// </summary>
        public void SetUnlockContent(string featureId, string message)
        {
            this.featureId = featureId;
            unlockMessage = message;

            // 获取功能配置
            var config = MetaGameSystem.Instance.featureConfigs[featureId];
            if (config != null)
            {
                if (featureNameText != null)
                    featureNameText.text = config.name;
            }

            if (unlockMessageText != null)
                unlockMessageText.text = unlockMessage;

            // 设置图标（如果有的话）
            SetFeatureIcon(featureId);
        }

        /// <summary>
        /// 设置功能图标
        /// </summary>
        private void SetFeatureIcon(string featureId)
        {
            if (featureIcon == null) return;

            // 根据功能ID设置不同的图标
            // 这里可以从Resources加载或使用预设的图标
            string iconPath = $"Icons/Features/{featureId}";
            var sprite = Resources.Load<Sprite>(iconPath);
            
            if (sprite != null)
            {
                featureIcon.sprite = sprite;
            }
        }

        /// <summary>
        /// 播放解锁动画
        /// </summary>
        private void PlayUnlockAnimation()
        {
            // 播放音效
            // TODO: 集成音频系统
            // if (unlockSound != null && AudioManager.Instance != null)
            // {
            //     AudioManager.Instance.PlaySFX(unlockSound);
            // }

            // 播放粒子效果
            if (unlockEffect != null)
            {
                unlockEffect.Play();
            }

            // 整体淡入
            if (canvasGroup != null)
            {
                canvasGroup.DOFade(1f, animationDuration * 0.5f)
                    .SetEase(Ease.OutQuart);
            }

            // 图标动画
            if (featureIcon != null)
            {
                featureIcon.transform.localScale = Vector3.zero;
                featureIcon.transform.DOScale(1f, animationDuration * 0.6f)
                    .SetEase(Ease.OutBounce)
                    .SetDelay(animationDuration * 0.2f);
            }

            // 文字动画
            if (featureNameText != null)
            {
                featureNameText.transform.localPosition += Vector3.up * 50f;
                featureNameText.transform.DOLocalMoveY(
                    featureNameText.transform.localPosition.y - 50f, 
                    animationDuration * 0.8f)
                    .SetEase(Ease.OutBack)
                    .SetDelay(animationDuration * 0.3f);
            }

            if (unlockMessageText != null)
            {
                unlockMessageText.transform.localPosition += Vector3.up * 30f;
                unlockMessageText.transform.DOLocalMoveY(
                    unlockMessageText.transform.localPosition.y - 30f, 
                    animationDuration * 0.8f)
                    .SetEase(Ease.OutBack)
                    .SetDelay(animationDuration * 0.5f);
            }
        }

        private async void OnConfirmClicked()
        {
            await UISystem.Close(UIName.TRUTH_DISCOVERY_POPUP);
        }
    }
}
