using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using DG.Tweening;

/// <summary>
/// 盲盒结果弹窗 - 显示开盒结果和物品效果
/// </summary>
public class BlindBoxResultPopup : UIBase
{
    [Header("UI组件")]
    public Image itemIcon;
    public TextMeshProUGUI itemNameText;
    public TextMeshProUGUI itemDescriptionText;
    public TextMeshProUGUI itemValueText;
    public TextMeshProUGUI effectsText;
    public Button confirmButton;
    public Button useButton;
    public Image backgroundImage;

    [Header("效果显示")]
    public GameObject positiveEffectPrefab;
    public GameObject negativeEffectPrefab;
    public Transform effectsContainer;

    private BlindBoxResultParam param;
    private BlindBoxResult result;

    public override void OnInit()
    {
        // 初始化UI组件引用
        InitializeUIReferences();
        
        // 绑定按钮事件
        BindButtonEvents();
    }

    public override void OnOpen(object param)
    {
        this.param = param as BlindBoxResultParam;
        if (this.param == null)
        {
            Debug.LogError("BlindBoxResultPopup: 参数类型错误");
            return;
        }

        result = this.param.result;
        
        // 显示开盒结果
        DisplayResult();
        
        // 播放开盒动画
        PlayOpenAnimation();
    }

    public override void OnClose()
    {
        confirmButton?.onClick.RemoveAllListeners();
        useButton?.onClick.RemoveAllListeners();
        base.OnClose();
    }

    /// <summary>
    /// 初始化UI组件引用
    /// </summary>
    private void InitializeUIReferences()
    {
        itemIcon = transform.Get<Image>("Content/ItemIcon");
        itemNameText = transform.Get<TextMeshProUGUI>("Content/ItemName");
        itemDescriptionText = transform.Get<TextMeshProUGUI>("Content/ItemDescription");
        itemValueText = transform.Get<TextMeshProUGUI>("Content/ItemDisplay/Value");
        effectsText = transform.Get<TextMeshProUGUI>("Content/Effects/EffectsText");
        confirmButton = transform.Get<Button>("Content/ConfirmBtn");
        useButton = transform.Get<Button>("Buttons/UseBtn");
        backgroundImage = transform.Get<Image>("Background");
        effectsContainer = transform.Get<Transform>("Content/Effects/EffectsContainer");
    }

    /// <summary>
    /// 绑定按钮事件
    /// </summary>
    private void BindButtonEvents()
    {
        confirmButton?.onClick.AddListener(OnConfirmClicked);
        useButton?.onClick.AddListener(OnUseClicked);
    }

    /// <summary>
    /// 显示开盒结果
    /// </summary>
    private void DisplayResult()
    {
        if (result == null) return;

        var item = result.item;
        
        // 设置物品信息
        if (itemNameText != null)
            itemNameText.text = item.name;
        
        if (itemDescriptionText != null)
            itemDescriptionText.text = item.description;
        
        if (itemValueText != null)
        {
            string valueColor = item.value >= 0 ? "#00FF00" : "#FF0000";
            itemValueText.text = $"<color={valueColor}>价值: {item.value:F0}</color>";
        }

        // 设置背景颜色（根据物品类型和价值）
        SetBackgroundColor(item);
        
        // 设置物品图标
        SetItemIcon(item);
        
        // 显示物品效果
        DisplayItemEffects(item);
        
        // 设置按钮状态
        SetButtonStates(item);
    }

    /// <summary>
    /// 设置背景颜色
    /// </summary>
    private void SetBackgroundColor(BlindBoxItem item)
    {
        if (backgroundImage == null) return;

        Color bgColor = item.contentType switch
        {
            BlindBoxContentType.Food => new Color(0.2f, 0.6f, 0.2f, 0.8f),      // 绿色
            BlindBoxContentType.Medicine => new Color(0.2f, 0.2f, 0.8f, 0.8f),  // 蓝色
            BlindBoxContentType.Technology => new Color(0.6f, 0.2f, 0.6f, 0.8f), // 紫色
            BlindBoxContentType.Currency => new Color(0.8f, 0.6f, 0.2f, 0.8f),   // 金色
            BlindBoxContentType.Talent => new Color(0.8f, 0.2f, 0.2f, 0.8f),     // 红色
            BlindBoxContentType.Trap => new Color(0.4f, 0.1f, 0.1f, 0.8f),       // 暗红
            BlindBoxContentType.Virus => new Color(0.1f, 0.1f, 0.1f, 0.9f),      // 黑色
            _ => new Color(0.3f, 0.3f, 0.3f, 0.8f)                               // 灰色
        };

        // 如果是负面物品，调暗颜色
        if (!item.isPositive)
        {
            bgColor = Color.Lerp(bgColor, Color.black, 0.3f);
        }

        backgroundImage.color = bgColor;
    }

    /// <summary>
    /// 设置物品图标
    /// </summary>
    private void SetItemIcon(BlindBoxItem item)
    {
        if (itemIcon == null) return;

        // 这里可以根据物品类型设置不同的图标
        // 暂时使用颜色来区分
        Color iconColor = item.contentType switch
        {
            BlindBoxContentType.Food => Color.green,
            BlindBoxContentType.Medicine => Color.blue,
            BlindBoxContentType.Technology => Color.magenta,
            BlindBoxContentType.Currency => Color.yellow,
            BlindBoxContentType.Talent => Color.red,
            BlindBoxContentType.Trap => new Color(0.5f, 0.2f, 0.2f),
            BlindBoxContentType.Virus => Color.black,
            _ => Color.white
        };

        itemIcon.color = iconColor;
    }

    /// <summary>
    /// 显示物品效果
    /// </summary>
    private void DisplayItemEffects(BlindBoxItem item)
    {
        if (effectsText == null) return;

        string effectsString = "效果:\n";
        
        foreach (var effect in item.effects)
        {
            string effectColor = effect.value >= 0 ? "#00FF00" : "#FF0000";
            string effectSign = effect.value >= 0 ? "+" : "";

            if (effect.effectType == ItemEffectType.AttributeChange)
            {
                effectsString += $"<color={effectColor}>{effect.targetAttribute}: {effectSign}{effect.value:F0}</color>\n";
            }
            else if (effect.effectType == ItemEffectType.ResourceChange)
            {
                effectsString += $"<color={effectColor}>{GetResourceName(effect.resourceType.Value)}: {effectSign}{effect.value:F0}</color>\n";
            }
            else if (effect.effectType == ItemEffectType.StatusEffect)
            {
                var statusEffectType = effect.statusEffectType ?? effect.statusEffect;
                if (statusEffectType.HasValue)
                {
                    string statusName = GetStatusEffectName(statusEffectType.Value);
                    effectsString += $"<color={effectColor}>{statusName} ({effect.duration}天)</color>\n";
                }
            }
        }

        effectsText.text = effectsString;
    }

    /// <summary>
    /// 设置按钮状态
    /// </summary>
    private void SetButtonStates(BlindBoxItem item)
    {
        // 确认按钮总是可用
        if (confirmButton != null)
            confirmButton.interactable = true;

        // 使用按钮只对某些物品类型可用
        if (useButton != null)
        {
            bool canUse = item.contentType == BlindBoxContentType.Food ||
                         item.contentType == BlindBoxContentType.Medicine ||
                         item.contentType == BlindBoxContentType.Technology;
            
            useButton.interactable = canUse;
            useButton.gameObject.SetActive(canUse);
        }
    }

    /// <summary>
    /// 播放开盒动画
    /// </summary>
    private void PlayOpenAnimation()
    {
        // 简单的缩放动画
        transform.localScale = Vector3.zero;
        transform.DOScale(Vector3.one, 0.5f)
            .SetEase(Ease.OutBack);
    }

    /// <summary>
    /// 确认按钮点击
    /// </summary>
    private async void OnConfirmClicked()
    {
        // 应用物品效果
        ApplyItemEffects();
        
        // 关闭弹窗
        await UISystem.Close(UIName.BLIND_BOX_RESULT_POPUP);
    }

    /// <summary>
    /// 使用按钮点击
    /// </summary>
    private async void OnUseClicked()
    {
        // 立即使用物品
        UseItemImmediately();
        
        // 关闭弹窗
        await UISystem.Close(UIName.BLIND_BOX_RESULT_POPUP);
    }

    /// <summary>
    /// 应用物品效果
    /// </summary>
    private void ApplyItemEffects()
    {
        if (result?.item == null) return;

        var gameLogic = GameLogic.Instance;
        if (gameLogic?.playerData == null) return;

        var playerData = gameLogic.playerData;
        var item = result.item;

        foreach (var effect in item.effects)
        {
            ApplyEffect(playerData, effect);
        }

        Debug.Log($"应用物品效果：{item.name}");
    }

    /// <summary>
    /// 立即使用物品
    /// </summary>
    private void UseItemImmediately()
    {
        ApplyItemEffects();
        
        // 可以添加额外的使用效果
        Debug.Log($"立即使用物品：{result.item.name}");
    }

    /// <summary>
    /// 应用单个效果
    /// </summary>
    private void ApplyEffect(PlayerData playerData, ItemEffect effect)
    {

        switch (effect.effectType)
        {
            case ItemEffectType.AttributeChange:
                playerData.attributes.ModifyAttribute(effect.targetAttribute, effect.value);
                break;

            case ItemEffectType.ResourceChange:
                if (effect.value > 0)
                {
                    playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                }
                else
                {
                    playerData.resources.ConsumeResource(effect.resourceType.Value, -effect.value);
                }
                break;

            case ItemEffectType.StatusEffect:
                var statusEffectType = effect.statusEffectType ?? effect.statusEffect;
                if (statusEffectType.HasValue)
                {
                    var statusEffect = new StatusEffect(
                        statusEffectType.Value,
                        effect.value,
                        (int)effect.duration,
                        $"来自物品：{result.item.name}"
                    );
                    playerData.AddStatusEffect(statusEffect);
                }
                break;
        }
    }

    private string GetResourceName(ResourceType resourceType)
    {
        return resourceType switch
        {
            ResourceType.Credits => "信用点",
            ResourceType.Time => "时间",
            ResourceType.Diamond => "钻石",
            ResourceType.SocialScore => "社会积分",
            _ => resourceType.ToString()
        };
    }

    private string GetStatusEffectName(StatusEffectType statusType)
    {
        return statusType switch
        {
            StatusEffectType.Sickness => "疾病",
            StatusEffectType.Fatigue => "疲劳",
            StatusEffectType.Euphoria => "兴奋",
            StatusEffectType.SystemTracking => "系统监控",
            StatusEffectType.CognitiveDamage => "认知损伤",
            StatusEffectType.WorkEfficiency => "工作效率",
            _ => statusType.ToString()
        };
    }
}
