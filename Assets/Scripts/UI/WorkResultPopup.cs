using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using DG.Tweening;

/// <summary>
/// 工作结果弹窗 - 显示工作完成后的结果
/// </summary>
public class WorkResultPopup : UIBase
{
    [Header("UI组件")]
    public TextMeshProUGUI titleText;
    public TextMeshProUGUI jobNameText;
    public TextMeshProUGUI resultMessageText;
    public TextMeshProUGUI rewardsText;
    public TextMeshProUGUI efficiencyText;
    public Button confirmButton;
    public Image backgroundImage;
    public Image resultIcon;

    private WorkResultParam param;
    private WorkResult result;

    public override void OnInit()
    {
        InitializeUIReferences();
        BindButtonEvents();
    }

    public override void OnOpen(object param)
    {
        this.param = param as WorkResultParam;
        if (this.param == null)
        {
            Debug.LogError("WorkResultPopup: 参数类型错误");
            return;
        }

        result = this.param.result;
        DisplayResult();
        PlayResultAnimation();
    }

    /// <summary>
    /// 初始化UI组件引用
    /// </summary>
    private void InitializeUIReferences()
    {
        titleText = transform.Get<TextMeshProUGUI>("Header/Title");
        jobNameText = transform.Get<TextMeshProUGUI>("Content/JobName");
        resultMessageText = transform.Get<TextMeshProUGUI>("Content/ResultMessage");
        rewardsText = transform.Get<TextMeshProUGUI>("Content/Rewards");
        efficiencyText = transform.Get<TextMeshProUGUI>("Content/Efficiency");
        confirmButton = transform.Get<Button>("ConfirmBtn");
        backgroundImage = transform.Get<Image>("Background");
        resultIcon = transform.Get<Image>("Content/ResultIcon");
    }

    /// <summary>
    /// 绑定按钮事件
    /// </summary>
    private void BindButtonEvents()
    {
        confirmButton?.onClick.AddListener(OnConfirmClicked);
    }

    /// <summary>
    /// 显示工作结果
    /// </summary>
    private void DisplayResult()
    {
        if (result == null) return;

        // 设置标题
        if (titleText != null)
            titleText.text = result.success ? "工作完成" : "工作失败";

        // 设置工作名称
        if (jobNameText != null)
            jobNameText.text = GetJobDisplayName(result.jobType);

        // 设置结果消息
        if (resultMessageText != null)
            resultMessageText.text = result.message;

        // 设置奖励信息
        if (rewardsText != null)
            rewardsText.text = GetRewardsText();

        // 设置效率信息
        if (efficiencyText != null)
            efficiencyText.text = $"工作效率: {result.efficiency:P0}";

        // 设置背景颜色
        SetBackgroundColor();

        // 设置结果图标
        SetResultIcon();
    }

    /// <summary>
    /// 获取奖励文本
    /// </summary>
    private string GetRewardsText()
    {
        if (!result.success) return "无奖励";

        string rewardsText = "获得奖励:\n";
        
        if (result.creditsEarned > 0)
            rewardsText += $"<color=#FFD700>信用点: +{result.creditsEarned:F0}</color>\n";
        
        if (result.socialScoreEarned > 0)
            rewardsText += $"<color=#00FF00>社会积分: +{result.socialScoreEarned:F0}</color>\n";
        
        if (result.attributeChanges != null && result.attributeChanges.Count > 0)
        {
            foreach (var change in result.attributeChanges)
            {
                string color = change.Value >= 0 ? "#00FF00" : "#FF0000";
                string sign = change.Value >= 0 ? "+" : "";
                rewardsText += $"<color={color}>{GetAttributeName(change.Key)}: {sign}{change.Value:F0}</color>\n";
            }
        }

        if (result.energyConsumed > 0)
            rewardsText += $"<color=#FF6666>精力消耗: -{result.energyConsumed:F0}</color>\n";

        return rewardsText;
    }

    /// <summary>
    /// 设置背景颜色
    /// </summary>
    private void SetBackgroundColor()
    {
        if (backgroundImage == null) return;

        Color bgColor = result.success ? 
            new Color(0.2f, 0.6f, 0.2f, 0.9f) :  // 成功：绿色
            new Color(0.6f, 0.2f, 0.2f, 0.9f);   // 失败：红色

        backgroundImage.color = bgColor;
    }

    /// <summary>
    /// 设置结果图标
    /// </summary>
    private void SetResultIcon()
    {
        if (resultIcon == null) return;

        Color iconColor = result.success ? Color.green : Color.red;
        resultIcon.color = iconColor;

        // 这里可以根据工作类型设置不同的图标
        // 暂时使用颜色区分
    }

    /// <summary>
    /// 播放结果动画
    /// </summary>
    private void PlayResultAnimation()
    {
        // 简单的弹出动画
        transform.localScale = Vector3.zero;
        transform.DOScale(Vector3.one, 0.5f)
            .SetEase(Ease.OutBack);

        // 如果成功，播放额外的庆祝动画
        if (result.success && resultIcon != null)
        {
            resultIcon.transform.DORotate(new Vector3(0, 0, 360f), 1f)
                .SetLoops(-1, LoopType.Incremental)
                .SetDelay(0.5f);
        }
    }

    /// <summary>
    /// 确认按钮点击
    /// </summary>
    private async void OnConfirmClicked()
    {
        await UISystem.Close(UIName.WORK_RESULT_POPUP);
    }

    /// <summary>
    /// 获取工作显示名称
    /// </summary>
    private string GetJobDisplayName(JobType jobType)
    {
        return jobType switch
        {
            JobType.DataEntry => "数据录入",
            JobType.BoxSorting => "盲盒分拣",
            JobType.SystemMaintenance => "系统维护",
            JobType.Security => "安保工作",
            JobType.Management => "管理工作",
            JobType.Research => "研究工作",
            JobType.Enforcement => "执法工作",
            JobType.Advertising => "广告宣传",
            _ => jobType.ToString()
        };
    }

    /// <summary>
    /// 获取属性显示名称
    /// </summary>
    private string GetAttributeName(AttributeType attributeName)
    {
        return attributeName switch
        {
            "health" => "生命值",
            "energy" => "精力值",
            "luck" => "幸运值",
            "social" => "社交值",
            "dependence" => "依存度",
            "cognition" => "认知值",
            "pollution" => "污染度",
            "humanity" => "人性值",
            "morality" => "道德值",
            _ => attributeName
        };
    }
}
