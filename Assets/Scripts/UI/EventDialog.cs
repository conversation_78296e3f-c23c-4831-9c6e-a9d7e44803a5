using UnityEngine;
using UnityEngine.UI;
using TMPro;
using BoxOfFate.Game;
using System.Collections.Generic;
using BoxOfFate.Game.Core;

/// <summary>
/// 事件对话框 - 显示游戏事件和选择
/// </summary>
public class EventDialog : UIBase
{
    [Header("UI组件")]
    public TextMeshProUGUI titleText;
    public TextMeshProUGUI descriptionText;
    public Image eventIcon;
    public Image backgroundImage;

    [Header("选择按钮")]
    public Transform choicesContainer;
    public Button skipButton;

    private GameEvent currentEvent;
    private List<GameObject> choiceButtons = new List<GameObject>();

    public override void OnInit()
    {
        InitializeUIReferences();
        BindButtonEvents();
    }

    public override void OnOpen(object param)
    {
        var eventParam = param as EventDialogParam;
        if (eventParam == null)
        {
            Debug.LogError("EventDialog: 参数类型错误");
            return;
        }

        currentEvent = eventParam.gameEvent;
        DisplayEvent();
    }

    /// <summary>
    /// 初始化UI组件引用
    /// </summary>
    private void InitializeUIReferences()
    {
        titleText = transform.Get<TextMeshProUGUI>("Header/Title");
        descriptionText = transform.Get<TextMeshProUGUI>("Content/Description");
        eventIcon = transform.Get<Image>("Content/EventIcon");
        backgroundImage = transform.Get<Image>("Background");

        choicesContainer = transform.Get<Transform>("Content/Choices");
        skipButton = transform.Get<Button>("Footer/SkipBtn");

    }

    /// <summary>
    /// 绑定按钮事件
    /// </summary>
    private void BindButtonEvents()
    {
        skipButton?.onClick.AddListener(OnSkipClicked);
    }

    /// <summary>
    /// 显示事件
    /// </summary>
    private void DisplayEvent()
    {
        if (currentEvent == null) return;

        // 设置事件信息
        if (titleText != null)
            titleText.text = currentEvent.title;

        if (descriptionText != null)
            descriptionText.text = currentEvent.description;

        // 设置事件图标和背景
        SetEventVisuals();

        // 创建选择按钮
        CreateChoiceButtons();

        // 应用立即效果
        ApplyImmediateEffects();
    }

    /// <summary>
    /// 设置事件视觉效果
    /// </summary>
    private void SetEventVisuals()
    {
        Color eventColor = GetEventTypeColor(currentEvent.type);
        
        if (eventIcon != null)
            eventIcon.color = eventColor;

        if (backgroundImage != null)
        {
            Color bgColor = eventColor;
            bgColor.a = 0.3f;
            backgroundImage.color = bgColor;
        }
    }

    /// <summary>
    /// 创建选择按钮
    /// </summary>
    private void CreateChoiceButtons()
    {
        // 清除现有按钮
        foreach (var button in choiceButtons)
        {
            if (button != null) DestroyImmediate(button);
        }
        choiceButtons.Clear();

        // 创建新按钮
        for (int i = 0; i < currentEvent.choices.Count; i++)
        {
            var choice = currentEvent.choices[i];
            CreateChoiceButton(choice, i);
        }
    }

    /// <summary>
    /// 创建选择按钮
    /// </summary>
    private void CreateChoiceButton(EventChoice choice, int index)
    {
        GameObject buttonObj = CreateChoiceButtonObject($"Choice{index}", choicesContainer);
        choiceButtons.Add(buttonObj);

        // 设置按钮文本
        var buttonText = buttonObj.transform.Get<TextMeshProUGUI>("Text");
        if (buttonText != null)
            buttonText.text = choice.text;

        // 设置按钮描述
        var descText = buttonObj.transform.Get<TextMeshProUGUI>("Description");
        if (descText != null)
            descText.text = choice.description;

        // 绑定点击事件
        var button = buttonObj.GetComponent<Button>();
        if (button != null)
        {
            button.onClick.AddListener(() => OnChoiceSelected(choice));
        }
    }

    /// <summary>
    /// 创建选择按钮对象
    /// </summary>
    private GameObject CreateChoiceButtonObject(string name, Transform parent)
    {
        GameObject obj = new GameObject(name);
        obj.transform.SetParent(parent, false);

        RectTransform rect = obj.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 80);
        
        Image image = obj.AddComponent<Image>();
        image.color = new Color(0.2f, 0.3f, 0.5f, 0.8f);
        
        Button button = obj.AddComponent<Button>();
        
        VerticalLayoutGroup layout = obj.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(15, 15, 10, 10);
        layout.spacing = 5;

        // 选择文本
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(obj.transform, false);
        TextMeshProUGUI text = textGO.AddComponent<TextMeshProUGUI>();
        text.fontSize = 16;
        text.color = Color.white;
        text.fontStyle = FontStyles.Bold;

        // 描述文本
        GameObject descGO = new GameObject("Description");
        descGO.transform.SetParent(obj.transform, false);
        TextMeshProUGUI desc = descGO.AddComponent<TextMeshProUGUI>();
        desc.fontSize = 12;
        desc.color = Color.gray;

        return obj;
    }


    /// <summary>
    /// 应用立即效果
    /// </summary>
    private void ApplyImmediateEffects()
    {
        if (currentEvent.immediateEffects == null || currentEvent.immediateEffects.Count == 0)
            return;

        var gameLogic = GameLogic.Instance;
        if (gameLogic?.playerData == null) return;

        foreach (var effect in currentEvent.immediateEffects)
        {
            ApplyEventEffect(effect, gameLogic.playerData);
        }
    }

    /// <summary>
    /// 选择被选中
    /// </summary>
    private async void OnChoiceSelected(EventChoice choice)
    {
        // 应用选择效果
        ApplyChoiceEffects(choice);

        // 关闭对话框
        await UISystem.Close(UIName.EVENT_DIALOG);
    }

    /// <summary>
    /// 应用选择效果
    /// </summary>
    private void ApplyChoiceEffects(EventChoice choice)
    {
        var gameLogic = GameLogic.Instance;
        if (gameLogic?.playerData == null) return;

        foreach (var effect in choice.effects)
        {
            ApplyEventEffect(effect, gameLogic.playerData);
        }

        Debug.Log($"选择了: {choice.text}");
    }

    /// <summary>
    /// 应用事件效果
    /// </summary>
    private void ApplyEventEffect(GameEffect effect, PlayerData playerData)
    {
        switch (effect.type)
        {
            case GameEffectType.AttributeChange:
                playerData.attributes.ModifyAttribute(effect.attributeType, effect.value);
                break;

            case GameEffectType.ResourceChange:
                if (effect.value > 0)
                {
                    playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                }
                else
                {
                    playerData.resources.ConsumeResource(effect.resourceType.Value, -effect.value);
                }
                break;

            case GameEffectType.AddStatusEffect:
                var statusEffect = new StatusEffect(
                    effect.statusEffectType.Value,
                    effect.value,
                    (int)effect.duration,
                    $"来自事件：{currentEvent.title}"
                );
                playerData.AddStatusEffect(statusEffect);
                break;

            case GameEffectType.StoryProgress:
                if (!playerData.unlockedEvents.Contains(effect.stringValue))
                {
                    playerData.unlockedEvents.Add(effect.stringValue);
                }
                break;
        }
    }

    /// <summary>
    /// 跳过按钮点击
    /// </summary>
    private async void OnSkipClicked()
    {
        await UISystem.Close(UIName.EVENT_DIALOG);
    }


    /// <summary>
    /// 获取事件类型颜色
    /// </summary>
    private Color GetEventTypeColor(BoxOfFate.Game.EventType eventType)
    {
        return eventType switch
        {
            BoxOfFate.Game.EventType.System => Color.blue,
            BoxOfFate.Game.EventType.Social => Color.green,
            BoxOfFate.Game.EventType.Personal => Color.yellow,
            BoxOfFate.Game.EventType.Crisis => Color.red,
            BoxOfFate.Game.EventType.Opportunity => Color.cyan,
            BoxOfFate.Game.EventType.Resistance => Color.magenta,
            _ => Color.white
        };
    }
}

/// <summary>
/// 事件对话框参数
/// </summary>
public class EventDialogParam
{
    public GameEvent gameEvent;

    public EventDialogParam(GameEvent gameEvent)
    {
        this.gameEvent = gameEvent;
    }
}
