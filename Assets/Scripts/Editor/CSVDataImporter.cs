using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using BoxOfFate.Game;

namespace BoxOfFate.Editor
{
    /// <summary>
    /// CSV数据导入工具 - 基于现有数据结构
    /// </summary>
    public static class CSVDataImporter
    {
        private const string CSV_PATH = "Assets/Data/CSV/";
        private const string SO_PATH = "Assets/Data/ScriptableObjects/";

        [MenuItem("Tools/CSV Import/Import All CSV Data")]
        public static void ImportAllCSVData()
        {
            Debug.Log("[CSVDataImporter] 开始导入所有CSV数据...");

            try
            {
                ImportBlindBoxData();
                ImportItemData();
                ImportAchievementData();

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                Debug.Log("[CSVDataImporter] 所有CSV数据导入完成");
                EditorUtility.DisplayDialog("导入完成", "所有CSV数据已成功导入", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[CSVDataImporter] 导入失败: {e.Message}");
                EditorUtility.DisplayDialog("导入失败", $"CSV数据导入失败:\n{e.Message}", "确定");
            }
        }

        [MenuItem("Tools/CSV Import/Import BlindBox Data")]
        public static void ImportBlindBoxData()
        {
            string csvFile = CSV_PATH + "BlindBoxes.csv";
            if (!File.Exists(csvFile))
            {
                Debug.LogWarning($"[CSVDataImporter] CSV文件不存在: {csvFile}");
                return;
            }

            // 创建或加载现有的BlindBoxDataConfig
            string assetPath = SO_PATH + "BlindBoxDataConfig.asset";
            BlindBoxDataConfig config = AssetDatabase.LoadAssetAtPath<BlindBoxDataConfig>(assetPath);
            
            if (config == null)
            {
                config = ScriptableObject.CreateInstance<BlindBoxDataConfig>();
                EnsureDirectoryExists(assetPath);
                AssetDatabase.CreateAsset(config, assetPath);
            }

            // 清空现有数据
            config.boxConfigs.Clear();

            string[] lines = File.ReadAllLines(csvFile);
            
            // 跳过标题行
            for (int i = 1; i < lines.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(lines[i])) continue;

                try
                {
                    var boxData = ParseBlindBoxLine(lines[i]);
                    if (boxData != null)
                    {
                        config.boxConfigs.Add(boxData);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[CSVDataImporter] 解析盲盒数据第{i+1}行失败: {e.Message}");
                }
            }

            // 标记为脏数据并保存
            EditorUtility.SetDirty(config);
            AssetDatabase.SaveAssets();

            Debug.Log($"[CSVDataImporter] 导入盲盒数据: {config.boxConfigs.Count} 个盲盒");
        }

        [MenuItem("Tools/CSV Import/Import Item Data")]
        public static void ImportItemData()
        {
            string csvFile = CSV_PATH + "Items.csv";
            if (!File.Exists(csvFile))
            {
                Debug.LogWarning($"[CSVDataImporter] CSV文件不存在: {csvFile}");
                return;
            }

            // 创建或加载现有的BlindBoxDataConfig
            string assetPath = SO_PATH + "BlindBoxDataConfig.asset";
            BlindBoxDataConfig config = AssetDatabase.LoadAssetAtPath<BlindBoxDataConfig>(assetPath);
            
            if (config == null)
            {
                config = ScriptableObject.CreateInstance<BlindBoxDataConfig>();
                EnsureDirectoryExists(assetPath);
                AssetDatabase.CreateAsset(config, assetPath);
            }

            // 清空现有物品数据
            config.itemConfigs.Clear();

            string[] lines = File.ReadAllLines(csvFile);
            
            for (int i = 1; i < lines.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(lines[i])) continue;

                try
                {
                    var itemData = ParseItemLine(lines[i]);
                    if (itemData != null)
                    {
                        config.itemConfigs.Add(itemData);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[CSVDataImporter] 解析物品数据第{i+1}行失败: {e.Message}");
                }
            }

            // 标记为脏数据并保存
            EditorUtility.SetDirty(config);
            AssetDatabase.SaveAssets();

            Debug.Log($"[CSVDataImporter] 导入物品数据: {config.itemConfigs.Count} 个物品");
        }

        [MenuItem("Tools/CSV Import/Import Achievement Data")]
        public static void ImportAchievementData()
        {
            string csvFile = CSV_PATH + "Achievements.csv";
            if (!File.Exists(csvFile))
            {
                Debug.LogWarning($"[CSVDataImporter] CSV文件不存在: {csvFile}");
                return;
            }

            // 创建或加载现有的AchievementConfigSO
            string assetPath = SO_PATH + "AchievementConfig.asset";
            AchievementConfigSO config = AssetDatabase.LoadAssetAtPath<AchievementConfigSO>(assetPath);

            if (config == null)
            {
                config = ScriptableObject.CreateInstance<AchievementConfigSO>();
                EnsureDirectoryExists(assetPath);
                AssetDatabase.CreateAsset(config, assetPath);
            }

            // 清空现有数据
            config.achievements.Clear();

            // 导入成就基础数据
            ImportAchievementBasicData(config);

            // 导入成就条件数据
            ImportAchievementConditions(config);

            // 导入成就奖励数据
            ImportAchievementRewards(config);

            // 标记为脏数据并保存
            EditorUtility.SetDirty(config);
            AssetDatabase.SaveAssets();

            Debug.Log($"[CSVDataImporter] 导入成就数据: {config.achievements.Count} 个成就");
        }

        /// <summary>
        /// 导入成就基础数据
        /// </summary>
        private static void ImportAchievementBasicData(AchievementConfigSO config)
        {
            string csvFile = CSV_PATH + "Achievements.csv";
            string[] lines = File.ReadAllLines(csvFile);

            // 跳过标题行
            for (int i = 1; i < lines.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(lines[i])) continue;

                try
                {
                    var achievementData = ParseAchievementLine(lines[i]);
                    if (achievementData != null)
                    {
                        config.achievements.Add(achievementData);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[CSVDataImporter] 解析成就数据第{i+1}行失败: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 导入成就条件数据
        /// </summary>
        private static void ImportAchievementConditions(AchievementConfigSO config)
        {
            string csvFile = CSV_PATH + "AchievementConditions.csv";
            if (!File.Exists(csvFile))
            {
                Debug.LogWarning($"[CSVDataImporter] 成就条件CSV文件不存在: {csvFile}");
                return;
            }

            string[] lines = File.ReadAllLines(csvFile);

            // 跳过标题行
            for (int i = 1; i < lines.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(lines[i])) continue;

                try
                {
                    var conditionData = ParseAchievementConditionLine(lines[i]);
                    if (conditionData.achievementId != null && conditionData.condition != null)
                    {
                        // 找到对应的成就并添加条件
                        var achievement = config.achievements.Find(a => a.id == conditionData.achievementId);
                        if (achievement != null)
                        {
                            achievement.conditions.Add(conditionData.condition);
                        }
                        else
                        {
                            Debug.LogWarning($"[CSVDataImporter] 未找到成就ID: {conditionData.achievementId}");
                        }
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[CSVDataImporter] 解析成就条件第{i+1}行失败: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 导入成就奖励数据
        /// </summary>
        private static void ImportAchievementRewards(AchievementConfigSO config)
        {
            string csvFile = CSV_PATH + "AchievementRewards.csv";
            if (!File.Exists(csvFile))
            {
                Debug.LogWarning($"[CSVDataImporter] 成就奖励CSV文件不存在: {csvFile}");
                return;
            }

            string[] lines = File.ReadAllLines(csvFile);

            // 跳过标题行
            for (int i = 1; i < lines.Length; i++)
            {
                if (string.IsNullOrWhiteSpace(lines[i])) continue;

                try
                {
                    var rewardData = ParseAchievementRewardLine(lines[i]);
                    if (rewardData.achievementId != null && rewardData.reward != null)
                    {
                        // 找到对应的成就并添加奖励
                        var achievement = config.achievements.Find(a => a.id == rewardData.achievementId);
                        if (achievement != null)
                        {
                            achievement.rewards.Add(rewardData.reward);
                        }
                        else
                        {
                            Debug.LogWarning($"[CSVDataImporter] 未找到成就ID: {rewardData.achievementId}");
                        }
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[CSVDataImporter] 解析成就奖励第{i+1}行失败: {e.Message}");
                }
            }
        }

        // ===== 解析方法 =====

        /// <summary>
        /// 解析成就基础数据行
        /// </summary>
        private static AchievementData ParseAchievementLine(string line)
        {
            var values = SplitCSVLine(line);
            if (values.Length < 8) return null;

            return new AchievementData
            {
                id = values[0],
                name = values[1],
                description = values[2],
                category = ParseEnum<AchievementCategory>(values[3]),
                rarity = ParseEnum<AchievementRarity>(values[4]),
                isHidden = bool.Parse(values[5]),
                iconPath = values[6],
                conditions = new List<AchievementConditionData>(),
                rewards = new List<AchievementRewardData>()
            };
        }

        /// <summary>
        /// 解析成就条件数据行
        /// </summary>
        private static (string achievementId, AchievementConditionData condition) ParseAchievementConditionLine(string line)
        {
            var values = SplitCSVLine(line);
            if (values.Length < 7) return (null, null);

            var condition = new AchievementConditionData
            {
                type = ParseEnum<AchievementConditionType>(values[1]),
                value = float.Parse(values[2]),
                trackerId = string.IsNullOrEmpty(values[3]) ? null : values[3],
                attributeName = string.IsNullOrEmpty(values[4]) ? null : values[4],
                resourceType = string.IsNullOrEmpty(values[5]) ? null : ParseEnum<ResourceType>(values[5]),
                description = values[6]
            };

            return (values[0], condition);
        }

        /// <summary>
        /// 解析成就奖励数据行
        /// </summary>
        private static (string achievementId, AchievementRewardData reward) ParseAchievementRewardLine(string line)
        {
            var values = SplitCSVLine(line);
            if (values.Length < 8) return (null, null);

            var reward = new AchievementRewardData
            {
                type = ParseEnum<AchievementRewardType>(values[1]),
                value = float.Parse(values[2]),
                resourceType = string.IsNullOrEmpty(values[3]) ? null : ParseEnum<ResourceType>(values[3]),
                attributeName = string.IsNullOrEmpty(values[4]) ? null : values[4],
                title = string.IsNullOrEmpty(values[5]) ? null : values[5],
                eventId = string.IsNullOrEmpty(values[6]) ? null : values[6],
                description = values[7]
            };

            return (values[0], reward);
        }

        private static BlindBoxConfigData ParseBlindBoxLine(string line)
        {
            var values = SplitCSVLine(line);
            if (values.Length < 6) return null;

            var boxData = new BlindBoxConfigData
            {
                name = values[1],
                description = values[2],
                type = ParseEnum<BlindBoxType>(values[3]),
                requiredSocialClass = int.Parse(values[4]),
                basePrice = float.Parse(values[5]),
                priceType = ResourceType.Credits,
                isLimited = false,
                dailyLimit = -1
            };

            // 解析掉落表（简化版）
            if (values.Length > 6 && !string.IsNullOrEmpty(values[6]))
            {
                boxData.lootTables = ParseLootTable(values[6]);
            }

            return boxData;
        }

        private static BlindBoxItemData ParseItemLine(string line)
        {
            var values = SplitCSVLine(line);
            if (values.Length < 7) return null;

            return new BlindBoxItemData
            {
                id = values[0],
                name = values[1],
                description = values[2],
                contentType = ParseEnum<BlindBoxContentType>(values[3]),
                rarity = ParseEnum<ItemRarity>(values[4]),
                value = float.Parse(values[5]),
                isConsumable = bool.Parse(values[6]) // CSV中isConsumable转换为isPositive的逆
            };
        }

        // ===== 辅助方法 =====

        private static string[] SplitCSVLine(string line)
        {
            // 简单的CSV分割，处理引号包围的字段
            var result = new List<string>();
            bool inQuotes = false;
            string currentField = "";

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];
                
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(currentField.Trim());
                    currentField = "";
                }
                else
                {
                    currentField += c;
                }
            }
            
            result.Add(currentField.Trim());
            return result.ToArray();
        }

        private static T ParseEnum<T>(string value) where T : System.Enum
        {
            if (System.Enum.TryParse(typeof(T), value, true, out var result))
            {
                return (T)result;
            }
            return default(T);
        }

        private static List<BlindBoxLootTableData> ParseLootTable(string lootTableString)
        {
            // 解析掉落表，格式: "itemId1:probability1;itemId2:probability2"
            var lootTable = new List<BlindBoxLootTableData>();
            var entries = lootTableString.Split(';');

            foreach (var entry in entries)
            {
                var parts = entry.Split(':');
                if (parts.Length >= 2)
                {
                    float probability = float.Parse(parts[1]);
                    lootTable.Add(new BlindBoxLootTableData
                    {
                        itemId = parts[0].Trim(),
                        baseWeight = 1.0f,
                        displayProbability = probability,
                        realProbability = probability
                    });
                }
            }

            return lootTable;
        }

        private static void EnsureDirectoryExists(string assetPath)
        {
            string directory = Path.GetDirectoryName(assetPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }
    }
}
