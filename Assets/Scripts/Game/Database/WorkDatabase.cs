using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 工作数据库
    /// </summary>
    public static class WorkDatabase
    {
        private static Dictionary<JobType, JobConfig> jobConfigs;
        private static WorkSystemConfig config;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有工作数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            jobConfigs = new Dictionary<JobType, JobConfig>();
            config = new WorkSystemConfig();

            isLoaded = WorkDataLoader.LoadAllData(jobConfigs, config);
        }

        /// <summary>
        /// 获取工作配置
        /// </summary>
        public static Dictionary<JobType, JobConfig> GetJobConfigs()
        {
            if (!isLoaded) LoadAllData();
            return jobConfigs;
        }

        /// <summary>
        /// 获取工作系统配置
        /// </summary>
        public static WorkSystemConfig GetConfig()
        {
            if (!isLoaded) LoadAllData();
            return config;
        }

        /// <summary>
        /// 获取单个工作配置
        /// </summary>
        public static JobConfig GetJobConfig(JobType jobType)
        {
            if (!isLoaded) LoadAllData();
            return jobConfigs.ContainsKey(jobType) ? jobConfigs[jobType] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }

    /// <summary>
    /// 工作系统配置
    /// </summary>
    [Serializable]
    public class WorkSystemConfig
    {
        [Header("基础设置")]
        public float baseWorkHours = 8f;
        public float maxWorkHours = 12f;
        public float minEnergyRequired = 20f;

        [Header("效率设置")]
        public float baseEfficiency = 1f;
        public float maxEfficiency = 2f;
        public float fatigueThreshold = 10f;

        [Header("奖励设置")]
        public float baseSalaryMultiplier = 1f;
        public float overtimeMultiplier = 1.5f;
        public float performanceBonusRate = 0.1f;

        [Header("风险设置")]
        public float accidentProbability = 0.05f;
        public float stressProbability = 0.1f;
        public float burnoutThreshold = 80f;

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static WorkSystemConfig GetDefault()
        {
            return new WorkSystemConfig
            {
                baseWorkHours = 8f,
                maxWorkHours = 12f,
                minEnergyRequired = 20f,
                baseEfficiency = 1f,
                maxEfficiency = 2f,
                fatigueThreshold = 10f,
                baseSalaryMultiplier = 1f,
                overtimeMultiplier = 1.5f,
                performanceBonusRate = 0.1f,
                accidentProbability = 0.05f,
                stressProbability = 0.1f,
                burnoutThreshold = 80f
            };
        }
    }
}
