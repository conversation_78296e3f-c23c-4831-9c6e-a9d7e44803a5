using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 天赋数据库
    /// </summary>
    public static class TalentDatabase
    {
        private static Dictionary<TalentType, TalentConfig> talentConfigs;
        private static Dictionary<StatusEffectType, StatusEffectConfig> statusEffectConfigs;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有天赋数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            talentConfigs = new Dictionary<TalentType, TalentConfig>();
            statusEffectConfigs = new Dictionary<StatusEffectType, StatusEffectConfig>();

            isLoaded = TalentDataLoader.LoadAllData(talentConfigs, statusEffectConfigs);
        }

        /// <summary>
        /// 获取天赋配置
        /// </summary>
        public static Dictionary<TalentType, TalentConfig> GetTalentConfigs()
        {
            if (!isLoaded) LoadAllData();
            return talentConfigs;
        }

        /// <summary>
        /// 获取状态效果配置
        /// </summary>
        public static Dictionary<StatusEffectType, StatusEffectConfig> GetStatusEffectConfigs()
        {
            if (!isLoaded) LoadAllData();
            return statusEffectConfigs;
        }

        /// <summary>
        /// 获取单个天赋配置
        /// </summary>
        public static TalentConfig GetTalentConfig(TalentType talentType)
        {
            if (!isLoaded) LoadAllData();
            return talentConfigs.ContainsKey(talentType) ? talentConfigs[talentType] : null;
        }

        /// <summary>
        /// 获取单个状态效果配置
        /// </summary>
        public static StatusEffectConfig GetStatusEffectConfig(StatusEffectType statusEffectType)
        {
            if (!isLoaded) LoadAllData();
            return statusEffectConfigs.ContainsKey(statusEffectType) ? statusEffectConfigs[statusEffectType] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }
}
