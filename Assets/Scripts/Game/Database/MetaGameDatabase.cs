using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 元游戏数据库
    /// </summary>
    public static class MetaGameDatabase
    {
        private static Dictionary<string, MetaUpgrade> metaUpgrades;
        private static MetaGameConfig config;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有元游戏数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            metaUpgrades = new Dictionary<string, MetaUpgrade>();
            config = new MetaGameConfig();

            isLoaded = MetaGameDataLoader.LoadAllData(metaUpgrades, config);
        }

        /// <summary>
        /// 获取元游戏升级
        /// </summary>
        public static Dictionary<string, MetaUpgrade> GetMetaUpgrades()
        {
            if (!isLoaded) LoadAllData();
            return metaUpgrades;
        }

        /// <summary>
        /// 获取元游戏配置
        /// </summary>
        public static MetaGameConfig GetConfig()
        {
            if (!isLoaded) LoadAllData();
            return config;
        }

        /// <summary>
        /// 获取单个元游戏升级
        /// </summary>
        public static MetaUpgrade GetMetaUpgrade(string upgradeId)
        {
            if (!isLoaded) LoadAllData();
            return metaUpgrades.ContainsKey(upgradeId) ? metaUpgrades[upgradeId] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }

    /// <summary>
    /// 元游戏配置
    /// </summary>
    [Serializable]
    public class MetaGameConfig
    {
        [Header("基础设置")]
        public float baseMetaCurrencyRate = 1f;
        public float maxMetaCurrencyPerRun = 1000f;
        public float metaCurrencyDecayRate = 0.1f;

        [Header("升级设置")]
        public float baseUpgradeCost = 100f;
        public float upgradeCostMultiplier = 1.5f;
        public int maxUpgradeLevel = 10;

        [Header("奖励设置")]
        public float completionBonusRate = 0.2f;
        public float achievementBonusRate = 0.1f;
        public float survivalBonusRate = 0.05f;

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static MetaGameConfig GetDefault()
        {
            return new MetaGameConfig
            {
                baseMetaCurrencyRate = 1f,
                maxMetaCurrencyPerRun = 1000f,
                metaCurrencyDecayRate = 0.1f,
                baseUpgradeCost = 100f,
                upgradeCostMultiplier = 1.5f,
                maxUpgradeLevel = 10,
                completionBonusRate = 0.2f,
                achievementBonusRate = 0.1f,
                survivalBonusRate = 0.05f
            };
        }
    }
}
