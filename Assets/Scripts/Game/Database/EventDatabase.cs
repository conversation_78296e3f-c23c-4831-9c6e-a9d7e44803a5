using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 事件数据库
    /// </summary>
    public static class EventDatabase
    {
        private static Dictionary<string, GameEvent> events;
        private static EventSystemConfig config;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有事件数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            events = new Dictionary<string, GameEvent>();
            config = new EventSystemConfig();

            isLoaded = EventDataLoader.LoadAllData(events, config);
        }

        /// <summary>
        /// 获取事件配置
        /// </summary>
        public static Dictionary<string, GameEvent> GetEvents()
        {
            if (!isLoaded) LoadAllData();
            return events;
        }

        /// <summary>
        /// 获取事件系统配置
        /// </summary>
        public static EventSystemConfig GetConfig()
        {
            if (!isLoaded) LoadAllData();
            return config;
        }

        /// <summary>
        /// 获取单个事件
        /// </summary>
        public static GameEvent GetEvent(string eventId)
        {
            if (!isLoaded) LoadAllData();
            return events.ContainsKey(eventId) ? events[eventId] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }

    /// <summary>
    /// 事件系统配置
    /// </summary>
    [Serializable]
    public class EventSystemConfig
    {
        [Header("基础设置")]
        public float baseEventInterval = 300f; // 5分钟
        public float minEventInterval = 60f;   // 1分钟
        public float maxEventInterval = 600f;  // 10分钟

        [Header("触发概率")]
        public float baseEventProbability = 0.3f;
        public float storyEventProbability = 0.5f;
        public float randomEventProbability = 0.2f;

        [Header("事件权重")]
        public float positiveEventWeight = 0.4f;
        public float neutralEventWeight = 0.4f;
        public float negativeEventWeight = 0.2f;

        [Header("条件检查")]
        public bool enableConditionCheck = true;
        public bool enableCooldownCheck = true;
        public float globalCooldown = 30f;

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static EventSystemConfig GetDefault()
        {
            return new EventSystemConfig
            {
                baseEventInterval = 300f,
                minEventInterval = 60f,
                maxEventInterval = 600f,
                baseEventProbability = 0.3f,
                storyEventProbability = 0.5f,
                randomEventProbability = 0.2f,
                positiveEventWeight = 0.4f,
                neutralEventWeight = 0.4f,
                negativeEventWeight = 0.2f,
                enableConditionCheck = true,
                enableCooldownCheck = true,
                globalCooldown = 30f
            };
        }
    }
}
