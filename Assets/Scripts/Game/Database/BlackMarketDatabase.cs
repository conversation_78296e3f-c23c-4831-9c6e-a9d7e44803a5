using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 黑市数据库
    /// </summary>
    public static class BlackMarketDatabase
    {
        private static Dictionary<string, BlackMarketVendor> vendors;
        private static Dictionary<string, BlackMarketListing> listings;
        private static BlackMarketConfig config;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有黑市数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            vendors = new Dictionary<string, BlackMarketVendor>();
            listings = new Dictionary<string, BlackMarketListing>();
            config = new BlackMarketConfig();

            isLoaded = BlackMarketDataLoader.LoadAllData(vendors, listings, config);
        }

        /// <summary>
        /// 获取商贩配置
        /// </summary>
        public static Dictionary<string, BlackMarketVendor> GetVendors()
        {
            if (!isLoaded) LoadAllData();
            return vendors;
        }

        /// <summary>
        /// 获取交易列表
        /// </summary>
        public static Dictionary<string, BlackMarketListing> GetListings()
        {
            if (!isLoaded) LoadAllData();
            return listings;
        }

        /// <summary>
        /// 获取黑市配置
        /// </summary>
        public static BlackMarketConfig GetConfig()
        {
            if (!isLoaded) LoadAllData();
            return config;
        }

        /// <summary>
        /// 获取单个商贩
        /// </summary>
        public static BlackMarketVendor GetVendor(string vendorId)
        {
            if (!isLoaded) LoadAllData();
            return vendors.ContainsKey(vendorId) ? vendors[vendorId] : null;
        }

        /// <summary>
        /// 获取单个交易列表
        /// </summary>
        public static BlackMarketListing GetListing(string listingId)
        {
            if (!isLoaded) LoadAllData();
            return listings.ContainsKey(listingId) ? listings[listingId] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }

    /// <summary>
    /// 黑市全局配置
    /// </summary>
    [Serializable]
    public class BlackMarketConfig
    {
        [Header("基础设置")]
        public float baseRiskMultiplier = 1f;
        public float suspicionDecayRate = 0.1f;
        public float maxSuspicionLevel = 100f;

        [Header("访问条件")]
        public float minimumCognition = 30f;
        public bool requireSpecialFlag = true;
        public string accessFlagName = "knows_black_market";

        [Header("交易设置")]
        public float priceFluctuationRange = 0.2f;
        public int maxDailyTransactions = 3;
        public float reputationDecayRate = 0.05f;

        [Header("风险系统")]
        public float detectionThreshold = 80f;
        public float raidProbability = 0.1f;
        public int cooldownHours = 24;

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static BlackMarketConfig GetDefault()
        {
            return new BlackMarketConfig
            {
                baseRiskMultiplier = 1f,
                suspicionDecayRate = 0.1f,
                maxSuspicionLevel = 100f,
                minimumCognition = 30f,
                requireSpecialFlag = true,
                accessFlagName = "knows_black_market",
                priceFluctuationRange = 0.2f,
                maxDailyTransactions = 3,
                reputationDecayRate = 0.05f,
                detectionThreshold = 80f,
                raidProbability = 0.1f,
                cooldownHours = 24
            };
        }
    }
}
