using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 社会阶层数据库
    /// </summary>
    public static class SocialClassDatabase
    {
        private static Dictionary<SocialClass, SocialClassConfig> socialClasses;
        private static SocialClassSystemConfig config;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有社会阶层数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            socialClasses = new Dictionary<SocialClass, SocialClassConfig>();
            config = new SocialClassSystemConfig();

            isLoaded = SocialClassDataLoader.LoadAllData(socialClasses, config);
        }

        /// <summary>
        /// 获取社会阶层配置
        /// </summary>
        public static Dictionary<SocialClass, SocialClassConfig> GetSocialClasses()
        {
            if (!isLoaded) LoadAllData();
            return socialClasses;
        }

        /// <summary>
        /// 获取社会阶层系统配置
        /// </summary>
        public static SocialClassSystemConfig GetConfig()
        {
            if (!isLoaded) LoadAllData();
            return config;
        }

        /// <summary>
        /// 获取单个社会阶层配置
        /// </summary>
        public static SocialClassConfig GetSocialClass(SocialClass socialClass)
        {
            if (!isLoaded) LoadAllData();
            return socialClasses.ContainsKey(socialClass) ? socialClasses[socialClass] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }

    /// <summary>
    /// 社会阶层系统配置
    /// </summary>
    [Serializable]
    public class SocialClassSystemConfig
    {
        [Header("基础设置")]
        public float basePromotionThreshold = 100f;
        public float baseDemotionThreshold = 50f;
        public float socialMobilityRate = 0.1f;

        [Header("阶层影响")]
        public float parasiteResourceMultiplier = 0.8f;
        public float workerResourceMultiplier = 1f;
        public float chosenResourceMultiplier = 1.5f;

        [Header("特殊机制")]
        public bool enableClassWarfare = true;
        public bool enableSocialRevolution = true;
        public float revolutionThreshold = 80f;

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static SocialClassSystemConfig GetDefault()
        {
            return new SocialClassSystemConfig
            {
                basePromotionThreshold = 100f,
                baseDemotionThreshold = 50f,
                socialMobilityRate = 0.1f,
                parasiteResourceMultiplier = 0.8f,
                workerResourceMultiplier = 1f,
                chosenResourceMultiplier = 1.5f,
                enableClassWarfare = true,
                enableSocialRevolution = true,
                revolutionThreshold = 80f
            };
        }
    }
}
