using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 特权数据库
    /// </summary>
    public static class PrivilegeDatabase
    {
        private static Dictionary<string, PrivilegeConfig> privileges;
        private static PrivilegeSystemConfig config;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有特权数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            privileges = new Dictionary<string, PrivilegeConfig>();
            config = new PrivilegeSystemConfig();

            isLoaded = PrivilegeDataLoader.LoadAllData(privileges, config);
        }

        /// <summary>
        /// 获取特权配置
        /// </summary>
        public static Dictionary<string, PrivilegeConfig> GetPrivileges()
        {
            if (!isLoaded) LoadAllData();
            return privileges;
        }

        /// <summary>
        /// 获取特权系统配置
        /// </summary>
        public static PrivilegeSystemConfig GetConfig()
        {
            if (!isLoaded) LoadAllData();
            return config;
        }

        /// <summary>
        /// 获取单个特权配置
        /// </summary>
        public static PrivilegeConfig GetPrivilege(string privilegeId)
        {
            if (!isLoaded) LoadAllData();
            return privileges.ContainsKey(privilegeId) ? privileges[privilegeId] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }

    /// <summary>
    /// 特权系统配置
    /// </summary>
    [Serializable]
    public class PrivilegeSystemConfig
    {
        [Header("基础设置")]
        public float basePrivilegeDecayRate = 0.05f;
        public float maxPrivilegeLevel = 100f;
        public float privilegeGainMultiplier = 1f;

        [Header("社会阶层影响")]
        public float parasitePrivilegeMultiplier = 0.5f;
        public float workerPrivilegeMultiplier = 1f;
        public float chosenPrivilegeMultiplier = 2f;

        [Header("特权效果")]
        public float resourceBonusRate = 0.1f;
        public float accessBonusRate = 0.2f;
        public float protectionBonusRate = 0.15f;

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public static PrivilegeSystemConfig GetDefault()
        {
            return new PrivilegeSystemConfig
            {
                basePrivilegeDecayRate = 0.05f,
                maxPrivilegeLevel = 100f,
                privilegeGainMultiplier = 1f,
                parasitePrivilegeMultiplier = 0.5f,
                workerPrivilegeMultiplier = 1f,
                chosenPrivilegeMultiplier = 2f,
                resourceBonusRate = 0.1f,
                accessBonusRate = 0.2f,
                protectionBonusRate = 0.15f
            };
        }
    }
}
