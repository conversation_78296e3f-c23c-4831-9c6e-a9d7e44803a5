using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒数据库
    /// </summary>
    public static class BlindBoxDatabase
    {
        private static Dictionary<BlindBoxType, BlindBoxConfig> boxConfigs;
        private static Dictionary<string, BlindBoxItem> itemDatabase;
        private static FraudAlgorithmConfig fraudConfig;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有盲盒数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            boxConfigs = new Dictionary<BlindBoxType, BlindBoxConfig>();
            itemDatabase = new Dictionary<string, BlindBoxItem>();
            fraudConfig = new FraudAlgorithmConfig();

            isLoaded = BlindBoxDataLoader.LoadAllData(boxConfigs, itemDatabase, fraudConfig);
        }

        /// <summary>
        /// 获取盲盒配置
        /// </summary>
        public static Dictionary<BlindBoxType, BlindBoxConfig> GetBoxConfigs()
        {
            if (!isLoaded) LoadAllData();
            return boxConfigs;
        }

        /// <summary>
        /// 获取物品数据库
        /// </summary>
        public static Dictionary<string, BlindBoxItem> GetItemDatabase()
        {
            if (!isLoaded) LoadAllData();
            return itemDatabase;
        }

        /// <summary>
        /// 获取欺诈算法配置
        /// </summary>
        public static FraudAlgorithmConfig GetFraudConfig()
        {
            if (!isLoaded) LoadAllData();
            return fraudConfig;
        }

    }

}
