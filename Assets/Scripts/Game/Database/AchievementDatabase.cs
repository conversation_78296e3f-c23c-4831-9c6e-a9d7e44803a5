using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 成就数据库
    /// </summary>
    public static class AchievementDatabase
    {
        private static Dictionary<string, Achievement> achievements;
        private static Dictionary<string, ProgressTracker> progressTrackers;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有成就数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            achievements = new Dictionary<string, Achievement>();
            progressTrackers = new Dictionary<string, ProgressTracker>();

            isLoaded = AchievementDataLoader.LoadAllData(achievements, progressTrackers);
        }

        /// <summary>
        /// 获取成就配置
        /// </summary>
        public static Dictionary<string, Achievement> GetAchievements()
        {
            if (!isLoaded) LoadAllData();
            return achievements;
        }

        /// <summary>
        /// 获取进度追踪器
        /// </summary>
        public static Dictionary<string, ProgressTracker> GetProgressTrackers()
        {
            if (!isLoaded) LoadAllData();
            return progressTrackers;
        }

        /// <summary>
        /// 获取单个成就
        /// </summary>
        public static Achievement GetAchievement(string achievementId)
        {
            if (!isLoaded) LoadAllData();
            return achievements.ContainsKey(achievementId) ? achievements[achievementId] : null;
        }

        /// <summary>
        /// 获取单个进度追踪器
        /// </summary>
        public static ProgressTracker GetProgressTracker(string trackerId)
        {
            if (!isLoaded) LoadAllData();
            return progressTrackers.ContainsKey(trackerId) ? progressTrackers[trackerId] : null;
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        public static void ReloadData()
        {
            isLoaded = false;
            LoadAllData();
        }
    }
}
