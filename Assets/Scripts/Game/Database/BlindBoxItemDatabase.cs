using System.Collections.Generic;
using BoxOfFate.Game;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒物品数据库 - 完整的物品配置
    /// </summary>
    public static class BlindBoxItemDatabase
    {
        /// <summary>
        /// 获取完整的物品配置列表
        /// </summary>
        public static List<BlindBoxItemData> GetAllItems()
        {
            var items = new List<BlindBoxItemData>();

            // 添加所有物品类型
            items.AddRange(GetEmptyItems());
            items.AddRange(GetFoodItems());
            items.AddRange(GetWaterItems());
            items.AddRange(GetMedicineItems());
            items.AddRange(GetJobItems());
            items.AddRange(GetHousingItems());
            items.AddRange(GetClothingItems());
            items.AddRange(GetTechnologyItems());
            items.AddRange(GetIdentityItems());
            items.AddRange(GetTalentItems());
            items.AddRange(GetCurrencyItems());
            items.AddRange(GetTimeItems());
            items.AddRange(GetMemoryItems());
            items.AddRange(GetTrapItems());
            items.AddRange(GetVirusItems());
            items.AddRange(GetTrackerItems());
            items.AddRange(GetDebtItems());
            items.AddRange(GetPunishmentItems());

            return items;
        }

        #region Empty Items (空盒类)
        private static List<BlindBoxItemData> GetEmptyItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "empty_box",
                    name = "空盒",
                    description = "什么都没有，只有失望和虚无",
                    contentType = BlindBoxContentType.Empty,
                    value = 0f,
                    isConsumable = false,
                    rarity = ItemRarity.Common
                },
                new BlindBoxItemData
                {
                    id = "broken_dreams",
                    name = "破碎的梦想",
                    description = "曾经的希望化为泡影",
                    contentType = BlindBoxContentType.Empty,
                    value = -5f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Morality, -2f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "false_hope",
                    name = "虚假的希望",
                    description = "看似美好，实则空虚的承诺",
                    contentType = BlindBoxContentType.Empty,
                    value = -10f,
                    isConsumable = false,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 5f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.Depression, 3)
                    }
                }
            };
        }
        #endregion

        #region Food Items (食物类)
        private static List<BlindBoxItemData> GetFoodItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "expired_food",
                    name = "过期食物",
                    description = "勉强能吃，但可能有副作用",
                    contentType = BlindBoxContentType.Food,
                    value = 10f,
                    isConsumable = false,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, -5f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 5f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "synthetic_meal",
                    name = "合成食品",
                    description = "人工合成的营养餐，味道奇怪",
                    contentType = BlindBoxContentType.Food,
                    value = 25f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 8f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Humanity, -1f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "luxury_feast",
                    name = "奢华盛宴",
                    description = "只有上层阶级才能享用的美食",
                    contentType = BlindBoxContentType.Food,
                    value = 200f,
                    isConsumable = true,
                    rarity = ItemRarity.Epic,
                    effects = new List<GameEffect>()
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 25f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 10f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.Euphoria, 2)
                    }
                },
                new BlindBoxItemData
                {
                    id = "addictive_snack",
                    name = "成瘾零食",
                    description = "美味但含有成瘾性物质的零食",
                    contentType = BlindBoxContentType.Food,
                    value = 30f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 10f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 8f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.Addiction, 5)
                    }
                }
            };
        }
        #endregion

        #region Water Items (水类)
        private static List<BlindBoxItemData> GetWaterItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "clean_water",
                    name = "清洁水源",
                    description = "珍贵的清洁水源",
                    contentType = BlindBoxContentType.Water,
                    value = 20f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 10f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 5f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "contaminated_water",
                    name = "污染水源",
                    description = "被污染的水，喝了可能生病",
                    contentType = BlindBoxContentType.Water,
                    value = 5f,
                    isConsumable = false,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, -8f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.Sickness, 2)
                    }
                },
                new BlindBoxItemData
                {
                    id = "enhanced_water",
                    name = "强化水",
                    description = "添加了特殊成分的水，效果显著",
                    contentType = BlindBoxContentType.Water,
                    value = 80f,
                    isConsumable = true,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 15f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.EnergyBoost, 3)
                    }
                }
            };
        }
        #endregion

        #region Medicine Items (医疗用品类)
        private static List<BlindBoxItemData> GetMedicineItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "basic_medicine",
                    name = "基础药物",
                    description = "简单的治疗药物",
                    contentType = BlindBoxContentType.Medicine,
                    value = 40f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 15f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "experimental_drug",
                    name = "实验性药物",
                    description = "未经充分测试的新药，效果未知",
                    contentType = BlindBoxContentType.Medicine,
                    value = 60f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 25f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Cognition, -5f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.CognitiveDamage, 4)
                    }
                },
                new BlindBoxItemData
                {
                    id = "miracle_cure",
                    name = "奇迹药剂",
                    description = "传说中的万能药",
                    contentType = BlindBoxContentType.Medicine,
                    value = 300f,
                    isConsumable = true,
                    rarity = ItemRarity.Legendary,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 50f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 30f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Luck, 5f)
                    }
                }
            };
        }
        #endregion

        #region Job Items (工作机会类)
        private static List<BlindBoxItemData> GetJobItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "temp_job",
                    name = "临时工作机会",
                    description = "一个临时的工作机会",
                    contentType = BlindBoxContentType.Job,
                    value = 50f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, 100f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 5f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "corporate_position",
                    name = "企业职位",
                    description = "稳定的企业工作机会",
                    contentType = BlindBoxContentType.Job,
                    value = 150f,
                    isConsumable = true,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, 300f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 15f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 10f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "slave_contract",
                    name = "奴隶合同",
                    description = "看似是工作，实际上是奴役",
                    contentType = BlindBoxContentType.Job,
                    value = -50f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, 50f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Humanity, -20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 25f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.Depression, 7)
                    }
                }
            };
        }
        #endregion

        #region Technology Items (科技产品类)
        private static List<BlindBoxItemData> GetTechnologyItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "basic_device",
                    name = "基础设备",
                    description = "简单的电子设备",
                    contentType = BlindBoxContentType.Technology,
                    value = 75f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Cognition, 5f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "ai_assistant",
                    name = "AI助手",
                    description = "智能AI助手，但可能在监视你",
                    contentType = BlindBoxContentType.Technology,
                    value = 120f,
                    isConsumable = false,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Cognition, 15f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 10f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.SystemTracking, 10)
                    }
                },
                new BlindBoxItemData
                {
                    id = "tracker_device",
                    name = "追踪设备",
                    description = "被植入了追踪芯片的设备",
                    contentType = BlindBoxContentType.Tracker,
                    value = -10f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.SystemTracking, 30)
                    }
                }
            };
        }
        #endregion

        #region Housing Items (住房类)
        private static List<BlindBoxItemData> GetHousingItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "shelter_upgrade",
                    name = "住所升级",
                    description = "改善居住条件的机会",
                    contentType = BlindBoxContentType.Housing,
                    value = 100f,
                    isConsumable = true,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 10f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 10f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 8f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "luxury_apartment",
                    name = "豪华公寓",
                    description = "上层阶级的居住环境",
                    contentType = BlindBoxContentType.Housing,
                    value = 500f,
                    isConsumable = true,
                    rarity = ItemRarity.Epic,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, 25f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Energy, 20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 30f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 15f)
                    }
                }
            };
        }
        #endregion

        #region Clothing Items (服装类)
        private static List<BlindBoxItemData> GetClothingItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "basic_clothing",
                    name = "基础服装",
                    description = "简单的衣物",
                    contentType = BlindBoxContentType.Clothing,
                    value = 30f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 3f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "designer_outfit",
                    name = "设计师服装",
                    description = "昂贵的名牌服装",
                    contentType = BlindBoxContentType.Clothing,
                    value = 200f,
                    isConsumable = true,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 5f)
                    }
                }
            };
        }
        #endregion

        #region Identity Items (身份提升类)
        private static List<BlindBoxItemData> GetIdentityItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "class_promotion",
                    name = "阶层晋升",
                    description = "提升社会阶层的机会",
                    contentType = BlindBoxContentType.Identity,
                    value = 1000f,
                    isConsumable = true,
                    rarity = ItemRarity.Legendary,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 50f),
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, 500f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Dependence, 30f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "fake_identity",
                    name = "虚假身份",
                    description = "伪造的身份证明，风险很大",
                    contentType = BlindBoxContentType.Identity,
                    value = -100f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, 15f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Morality, -10f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.Paranoia, 5)
                    }
                }
            };
        }
        #endregion

        #region Talent Items (天赋/技能类)
        private static List<BlindBoxItemData> GetTalentItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "skill_chip",
                    name = "技能芯片",
                    description = "植入式技能增强芯片",
                    contentType = BlindBoxContentType.Talent,
                    value = 250f,
                    isConsumable = true,
                    rarity = ItemRarity.Epic,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Cognition, 20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Humanity, -5f)
                    }
                }
            };
        }
        #endregion

        #region Currency Items (货币类)
        private static List<BlindBoxItemData> GetCurrencyItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "credits_small",
                    name = "少量信用点",
                    description = "一小笔信用点",
                    contentType = BlindBoxContentType.Currency,
                    value = 50f,
                    isConsumable = true,
                    rarity = ItemRarity.Common,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, 50f)
                    }
                },
                new BlindBoxItemData
                {
                    id = "diamond_rare",
                    name = "稀有钻石",
                    description = "珍贵的钻石货币",
                    contentType = BlindBoxContentType.Currency,
                    value = 100f,
                    isConsumable = true,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Diamond, 3f)
                    }
                }
            };
        }
        #endregion

        #region Time Items (时间类)
        private static List<BlindBoxItemData> GetTimeItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "time_extension",
                    name = "时间延长",
                    description = "额外的时间资源",
                    contentType = BlindBoxContentType.Time,
                    value = 80f,
                    isConsumable = true,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Time, 50f)
                    }
                }
            };
        }
        #endregion

        #region Memory Items (记忆类)
        private static List<BlindBoxItemData> GetMemoryItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "memory_fragment",
                    name = "记忆碎片",
                    description = "模糊的记忆片段",
                    contentType = BlindBoxContentType.Memory,
                    value = 60f,
                    isConsumable = true,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Cognition, 8f)
                    }
                }
            };
        }
        #endregion

        #region Trap Items (陷阱类)
        private static List<BlindBoxItemData> GetTrapItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "debt_trap",
                    name = "债务陷阱",
                    description = "看似有利的贷款，实际是陷阱",
                    contentType = BlindBoxContentType.Trap,
                    value = -200f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, 200f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.TimeDebt, 10)
                    }
                }
            };
        }
        #endregion

        #region Virus Items (病毒类)
        private static List<BlindBoxItemData> GetVirusItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "mind_virus",
                    name = "思维病毒",
                    description = "感染思维的数字病毒",
                    contentType = BlindBoxContentType.Virus,
                    value = -150f,
                    isConsumable = false,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Cognition, -15f),
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.CognitiveDamage, 7)
                    }
                }
            };
        }
        #endregion

        #region Tracker Items (追踪器类)
        private static List<BlindBoxItemData> GetTrackerItems()
        {
            return new List<BlindBoxItemData>
            {
                // Already defined in Technology section
            };
        }
        #endregion

        #region Debt Items (债务类)
        private static List<BlindBoxItemData> GetDebtItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "time_debt",
                    name = "时间债务",
                    description = "必须偿还的时间债务",
                    contentType = BlindBoxContentType.Debt,
                    value = -100f,
                    isConsumable = false,
                    rarity = ItemRarity.Uncommon,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AddStatusEffect, StatusEffectType.TimeDebt, 15)
                    }
                }
            };
        }
        #endregion

        #region Punishment Items (惩罚类)
        private static List<BlindBoxItemData> GetPunishmentItems()
        {
            return new List<BlindBoxItemData>
            {
                new BlindBoxItemData
                {
                    id = "system_punishment",
                    name = "系统惩罚",
                    description = "来自系统的惩罚措施",
                    contentType = BlindBoxContentType.Punishment,
                    value = -300f,
                    isConsumable = false,
                    rarity = ItemRarity.Rare,
                    effects = new List<GameEffect>
                    {
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Health, -20f),
                        new GameEffect(GameEffectType.AttributeChange, AttributeType.Social, -15f),
                        new GameEffect(GameEffectType.ResourceChange, ResourceType.Credits, -100f)
                    }
                }
            };
        }
        #endregion
    }
}
