using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 成就配置ScriptableObject
    /// </summary>
    [CreateAssetMenu(fileName = "AchievementConfig", menuName = "BoxOfFate/Achievement Config")]
    public class AchievementConfigSO : ScriptableObject
    {
        [Header("成就数据")]
        public List<AchievementData> achievements = new List<AchievementData>();
        
        [Header("进度追踪器配置")]
        public List<ProgressTrackerData> progressTrackers = new List<ProgressTrackerData>();

        /// <summary>
        /// 获取成就字典
        /// </summary>
        public Dictionary<string, Achievement> GetAchievementDictionary()
        {
            var dict = new Dictionary<string, Achievement>();
            
            foreach (var data in achievements)
            {
                var achievement = data.ToAchievement();
                dict[achievement.id] = achievement;
            }
            
            return dict;
        }

        /// <summary>
        /// 获取进度追踪器字典
        /// </summary>
        public Dictionary<string, ProgressTracker> GetProgressTrackerDictionary()
        {
            var dict = new Dictionary<string, ProgressTracker>();
            
            foreach (var data in progressTrackers)
            {
                var tracker = data.ToProgressTracker();
                dict[tracker.id] = tracker;
            }
            
            return dict;
        }

        /// <summary>
        /// 验证数据完整性
        /// </summary>
        [ContextMenu("验证成就数据")]
        public void ValidateData()
        {
            Debug.Log($"[AchievementConfig] 开始验证成就数据...");
            
            // 检查ID唯一性
            var ids = new HashSet<string>();
            foreach (var achievement in achievements)
            {
                if (ids.Contains(achievement.id))
                {
                    Debug.LogError($"[AchievementConfig] 重复的成就ID: {achievement.id}");
                }
                else
                {
                    ids.Add(achievement.id);
                }
            }
            
            // 检查条件和奖励的完整性
            foreach (var achievement in achievements)
            {
                if (achievement.conditions == null || achievement.conditions.Count == 0)
                {
                    Debug.LogWarning($"[AchievementConfig] 成就 {achievement.id} 没有条件");
                }
                
                if (achievement.rewards == null || achievement.rewards.Count == 0)
                {
                    Debug.LogWarning($"[AchievementConfig] 成就 {achievement.id} 没有奖励");
                }
            }
            
            Debug.Log($"[AchievementConfig] 验证完成，共 {achievements.Count} 个成就");
        }
    }

    /// <summary>
    /// 成就数据（用于CSV导入）
    /// </summary>
    [Serializable]
    public class AchievementData
    {
        public string id;
        public string name;
        public string description;
        public AchievementCategory category;
        public AchievementRarity rarity;
        public bool isHidden;
        public string iconPath;
        public List<AchievementConditionData> conditions = new List<AchievementConditionData>();
        public List<AchievementRewardData> rewards = new List<AchievementRewardData>();

        /// <summary>
        /// 转换为Achievement对象
        /// </summary>
        public Achievement ToAchievement()
        {
            var achievement = new Achievement
            {
                id = this.id,
                name = this.name,
                description = this.description,
                category = this.category,
                rarity = this.rarity,
                isHidden = this.isHidden,
                iconPath = this.iconPath,
                conditions = new List<AchievementCondition>(),
                rewards = new List<AchievementReward>(),
                isUnlocked = false,
                unlockedDate = DateTime.MinValue
            };

            // 转换条件
            foreach (var conditionData in conditions)
            {
                achievement.conditions.Add(conditionData.ToAchievementCondition());
            }

            // 转换奖励
            foreach (var rewardData in rewards)
            {
                achievement.rewards.Add(rewardData.ToAchievementReward());
            }

            return achievement;
        }
    }

    /// <summary>
    /// 成就条件数据（用于CSV导入）
    /// </summary>
    [Serializable]
    public class AchievementConditionData
    {
        public AchievementConditionType type;
        public float value;
        public string trackerId;
        public AttributeType? attributeType;
        public ResourceType? resourceType;
        public string description;

        /// <summary>
        /// 转换为AchievementCondition对象
        /// </summary>
        public AchievementCondition ToAchievementCondition()
        {
            var condition = new AchievementCondition(type, value);
            condition.trackerId = trackerId;
            condition.attributeType = attributeType;
            condition.resourceType = resourceType;
            return condition;
        }
    }

    /// <summary>
    /// 成就奖励数据（用于CSV导入）
    /// </summary>
    [Serializable]
    public class AchievementRewardData
    {
        public AchievementRewardType type;
        public float value;
        public ResourceType? resourceType;
        public AttributeType? attributeType;
        public string title;
        public string eventId;
        public string description;

        /// <summary>
        /// 转换为AchievementReward对象
        /// </summary>
        public AchievementReward ToAchievementReward()
        {
            // 根据奖励类型选择合适的构造函数
            switch (type)
            {
                case AchievementRewardType.Resource:
                    return new AchievementReward(type, resourceType, value);
                case AchievementRewardType.Attribute:
                    return new AchievementReward(type, attributeType, value);
                case AchievementRewardType.Title:
                    return new AchievementReward(type, title);
                case AchievementRewardType.UnlockEvent:
                    return new AchievementReward(type, eventId);
                default:
                    // 默认使用资源奖励构造函数
                    return new AchievementReward(type, resourceType ?? ResourceType.Credits, value);
            }
        }
    }

    /// <summary>
    /// 进度追踪器数据（用于CSV导入）
    /// </summary>
    [Serializable]
    public class ProgressTrackerData
    {
        public string id;
        public string name;
        public string description;
        public float maxValue;
        public bool isPersistent;
        public List<MilestoneData> milestones = new List<MilestoneData>();

        /// <summary>
        /// 转换为ProgressTracker对象
        /// </summary>
        public ProgressTracker ToProgressTracker()
        {
            var tracker = new ProgressTracker();
            tracker.id = id;
            tracker.name = name;
            tracker.description = description;
            tracker.maxValue = maxValue;
            tracker.currentValue = 0f;

            // 转换里程碑
            foreach (var milestoneData in milestones)
            {
                tracker.milestones.Add(milestoneData.ToMilestone());
            }

            return tracker;
        }
    }

    /// <summary>
    /// 里程碑数据（用于CSV导入）
    /// </summary>
    [Serializable]
    public class MilestoneData
    {
        public float value;
        public string name;
        public string description;
        public bool isReached;

        /// <summary>
        /// 转换为Milestone对象
        /// </summary>
        public Milestone ToMilestone()
        {
            return new Milestone
            {
                threshold = value,  // Milestone使用threshold而不是value
                name = name,
                description = description,
                isReached = isReached
            };
        }
    }
}
