using UnityEngine;
using BoxOfFate.Game;
using System.Linq;

namespace BoxOfFate.Examples
{
    /// <summary>
    /// 简化的CSV数据系统示例
    /// </summary>
    public class SimpleCSVExample : MonoBehaviour
    {
        [Header("测试配置")]
        public bool testOnStart = true;

        private void Start()
        {
            if (testOnStart)
            {
                TestSimpleDataSystem();
            }
        }

        /// <summary>
        /// 测试CSV数据系统
        /// </summary>
        [ContextMenu("测试数据系统")]
        public void TestSimpleDataSystem()
        {
            Debug.Log("=== CSV数据系统测试开始 ===");

            // 等待数据系统初始化
            if (GameDataSystem.Instance == null)
            {
                Debug.LogError("GameDataSystem未初始化！请确保场景中有GameDataSystem组件");
                return;
            }

            if (!GameDataSystem.Instance.IsDataLoaded())
            {
                Debug.LogError("游戏数据未加载！请检查CSV文件和ScriptableObject配置");
                return;
            }

            // 测试盲盒数据
            TestBlindBoxData();

            // 测试物品数据
            TestItemData();

            // 测试成就数据
            TestAchievementData();

            // 测试游戏平衡配置
            TestGameBalance();

            // 测试元游戏配置
            TestMetaGameConfig();

            Debug.Log("=== CSV数据系统测试完成 ===");
        }

        /// <summary>
        /// 测试盲盒数据
        /// </summary>
        private void TestBlindBoxData()
        {
            Debug.Log("--- 测试盲盒数据 ---");

            var allBoxes = GameDataSystem.Instance.GetAllBlindBoxConfigs();
            Debug.Log($"总共加载了 {allBoxes.Count} 个盲盒配置");

            foreach (var kvp in allBoxes)
            {
                var boxType = kvp.Key;
                var config = kvp.Value;

                Debug.Log($"盲盒: {config.name} (类型: {boxType})");
                Debug.Log($"  价格: {config.basePrice}, 描述: {config.description}");

                if (config.lootTables != null && config.lootTables.Count > 0)
                {
                    Debug.Log($"  掉落表包含 {config.lootTables.Count} 个物品:");
                    foreach (var loot in config.lootTables)
                    {
                        Debug.Log($"    - {loot.itemId}: 显示概率 {loot.displayProbability:P1}, 真实概率 {loot.realProbability:P1}");
                    }
                }
            }

            // 测试特定盲盒获取
            var basicBox = GameDataSystem.Instance.GetBlindBoxConfig(BlindBoxType.Basic);
            if (basicBox != null)
            {
                Debug.Log($"成功获取基础盲盒: {basicBox.name}");
            }
            else
            {
                Debug.LogWarning("未找到基础盲盒数据");
            }
        }

        /// <summary>
        /// 测试物品数据
        /// </summary>
        private void TestItemData()
        {
            Debug.Log("--- 测试物品数据 ---");

            var allItems = GameDataSystem.Instance.GetAllItems();
            Debug.Log($"总共加载了 {allItems.Count} 个物品");

            // 按类型统计物品
            var foodItems = GameDataSystem.Instance.GetItemsByType(BlindBoxContentType.Food);
            var waterItems = GameDataSystem.Instance.GetItemsByType(BlindBoxContentType.Water);
            var emptyItems = GameDataSystem.Instance.GetItemsByType(BlindBoxContentType.Empty);

            Debug.Log($"食物类物品: {foodItems.Count} 个");
            Debug.Log($"水类物品: {waterItems.Count} 个");
            Debug.Log($"空盒类物品: {emptyItems.Count} 个");

            // 测试特定物品获取
            var emptyBox = GameDataSystem.Instance.GetItemData("empty_box");
            if (emptyBox != null)
            {
                Debug.Log($"空盒物品详情:");
                Debug.Log($"  名称: {emptyBox.name}");
                Debug.Log($"  描述: {emptyBox.description}");
                Debug.Log($"  价值: {emptyBox.value}");
                Debug.Log($"  类型: {emptyBox.contentType}");
                Debug.Log($"  是否正面: {emptyBox.isPositive}");
            }
            else
            {
                Debug.LogWarning("未找到空盒物品数据");
            }
        }

        /// <summary>
        /// 测试成就数据
        /// </summary>
        private void TestAchievementData()
        {
            Debug.Log("--- 测试成就数据 ---");

            var allAchievements = GameDataSystem.Instance.GetAllAchievements();
            Debug.Log($"总共加载了 {allAchievements.Count} 个成就");

            // 按类别统计成就
            var survivalAchievements = GameDataSystem.Instance.GetAchievementsByCategory(AchievementCategory.Survival);
            var blindBoxAchievements = GameDataSystem.Instance.GetAchievementsByCategory(AchievementCategory.BlindBox);
            var socialAchievements = GameDataSystem.Instance.GetAchievementsByCategory(AchievementCategory.Social);
            var storyAchievements = GameDataSystem.Instance.GetAchievementsByCategory(AchievementCategory.Story);

            Debug.Log($"生存类成就: {survivalAchievements.Count} 个");
            Debug.Log($"盲盒类成就: {blindBoxAchievements.Count} 个");
            Debug.Log($"社会类成就: {socialAchievements.Count} 个");
            Debug.Log($"剧情类成就: {storyAchievements.Count} 个");

            // 按稀有度统计成就
            var commonAchievements = allAchievements.Values.Where(a => a.rarity == AchievementRarity.Common).Count();
            var rareAchievements = allAchievements.Values.Where(a => a.rarity == AchievementRarity.Rare).Count();
            var epicAchievements = allAchievements.Values.Where(a => a.rarity == AchievementRarity.Epic).Count();
            var legendaryAchievements = allAchievements.Values.Where(a => a.rarity == AchievementRarity.Legendary).Count();

            Debug.Log($"普通成就: {commonAchievements} 个");
            Debug.Log($"稀有成就: {rareAchievements} 个");
            Debug.Log($"史诗成就: {epicAchievements} 个");
            Debug.Log($"传奇成就: {legendaryAchievements} 个");

            // 测试特定成就获取
            var firstWeekAchievement = GameDataSystem.Instance.GetAchievement("first_week");
            if (firstWeekAchievement != null)
            {
                Debug.Log($"成就详情:");
                Debug.Log($"  名称: {firstWeekAchievement.name}");
                Debug.Log($"  描述: {firstWeekAchievement.description}");
                Debug.Log($"  类别: {firstWeekAchievement.category}");
                Debug.Log($"  稀有度: {firstWeekAchievement.rarity}");
                Debug.Log($"  是否隐藏: {firstWeekAchievement.isHidden}");
                Debug.Log($"  条件数量: {firstWeekAchievement.conditions?.Count ?? 0}");
                Debug.Log($"  奖励数量: {firstWeekAchievement.rewards?.Count ?? 0}");
            }
            else
            {
                Debug.LogWarning("未找到'初来乍到'成就数据");
            }

            // 测试进度追踪器
            var allTrackers = GameDataSystem.Instance.GetAllProgressTrackers();
            Debug.Log($"进度追踪器: {allTrackers.Count} 个");
        }

        /// <summary>
        /// 测试游戏平衡配置
        /// </summary>
        private void TestGameBalance()
        {
            Debug.Log("--- 测试游戏平衡配置 ---");

            var gameBalance = GameDataSystem.Instance.GetGameBalance();
            if (gameBalance != null)
            {
                Debug.Log("成功加载游戏平衡配置");

                if (gameBalance.fraudAlgorithm != null)
                {
                    var fraud = gameBalance.fraudAlgorithm;
                    Debug.Log($"欺诈算法配置:");
                    Debug.Log($"  最大概率偏差: {fraud.maxProbabilityDeviation:P1}");
                    Debug.Log($"  成瘾阈值: {fraud.addictionThreshold}");
                    if (fraud.socialClassBias != null)
                    {
                        Debug.Log($"  社会阶层偏见: [{string.Join(", ", fraud.socialClassBias)}]");
                    }
                }

                if (gameBalance.socialClass?.classes != null)
                {
                    Debug.Log($"社会阶层配置: {gameBalance.socialClass.classes.Count} 个阶层");
                    foreach (var socialClass in gameBalance.socialClass.classes)
                    {
                        Debug.Log($"  {socialClass.name} (等级 {socialClass.level}):");
                        Debug.Log($"    信用倍数: {socialClass.creditMultiplier:F1}");
                        Debug.Log($"    特权奖励: {socialClass.privilegeBonus:P1}");
                    }
                }

                if (gameBalance.attributes?.attributes != null)
                {
                    Debug.Log($"属性配置: {gameBalance.attributes.attributes.Count} 个属性");
                    foreach (var attr in gameBalance.attributes.attributes)
                    {
                        Debug.Log($"  {attr.name}: 默认值 {attr.defaultValue}, 范围 [{attr.minValue}, {attr.maxValue}]");
                    }
                }
            }
            else
            {
                Debug.LogWarning("未加载游戏平衡配置");
            }
        }

        /// <summary>
        /// 测试元游戏配置
        /// </summary>
        private void TestMetaGameConfig()
        {
            Debug.Log("--- 测试元游戏配置 ---");

            var metaConfig = GameDataSystem.Instance.GetMetaGameConfig();
            if (metaConfig != null)
            {
                Debug.Log("成功加载元游戏配置");

                if (metaConfig.awakeningMultipliers != null)
                {
                    Debug.Log($"觉醒倍数配置: {metaConfig.awakeningMultipliers.Count} 个");
                    foreach (var multiplier in metaConfig.awakeningMultipliers)
                    {
                        Debug.Log($"  {multiplier.Key}: {multiplier.Value}x");
                    }
                }

                if (metaConfig.phases != null)
                {
                    Debug.Log($"阶段配置: {metaConfig.phases.Count} 个阶段");
                    foreach (var phase in metaConfig.phases)
                    {
                        Debug.Log($"  {phase.name}: 需要觉醒值 {phase.requiredAwakening}");
                    }
                }
            }
            else
            {
                Debug.LogWarning("未加载元游戏配置");
            }
        }

        /// <summary>
        /// 重新加载数据
        /// </summary>
        [ContextMenu("重新加载数据")]
        public void ReloadData()
        {
            if (GameDataSystem.Instance != null)
            {
                GameDataSystem.Instance.ReloadData();
                Debug.Log("数据重新加载完成");
            }
            else
            {
                Debug.LogError("GameDataSystem未初始化");
            }
        }

        /// <summary>
        /// 显示数据统计
        /// </summary>
        [ContextMenu("显示数据统计")]
        public void ShowDataStatistics()
        {
            if (GameDataSystem.Instance == null || !GameDataSystem.Instance.IsDataLoaded())
            {
                Debug.LogError("数据系统未初始化或数据未加载");
                return;
            }

            Debug.Log("=== 数据统计 ===");

            // 盲盒和物品统计
            var allBoxes = GameDataSystem.Instance.GetAllBlindBoxConfigs();
            var allItems = GameDataSystem.Instance.GetAllItems();
            var allAchievements = GameDataSystem.Instance.GetAllAchievements();
            var allTrackers = GameDataSystem.Instance.GetAllProgressTrackers();
            Debug.Log($"盲盒配置: {allBoxes.Count} 个");
            Debug.Log($"物品数据: {allItems.Count} 个");
            Debug.Log($"成就数据: {allAchievements.Count} 个");
            Debug.Log($"进度追踪器: {allTrackers.Count} 个");

            var gameBalance = GameDataSystem.Instance.GetGameBalance();
            if (gameBalance != null)
            {
                Debug.Log("游戏平衡配置: 已加载");
                if (gameBalance.socialClass?.classes != null)
                    Debug.Log($"社会阶层: {gameBalance.socialClass.classes.Count} 个");
                if (gameBalance.attributes?.attributes != null)
                    Debug.Log($"属性配置: {gameBalance.attributes.attributes.Count} 个");
            }
            else
            {
                Debug.Log("游戏平衡配置: 未加载");
            }

            var metaConfig = GameDataSystem.Instance.GetMetaGameConfig();
            if (metaConfig != null)
            {
                Debug.Log("元游戏配置: 已加载");
                if (metaConfig.awakeningMultipliers != null)
                    Debug.Log($"觉醒倍数: {metaConfig.awakeningMultipliers.Count} 个");
            }
            else
            {
                Debug.Log("元游戏配置: 未加载");
            }
        }

        /// <summary>
        /// 测试现有系统集成
        /// </summary>
        [ContextMenu("测试系统集成")]
        public void TestSystemIntegration()
        {
            Debug.Log("--- 测试系统集成 ---");

            // 测试BlindBoxSystem
            try
            {
                var blindBoxSystem = new BlindBoxSystem(GameDataSystem.Instance);
                Debug.Log("BlindBoxSystem创建成功");

                // 创建测试玩家数据
                var playerData = new PlayerData();
                playerData.Initialize();
                playerData.resources.credits = 100;
                playerData.socialClass = 0;

                // 测试开盒
                var result = blindBoxSystem.OpenBlindBox(BlindBoxType.Basic, playerData);
                if (result != null)
                {
                    Debug.Log($"开盒成功！获得: {result.item.name}");
                    Debug.Log($"物品描述: {result.item.description}");
                    Debug.Log($"物品价值: {result.item.value}");
                    Debug.Log($"是否被操控: {result.wasManipulated}");
                }
                else
                {
                    Debug.LogWarning("开盒失败");
                }

                // 清理资源
                blindBoxSystem.Dispose();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"BlindBoxSystem测试失败: {e.Message}");
            }

            // 测试成就系统
            try
            {
                var achievementSystem = new AchievementSystem(GameDataSystem.Instance);
                Debug.Log("AchievementSystem创建成功");

                // 测试成就获取
                var achievements = achievementSystem.GetAllAchievements();
                Debug.Log($"成就系统加载了 {achievements.Count} 个成就");

                // 测试进度更新
                var playerData = new PlayerData();
                playerData.Initialize();

                achievementSystem.UpdateProgress("days_survived", 7f, playerData);
                Debug.Log("成就系统进度更新测试完成");

                // 清理资源
                achievementSystem.Dispose();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"AchievementSystem测试失败: {e.Message}");
            }

            // 测试其他系统
            if (MetaGameSystem.Instance != null)
            {
                Debug.Log("MetaGameSystem已初始化");
            }
            else
            {
                Debug.LogWarning("MetaGameSystem未初始化");
            }
        }
    }
}
