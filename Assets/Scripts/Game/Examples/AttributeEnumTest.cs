using UnityEngine;
using BoxOfFate.Game;

namespace BoxOfFate.Examples
{
    /// <summary>
    /// 属性枚举系统测试
    /// </summary>
    public class AttributeEnumTest : MonoBehaviour
    {
        [Header("测试配置")]
        public bool runTestOnStart = true;
        public bool logDetailedResults = true;

        private void Start()
        {
            if (runTestOnStart)
            {
                RunAttributeEnumTests();
            }
        }

        [ContextMenu("运行属性枚举测试")]
        public void RunAttributeEnumTests()
        {
            Debug.Log("=== 开始属性枚举系统测试 ===");

            TestAttributeUtility();
            TestPlayerAttributesWithEnum();
            TestStringToEnumConversion();
            TestAttributeValidation();

            Debug.Log("=== 属性枚举系统测试完成 ===");
        }

        /// <summary>
        /// 测试AttributeUtility工具类
        /// </summary>
        private void TestAttributeUtility()
        {
            Debug.Log("--- 测试AttributeUtility工具类 ---");

            // 测试枚举到字符串转换
            foreach (var attributeType in AttributeUtility.GetAllAttributeTypes())
            {
                string stringName = AttributeUtility.ToString(attributeType);
                string displayName = AttributeUtility.GetDisplayName(attributeType);
                float defaultValue = AttributeUtility.GetDefaultValue(attributeType);
                float maxValue = AttributeUtility.GetMaxValue(attributeType);

                if (logDetailedResults)
                {
                    Debug.Log($"属性: {attributeType} -> 字符串: {stringName}, 显示名: {displayName}, 默认值: {defaultValue}, 最大值: {maxValue}");
                }
            }

            // 测试字符串到枚举转换
            string[] testStrings = { "health", "energy", "luck", "invalid_attribute" };
            foreach (var testString in testStrings)
            {
                var attributeType = AttributeUtility.FromString(testString);
                bool isValid = AttributeUtility.IsValidAttributeName(testString);
                
                if (logDetailedResults)
                {
                    Debug.Log($"字符串: {testString} -> 枚举: {attributeType}, 有效: {isValid}");
                }
            }

            Debug.Log("AttributeUtility测试完成");
        }

        /// <summary>
        /// 测试PlayerAttributes使用枚举
        /// </summary>
        private void TestPlayerAttributesWithEnum()
        {
            Debug.Log("--- 测试PlayerAttributes枚举接口 ---");

            var playerData = new PlayerData();
            playerData.Initialize();

            // 测试枚举版本的属性操作
            Debug.Log("测试枚举版本的属性操作:");
            
            // 获取初始值
            float initialHealth = playerData.attributes.GetAttributeValue(AttributeType.Health);
            float initialLuck = playerData.attributes.GetAttributeValue(AttributeType.Luck);
            
            Debug.Log($"初始健康值: {initialHealth}, 初始幸运值: {initialLuck}");

            // 修改属性
            playerData.attributes.ModifyAttribute(AttributeType.Health, -20f);
            playerData.attributes.ModifyAttribute(AttributeType.Luck, 2f);

            // 获取修改后的值
            float newHealth = playerData.attributes.GetAttributeValue(AttributeType.Health);
            float newLuck = playerData.attributes.GetAttributeValue(AttributeType.Luck);

            Debug.Log($"修改后健康值: {newHealth}, 修改后幸运值: {newLuck}");

            // 测试设置属性值
            playerData.attributes.SetAttributeValue(AttributeType.Energy, 75f);
            float energyValue = playerData.attributes.GetAttributeValue(AttributeType.Energy);
            Debug.Log($"设置精力值为75, 实际值: {energyValue}");

            Debug.Log("PlayerAttributes枚举接口测试完成");
        }

        /// <summary>
        /// 测试字符串到枚举的兼容性
        /// </summary>
        private void TestStringToEnumConversion()
        {
            Debug.Log("--- 测试字符串到枚举兼容性 ---");

            var playerData = new PlayerData();
            playerData.Initialize();

            // 测试字符串版本的属性操作（向后兼容）
            Debug.Log("测试字符串版本的属性操作:");

            float initialSocial = playerData.attributes.GetAttributeValue("social");
            Debug.Log($"初始社交值: {initialSocial}");

            playerData.attributes.ModifyAttribute("social", 15f);
            float newSocial = playerData.attributes.GetAttributeValue("social");
            Debug.Log($"修改后社交值: {newSocial}");

            // 测试无效属性名
            playerData.attributes.ModifyAttribute("invalid_attribute", 10f);
            float invalidValue = playerData.attributes.GetAttributeValue("invalid_attribute");
            Debug.Log($"无效属性值: {invalidValue}");

            Debug.Log("字符串到枚举兼容性测试完成");
        }

        /// <summary>
        /// 测试属性验证
        /// </summary>
        private void TestAttributeValidation()
        {
            Debug.Log("--- 测试属性验证 ---");

            var playerData = new PlayerData();
            playerData.Initialize();

            // 测试属性值限制
            Debug.Log("测试属性值限制:");

            // 测试超出最大值
            playerData.attributes.SetAttributeValue(AttributeType.Health, 150f);
            float clampedHealth = playerData.attributes.GetAttributeValue(AttributeType.Health);
            Debug.Log($"设置健康值为150, 实际值: {clampedHealth} (应该被限制在100)");

            // 测试负值
            playerData.attributes.SetAttributeValue(AttributeType.Luck, -5f);
            float clampedLuck = playerData.attributes.GetAttributeValue(AttributeType.Luck);
            Debug.Log($"设置幸运值为-5, 实际值: {clampedLuck} (应该被限制在0)");

            // 测试幸运值的特殊最大值
            playerData.attributes.SetAttributeValue(AttributeType.Luck, 15f);
            float maxLuck = playerData.attributes.GetAttributeValue(AttributeType.Luck);
            Debug.Log($"设置幸运值为15, 实际值: {maxLuck} (应该被限制在10)");

            Debug.Log("属性验证测试完成");
        }

        /// <summary>
        /// 性能测试
        /// </summary>
        [ContextMenu("运行性能测试")]
        public void RunPerformanceTest()
        {
            Debug.Log("=== 开始性能测试 ===");

            var playerData = new PlayerData();
            playerData.Initialize();

            int iterations = 10000;

            // 测试枚举版本性能
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                playerData.attributes.GetAttributeValue(AttributeType.Health);
                playerData.attributes.ModifyAttribute(AttributeType.Energy, 1f);
            }
            stopwatch.Stop();
            long enumTime = stopwatch.ElapsedMilliseconds;

            // 测试字符串版本性能
            stopwatch.Restart();
            for (int i = 0; i < iterations; i++)
            {
                playerData.attributes.GetAttributeValue("health");
                playerData.attributes.ModifyAttribute("energy", 1f);
            }
            stopwatch.Stop();
            long stringTime = stopwatch.ElapsedMilliseconds;

            Debug.Log($"性能测试结果 ({iterations} 次迭代):");
            Debug.Log($"枚举版本: {enumTime}ms");
            Debug.Log($"字符串版本: {stringTime}ms");
            Debug.Log($"性能提升: {(float)stringTime / enumTime:F2}x");

            Debug.Log("=== 性能测试完成 ===");
        }
    }
}
