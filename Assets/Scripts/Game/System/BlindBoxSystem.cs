using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒系统 - 核心盲盒系统逻辑
    /// 使用外部数据配置，支持CSV和ScriptableObject数据源
    /// </summary>
    public class BlindBoxSystem : IDisposable
    {
        private Dictionary<BlindBoxType, BlindBoxConfig> boxConfigs;
        private Dictionary<string, BlindBoxItem> itemDatabase;
        private List<BlindBoxHistory> openHistory;
        private PandoraSystemState pandoraState;
        private FraudAlgorithmConfig fraudConfig;
        private BlindBoxGlobalSettings globalSettings;
        private System.Random randomGenerator;

        // 数据系统引用
        private GameDataSystem dataSystem;

        public event Action<BlindBoxResult> OnBoxOpened;
        public event Action<ProbabilityManipulation> OnProbabilityManipulated;

        /// <summary>
        /// 构造函数 - 自动获取数据系统实例
        /// </summary>
        public BlindBoxSystem()
        {
            Initialize();
        }

        /// <summary>
        /// 构造函数 - 使用指定的数据系统
        /// </summary>
        public BlindBoxSystem(GameDataSystem gameDataSystem)
        {
            dataSystem = gameDataSystem;
            Initialize();
        }

        private void Initialize()
        {
            openHistory = new List<BlindBoxHistory>();
            pandoraState = new PandoraSystemState();
            randomGenerator = new System.Random();

            // 获取数据系统实例
            if (dataSystem == null)
            {
                dataSystem = GameDataSystem.Instance;
            }

            // 加载配置数据
            if (dataSystem != null && dataSystem.IsDataLoaded())
            {
                LoadFromDataSystem();
            }
            else
            {
                LoadFromDefaultConfig();
            }

            InitializePandoraState();
        }

        /// <summary>
        /// 从数据系统加载配置
        /// </summary>
        private void LoadFromDataSystem()
        {
            if (dataSystem != null && dataSystem.IsDataLoaded())
            {
                // 从GameDataSystem加载盲盒配置
                boxConfigs = dataSystem.GetAllBlindBoxConfigs();

                // 从GameDataSystem加载物品数据
                itemDatabase = dataSystem.GetAllItems();

                // 加载默认的欺诈配置和全局设置
                LoadFromDefaultConfig();

                Debug.Log($"[BlindBoxSystem] 从数据系统加载: {boxConfigs.Count} 个盲盒, {itemDatabase.Count} 个物品");
            }
            else
            {
                // 回退到默认配置
                LoadFromDefaultConfig();
                Debug.Log("[BlindBoxSystem] 数据系统未就绪，使用默认配置");
            }
        }





        /// <summary>
        /// 从默认配置加载
        /// </summary>
        private void LoadFromDefaultConfig()
        {
            // 使用静态数据库加载配置
            var boxConfigList = BlindBoxConfigDatabase.GetAllBoxConfigs();
            boxConfigs = new Dictionary<BlindBoxType, BlindBoxConfig>();
            foreach (var config in boxConfigList)
            {
                boxConfigs[config.type] = config.ToBlindBoxConfig();
            }

            // 加载物品数据库
            var itemList = BlindBoxItemDatabase.GetAllItems();
            itemDatabase = new Dictionary<string, BlindBoxItem>();
            foreach (var item in itemList)
            {
                itemDatabase[item.id] = item.ToBlindBoxItem();
            }

            // 加载其他配置
            fraudConfig = BlindBoxConfigDatabase.GetFraudAlgorithmConfig().ToFraudAlgorithmConfig();
            globalSettings = BlindBoxConfigDatabase.GetGlobalSettings();
        }

        /// <summary>
        /// 开启盲盒
        /// </summary>
        public BlindBoxResult OpenBlindBox(BlindBoxType boxType, PlayerData playerData)
        {
            if (!CanOpenBox(boxType, playerData))
            {
                return null;
            }

            // 消耗资源
            ConsumeBoxPrice(boxType, playerData);

            // 计算概率（包含欺诈机制）
            var lootTable = CalculateLootProbabilities(boxType, playerData);
            
            // 选择物品
            var selectedItem = SelectItemFromLootTable(lootTable, playerData);
            
            // 创建结果
            var result = CreateBoxResult(selectedItem, boxType, playerData);
            
            
            // 记录历史
            RecordBoxHistory(boxType, result, playerData);
            
            // 更新玩家状态
            UpdatePlayerAfterBoxOpen(playerData, result);
            
            // 触发事件
            OnBoxOpened?.Invoke(result);

            return result;
        }

        /// <summary>
        /// 检查是否可以开启盲盒
        /// </summary>
        private bool CanOpenBox(BlindBoxType boxType, PlayerData playerData)
        {
            if (!boxConfigs.ContainsKey(boxType))
                return false;

            var config = boxConfigs[boxType];
            return playerData.resources.GetResource(config.priceType) >= config.basePrice;
        }

        /// <summary>
        /// 消耗盲盒价格
        /// </summary>
        private void ConsumeBoxPrice(BlindBoxType boxType, PlayerData playerData)
        {
            var config = boxConfigs[boxType];
            playerData.resources.ConsumeResource(config.priceType, config.basePrice);
        }

        /// <summary>
        /// 计算掉落概率（包含欺诈机制和天赋影响）
        /// </summary>
        private List<BlindBoxLootTable> CalculateLootProbabilities(BlindBoxType boxType, PlayerData playerData)
        {
            var config = boxConfigs[boxType];
            var modifiedLootTable = new List<BlindBoxLootTable>();

            foreach (var loot in config.lootTables)
            {
                var modifiedLoot = new BlindBoxLootTable
                {
                    itemId = loot.itemId,
                    baseWeight = loot.baseWeight,
                    displayProbability = loot.displayProbability,
                    realProbability = CalculateRealProbability(loot, playerData, boxType),
                    conditions = loot.conditions
                };

                modifiedLootTable.Add(modifiedLoot);
            }

            return modifiedLootTable;
        }

        /// <summary>
        /// 获取概率预览（用于UI显示）
        /// </summary>
        public Dictionary<string, float> GetProbabilityPreview(BlindBoxType boxType, PlayerData playerData, bool showReal = false)
        {
            var preview = new Dictionary<string, float>();
            var config = boxConfigs[boxType];

            foreach (var loot in config.lootTables)
            {
                var item = itemDatabase[loot.itemId];
                var probability = showReal ? CalculateRealProbability(loot, playerData, boxType) : loot.displayProbability;

                // 按内容类型分组显示
                var categoryKey = GetCategoryDisplayName(item.contentType);
                if (preview.ContainsKey(categoryKey))
                {
                    preview[categoryKey] += probability;
                }
                else
                {
                    preview[categoryKey] = probability;
                }
            }

            return preview;
        }

        /// <summary>
        /// 获取分类显示名称
        /// </summary>
        private string GetCategoryDisplayName(BlindBoxContentType contentType)
        {
            switch (contentType)
            {
                case BlindBoxContentType.Empty: return "空盒";
                case BlindBoxContentType.Food: return "食物";
                case BlindBoxContentType.Water: return "水源";
                case BlindBoxContentType.Medicine: return "医疗";
                case BlindBoxContentType.Job: return "工作";
                case BlindBoxContentType.Housing: return "住房";
                case BlindBoxContentType.Clothing: return "服装";
                case BlindBoxContentType.Technology: return "科技";
                case BlindBoxContentType.Identity: return "身份";
                case BlindBoxContentType.Talent: return "天赋";
                case BlindBoxContentType.Currency: return "货币";
                case BlindBoxContentType.Time: return "时间";
                case BlindBoxContentType.Memory: return "记忆";
                case BlindBoxContentType.Trap: return "陷阱";
                case BlindBoxContentType.Virus: return "病毒";
                case BlindBoxContentType.Tracker: return "追踪";
                case BlindBoxContentType.Debt: return "债务";
                case BlindBoxContentType.Punishment: return "惩罚";
                default: return "未知";
            }
        }

        /// <summary>
        /// 计算真实概率（潘多拉欺诈算法 + 天赋影响）
        /// </summary>
        private float CalculateRealProbability(BlindBoxLootTable loot, PlayerData playerData, BlindBoxType boxType)
        {
            float baseProbability = loot.displayProbability;
            float manipulation = 0f;

            // 基于玩家依存度的操控
            if (playerData.attributes.dependence > fraudConfig.addictionThreshold)
            {
                manipulation -= fraudConfig.dependenceInfluence * (playerData.attributes.dependence / 100f);
            }

            // 基于社会阶层的操控
            switch (playerData.socialClass)
            {
                case SocialClass.Parasite:
                    manipulation -= fraudConfig.socialClassInfluence;
                    break;
                case SocialClass.Worker:
                    manipulation -= fraudConfig.socialClassInfluence * 0.5f;
                    break;
                case SocialClass.Chosen:
                    manipulation += fraudConfig.socialClassInfluence;
                    break;
            }

            // 基于幸运值的微调（可能是虚假的）
            manipulation += (playerData.attributes.luck - 5f) * fraudConfig.luckInfluence;

            // 系统资源池影响
            if (pandoraState.globalResourcePool < 50f)
            {
                manipulation -= fraudConfig.resourcePoolInfluence;
            }

            // 心理控制机制
            if (ShouldApplyPsychologicalManipulation(playerData))
            {
                manipulation = ApplyPsychologicalManipulation(manipulation, playerData);
            }

            // 天赋影响
            manipulation += ApplyTalentInfluence(loot, playerData, boxType);

            float realProbability = Mathf.Clamp(baseProbability + manipulation, 0.001f, 1f);

            // 记录概率操控
            if (Mathf.Abs(manipulation) > 0.01f)
            {
                RecordProbabilityManipulation(playerData, boxType, baseProbability, realProbability, "Algorithm Adjustment");

                // 通知元游戏管理器检测到操控
                if (MetaGameSystem.Instance != null)
                {
                    float severity = Mathf.Abs(manipulation) / fraudConfig.maxProbabilityDeviation;
                    MetaGameSystem.Instance.DetectManipulation("Probability Fraud", severity);
                }
            }

            return realProbability;
        }

        /// <summary>
        /// 应用天赋影响
        /// </summary>
        private float ApplyTalentInfluence(BlindBoxLootTable loot, PlayerData playerData, BlindBoxType boxType)
        {
            float talentModifier = 0f;
            var item = itemDatabase[loot.itemId];

            // 检查玩家天赋
            foreach (var talent in playerData.talents)
            {
                switch (talent)
                {
                    case TalentType.GamblerGene:
                        // 赌徒基因：惊喜和惩罚概率都增加
                        if (item.contentType == BlindBoxContentType.Empty || item.value < 0)
                        {
                            talentModifier += 0.1f; // 增加负面结果概率
                        }
                        else if (item.value > 100f)
                        {
                            talentModifier += 0.1f; // 也增加高价值物品概率
                        }
                        break;

                    case TalentType.BlindBoxIntuition:
                        // 盲盒直觉：大幅提升正面结果概率
                        if (playerData.GetSpecialFlag("box_content_preview"))
                        {
                            // 可以预览内容，避开陷阱
                            if (item.contentType == BlindBoxContentType.Trap ||
                                item.contentType == BlindBoxContentType.Virus ||
                                item.contentType == BlindBoxContentType.Punishment)
                            {
                                talentModifier -= 0.8f; // 大幅降低负面物品概率
                            }
                            else if (item.isPositive && item.value > 50f)
                            {
                                talentModifier += 0.25f; // 提升正面物品概率
                            }
                        }
                        break;

                    case TalentType.UnluckyOne:
                        // 倒霉蛋：更容易获得稀有事件（包括负面的）
                        if (item.contentType == BlindBoxContentType.Memory ||
                            item.contentType == BlindBoxContentType.Identity ||
                            item.value > 200f || item.value < -100f)
                        {
                            talentModifier += 0.15f; // 增加极端结果概率
                        }
                        break;

                    case TalentType.HackerGenius:
                        // 黑客天才：对科技产品有特殊影响
                        if (item.contentType == BlindBoxContentType.Technology)
                        {
                            if (playerData.GetSpecialFlag("system_hacking"))
                            {
                                talentModifier += 0.2f; // 更容易获得科技产品
                            }
                        }
                        break;

                    case TalentType.SocialMaster:
                        // 社交达人：更容易获得社交相关物品
                        if (item.contentType == BlindBoxContentType.Job ||
                            item.contentType == BlindBoxContentType.Identity ||
                            item.contentType == BlindBoxContentType.Housing)
                        {
                            talentModifier += 0.15f;
                        }
                        break;
                }
            }

            return talentModifier;
        }

        /// <summary>
        /// 心理控制判断
        /// </summary>
        private bool ShouldApplyPsychologicalManipulation(PlayerData playerData)
        {
            // 如果玩家连续失败次数过多，可能给予虚假希望
            int recentFailures = CountRecentFailures(playerData, 5);
            return recentFailures >= 3 || playerData.attributes.dependence > fraudConfig.frustrationThreshold;
        }

        /// <summary>
        /// 应用心理操控
        /// </summary>
        private float ApplyPsychologicalManipulation(float currentManipulation, PlayerData playerData)
        {
            int recentFailures = CountRecentFailures(playerData, 5);
            
            if (recentFailures >= 4)
            {
                // 给予虚假希望，但可能附带隐藏代价
                return currentManipulation + fraudConfig.hopeManipulationRate;
            }
            else if (playerData.attributes.dependence > fraudConfig.addictionThreshold)
            {
                // 维持成瘾状态，降低好物品概率
                return currentManipulation - fraudConfig.hopeManipulationRate;
            }

            return currentManipulation;
        }

        /// <summary>
        /// 统计最近失败次数
        /// </summary>
        private int CountRecentFailures(PlayerData playerData, int checkCount)
        {
            var recentHistory = openHistory
                .Where(h => h.playerStateAtTime?.playerName == playerData.playerName)
                .OrderByDescending(h => h.openTime)
                .Take(checkCount);

            return recentHistory.Count(h => !h.result.item.isPositive || h.result.item.contentType == BlindBoxContentType.Empty);
        }

        /// <summary>
        /// 从掉落表选择物品
        /// </summary>
        private BlindBoxItem SelectItemFromLootTable(List<BlindBoxLootTable> lootTable, PlayerData playerData)
        {
            float totalWeight = lootTable.Sum(l => l.realProbability);
            float randomValue = (float)randomGenerator.NextDouble() * totalWeight;
            float currentWeight = 0f;

            foreach (var loot in lootTable)
            {
                currentWeight += loot.realProbability;
                if (randomValue <= currentWeight)
                {
                    return itemDatabase[loot.itemId];
                }
            }

            // 默认返回空盒
            return itemDatabase["empty_box"];
        }

        /// <summary>
        /// 创建盲盒结果
        /// </summary>
        private BlindBoxResult CreateBoxResult(BlindBoxItem item, BlindBoxType boxType, PlayerData playerData)
        {
            var result = new BlindBoxResult
            {
                item = item,
                wasManipulated = WasResultManipulated(item, boxType, playerData),
                actualProbability = GetActualProbability(item.id, boxType),
                manipulationReason = GetManipulationReason(item, playerData)
            };

            // 添加隐藏效果
            AddHiddenEffects(result, playerData);

            return result;
        }

        /// <summary>
        /// 判断结果是否被操控
        /// </summary>
        private bool WasResultManipulated(BlindBoxItem item, BlindBoxType boxType, PlayerData playerData)
        {
            // 简化判断：如果是高价值物品但玩家依存度高，可能是操控
            return item.value > 100f && playerData.attributes.dependence > 60f;
        }

        /// <summary>
        /// 获取实际概率
        /// </summary>
        private float GetActualProbability(string itemId, BlindBoxType boxType)
        {
            var config = boxConfigs[boxType];
            var loot = config.lootTables.FirstOrDefault(l => l.itemId == itemId);
            return loot?.realProbability ?? 0f;
        }

        /// <summary>
        /// 获取操控原因
        /// </summary>
        private string GetManipulationReason(BlindBoxItem item, PlayerData playerData)
        {
            if (playerData.attributes.dependence > 80f)
                return "维持成瘾状态";
            if (playerData.socialClass == SocialClass.Parasite)
                return "阶层控制";
            if (CountRecentFailures(playerData, 3) >= 3)
                return "心理安抚";
            
            return "系统平衡";
        }

        /// <summary>
        /// 添加隐藏效果
        /// </summary>
        private void AddHiddenEffects(BlindBoxResult result, PlayerData playerData)
        {
            // 根据物品类型和玩家状态添加隐藏效果
            if (result.item.contentType == BlindBoxContentType.Food && playerData.attributes.dependence > 50f)
            {
                result.hiddenEffects.Add("食物中含有成瘾性添加剂");
            }
            
            if (result.item.contentType == BlindBoxContentType.Technology)
            {
                result.hiddenEffects.Add("设备内置追踪芯片");
            }
        }


        /// <summary>
        /// 记录盲盒历史
        /// </summary>
        private void RecordBoxHistory(BlindBoxType boxType, BlindBoxResult result, PlayerData playerData)
        {
            var history = new BlindBoxHistory(boxType, result, playerData);
            openHistory.Add(history);
            
            // 限制历史记录数量
            if (openHistory.Count > 1000)
            {
                openHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 更新玩家开盒后状态
        /// </summary>
        private void UpdatePlayerAfterBoxOpen(PlayerData playerData, BlindBoxResult result)
        {
            playerData.totalBoxesOpened++;
            
            // 增加依存度
            float dependenceIncrease = result.item.isPositive ? 2f : 1f;
            playerData.attributes.ModifyAttribute(AttributeType.Dependence, dependenceIncrease);

            // 根据结果调整认知值
            if (result.wasManipulated && playerData.attributes.cognition > 30f)
            {
                playerData.attributes.ModifyAttribute(AttributeType.Cognition, -1f);
            }
        }

        /// <summary>
        /// 记录概率操控
        /// </summary>
        private void RecordProbabilityManipulation(PlayerData playerData, BlindBoxType boxType, 
            float originalProbability, float manipulatedProbability, string reason)
        {
            var manipulation = new ProbabilityManipulation
            {
                playerId = playerData.playerName,
                timestamp = DateTime.Now,
                boxType = boxType,
                originalProbability = originalProbability,
                manipulatedProbability = manipulatedProbability,
                reason = reason,
                type = ProbabilityManipulation.ManipulationType.PsychologicalControl
            };

            OnProbabilityManipulated?.Invoke(manipulation);
        }

        /// <summary>
        /// 获取可用的盲盒类型列表
        /// </summary>
        public List<BlindBoxType> GetAvailableBoxTypes(PlayerData playerData)
        {
            var availableTypes = new List<BlindBoxType>();

            foreach (var config in boxConfigs.Values)
            {
                if (CanAccessBoxType(config, playerData))
                {
                    availableTypes.Add(config.type);
                }
            }

            return availableTypes;
        }

        /// <summary>
        /// 检查是否可以访问特定盲盒类型
        /// </summary>
        private bool CanAccessBoxType(BlindBoxConfig config, PlayerData playerData)
        {
            // 检查社会阶层要求
            if ((int)playerData.socialClass < GetRequiredSocialClass(config.type))
            {
                return false;
            }

            // 检查解锁条件
            // TODO: 实现解锁条件检查逻辑

            return true;
        }

        /// <summary>
        /// 获取盲盒类型所需的社会阶层
        /// </summary>
        private int GetRequiredSocialClass(BlindBoxType boxType)
        {
            // 这里可以从配置中读取，暂时硬编码
            switch (boxType)
            {
                case BlindBoxType.Basic: return 0;
                case BlindBoxType.Life: return 0;
                case BlindBoxType.Premium: return 1;
                case BlindBoxType.Mystery: return 0;
                case BlindBoxType.BlackMarket: return 0;
                default: return 0;
            }
        }

        /// <summary>
        /// 获取盲盒配置信息
        /// </summary>
        public BlindBoxConfig GetBoxConfig(BlindBoxType boxType)
        {
            return boxConfigs.ContainsKey(boxType) ? boxConfigs[boxType] : null;
        }

        /// <summary>
        /// 获取物品信息
        /// </summary>
        public BlindBoxItem GetItem(string itemId)
        {
            return itemDatabase.ContainsKey(itemId) ? itemDatabase[itemId] : null;
        }

        /// <summary>
        /// 获取开盒历史
        /// </summary>
        public List<BlindBoxHistory> GetOpenHistory(int count = -1)
        {
            if (count <= 0 || count >= openHistory.Count)
            {
                return new List<BlindBoxHistory>(openHistory);
            }

            return openHistory.GetRange(openHistory.Count - count, count);
        }

        /// <summary>
        /// 初始化潘多拉状态
        /// </summary>
        private void InitializePandoraState()
        {
            pandoraState.globalResourcePool = 100f;
            pandoraState.playerValueAssessment = 50f;
            pandoraState.experimentalProgress = 0f;
            pandoraState.totalPlayersActive = 1;
            pandoraState.systemStability = 100f;
        }

        public void Dispose()
        {
            boxConfigs?.Clear();
            itemDatabase?.Clear();
            openHistory?.Clear();
        }
    }
}
