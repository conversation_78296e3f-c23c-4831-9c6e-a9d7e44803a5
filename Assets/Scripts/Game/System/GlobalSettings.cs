using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{

    /// <summary>
    /// 盲盒全局设置 - 控制盲盒系统的全局行为参数
    /// </summary>
    [Serializable]
    public class GlobalSettings
    {
        [Header("历史记录")]
        public int maxHistoryRecords = 1000;
        public bool enableDetailedLogging = true;

        [Header("概率显示")]
        public bool showRealProbabilities = false;
        public bool enableProbabilityHints = true;

        [Header("动画设置")]
        public float openAnimationDuration = 2f;
        public bool enableParticleEffects = true;

        [Header("音效设置")]
        public bool enableSoundEffects = true;
        public float soundVolume = 1f;

        [Header("调试设置")]
        public bool enableDebugMode = false;
        public bool forceShowAllProbabilities = false;

        /// <summary>
        /// 获取默认设置
        /// </summary>
        public static GlobalSettings GetDefault()
        {
            return new GlobalSettings
            {
                maxHistoryRecords = 1000,
                enableDetailedLogging = true,
                showRealProbabilities = false,
                enableProbabilityHints = true,
                openAnimationDuration = 2f,
                enableParticleEffects = true,
                enableSoundEffects = true,
                soundVolume = 1f,
                enableDebugMode = false,
                forceShowAllProbabilities = false
            };
        }

        /// <summary>
        /// 验证设置的有效性
        /// </summary>
        public bool IsValid()
        {
            return maxHistoryRecords > 0 &&
                   openAnimationDuration > 0f &&
                   soundVolume >= 0f && soundVolume <= 1f;
        }

        /// <summary>
        /// 应用开发者模式设置
        /// </summary>
        public void ApplyDeveloperMode(bool enabled)
        {
            enableDebugMode = enabled;
            showRealProbabilities = enabled;
            forceShowAllProbabilities = enabled;
            enableDetailedLogging = enabled;
        }
    }
}
