using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 黑市交易系统 - 管理非法交易和特殊物品
    /// </summary>
    public class BlackMarketSystem : IDisposable
    {
        private List<BlackMarketListing> availableListings;
        private List<BlackMarketTransaction> transactionHistory;
        private Dictionary<string, BlackMarketVendor> vendors;
        private float riskLevel = 0f;
        private float systemSuspicion = 0f;

        public event Action<BlackMarketTransaction> OnTransactionCompleted;
        public event Action<float> OnRiskLevelChanged;
        public event Action<string> OnSystemAlert;

        public BlackMarketSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            availableListings = new List<BlackMarketListing>();
            transactionHistory = new List<BlackMarketTransaction>();
            vendors = new Dictionary<string, BlackMarketVendor>();
            
            InitializeVendors();
            GenerateInitialListings();
        }

        /// <summary>
        /// 进入黑市
        /// </summary>
        public BlackMarketAccess AccessBlackMarket(PlayerData playerData)
        {
            var access = new BlackMarketAccess();

            // 检查访问条件
            if (!CanAccessBlackMarket(playerData))
            {
                access.canAccess = false;
                access.reason = GetAccessDeniedReason(playerData);
                return access;
            }

            access.canAccess = true;
            access.availableListings = GetAvailableListings(playerData);
            access.currentRiskLevel = riskLevel;
            access.playerReputation = GetPlayerReputation(playerData);

            // 增加系统怀疑度
            IncreaseSystemSuspicion(playerData, 5f);

            return access;
        }

        /// <summary>
        /// 执行交易
        /// </summary>
        public BlackMarketTransaction ExecuteTransaction(PlayerData playerData, string listingId, 
            ResourceType paymentType, float paymentAmount)
        {
            var listing = availableListings.FirstOrDefault(l => l.id == listingId);
            if (listing == null)
            {
                return new BlackMarketTransaction
                {
                    success = false,
                    errorMessage = "交易项目不存在"
                };
            }

            // 检查支付能力
            if (!CanAffordTransaction(playerData, paymentType, paymentAmount))
            {
                return new BlackMarketTransaction
                {
                    success = false,
                    errorMessage = "资源不足"
                };
            }

            // 计算交易风险
            float transactionRisk = CalculateTransactionRisk(playerData, listing);
            bool transactionSuccess = UnityEngine.Random.Range(0f, 1f) > transactionRisk;

            var transaction = new BlackMarketTransaction
            {
                id = Guid.NewGuid().ToString(),
                playerId = playerData.playerName,
                listingId = listingId,
                item = listing.item,
                paymentType = paymentType,
                paymentAmount = paymentAmount,
                riskLevel = transactionRisk,
                timestamp = DateTime.Now,
                success = transactionSuccess
            };

            if (transactionSuccess)
            {
                // 执行成功的交易
                ProcessSuccessfulTransaction(playerData, listing, transaction);
            }
            else
            {
                // 处理失败的交易
                ProcessFailedTransaction(playerData, listing, transaction);
            }

            // 记录交易历史
            transactionHistory.Add(transaction);
            
            // 更新风险等级
            UpdateRiskLevel(transactionRisk);
            
            OnTransactionCompleted?.Invoke(transaction);
            return transaction;
        }

        /// <summary>
        /// 检查是否可以访问黑市
        /// </summary>
        private bool CanAccessBlackMarket(PlayerData playerData)
        {
            // 基础条件：认知值足够高
            if (playerData.attributes.cognition < 40f)
                return false;

            // 不能被系统严密监控
            if (playerData.HasStatusEffect(StatusEffectType.SystemTracking) && 
                systemSuspicion > 80f)
                return false;

            // 需要一定的道德值下限（或者足够低的道德值）
            if (playerData.attributes.morality > 80f && playerData.attributes.morality < 20f)
                return false;

            return true;
        }

        /// <summary>
        /// 获取访问被拒绝的原因
        /// </summary>
        private string GetAccessDeniedReason(PlayerData playerData)
        {
            if (playerData.attributes.cognition < 40f)
                return "认知水平不足，无法发现黑市入口";
            
            if (playerData.HasStatusEffect(StatusEffectType.SystemTracking) && systemSuspicion > 80f)
                return "系统监控过于严密，无法安全进入";
            
            return "当前状态不适合进入黑市";
        }

        /// <summary>
        /// 获取可用的交易列表
        /// </summary>
        private List<BlackMarketListing> GetAvailableListings(PlayerData playerData)
        {
            var available = new List<BlackMarketListing>();

            foreach (var listing in availableListings)
            {
                // 检查玩家是否满足交易条件
                if (MeetsListingRequirements(playerData, listing))
                {
                    available.Add(listing);
                }
            }

            return available;
        }

        /// <summary>
        /// 检查是否满足交易条件
        /// </summary>
        private bool MeetsListingRequirements(PlayerData playerData, BlackMarketListing listing)
        {
            // 检查社会阶层
            if ((int)playerData.socialClass < (int)listing.minimumSocialClass)
                return false;

            // 检查声誉
            if (GetPlayerReputation(playerData) < listing.minimumReputation)
                return false;

            // 检查特殊条件
            foreach (var condition in listing.conditions)
            {
                if (!CheckListingCondition(playerData, condition))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 检查交易条件
        /// </summary>
        private bool CheckListingCondition(PlayerData playerData, ListingCondition condition)
        {
            switch (condition.type)
            {
                case ListingConditionType.HasTalent:
                    return playerData.HasTalent(condition.talentType.Value);
                
                case ListingConditionType.AttributeThreshold:
                    return GetAttributeValue(playerData, condition.attributeType) >= condition.value;
                
                case ListingConditionType.CompletedEvent:
                    return playerData.unlockedEvents.Contains(condition.eventId);
                
                case ListingConditionType.NoSystemTracking:
                    return !playerData.HasStatusEffect(StatusEffectType.SystemTracking);
                
                default:
                    return true;
            }
        }

        /// <summary>
        /// 计算交易风险
        /// </summary>
        private float CalculateTransactionRisk(PlayerData playerData, BlackMarketListing listing)
        {
            float risk = listing.baseRisk;

            // 系统怀疑度影响
            risk += systemSuspicion * 0.01f;

            // 玩家状态影响
            if (playerData.HasStatusEffect(StatusEffectType.SystemTracking))
            {
                risk += 0.3f;
            }

            // 社会阶层影响
            switch (playerData.socialClass)
            {
                case SocialClass.Parasite:
                    risk += 0.1f; // 底层更容易被发现
                    break;
                case SocialClass.Chosen:
                    risk -= 0.2f; // 顶层有更多保护
                    break;
            }

            // 天赋影响
            if (playerData.HasTalent(TalentType.HackerGenius))
            {
                risk -= 0.25f;
            }

            return Mathf.Clamp(risk, 0.05f, 0.95f);
        }

        /// <summary>
        /// 处理成功的交易
        /// </summary>
        private void ProcessSuccessfulTransaction(PlayerData playerData, BlackMarketListing listing, 
            BlackMarketTransaction transaction)
        {
            // 扣除支付资源
            playerData.resources.ConsumeResource(transaction.paymentType, transaction.paymentAmount);

            // 给予物品
            ApplyItemEffects(playerData, listing.item);

            // 增加黑市声誉
            IncreasePlayerReputation(playerData, listing.reputationReward);

            transaction.resultMessage = $"成功获得 {listing.item.name}";
        }

        /// <summary>
        /// 处理失败的交易
        /// </summary>
        private void ProcessFailedTransaction(PlayerData playerData, BlackMarketListing listing, 
            BlackMarketTransaction transaction)
        {
            // 部分资源损失
            float lossAmount = transaction.paymentAmount * 0.5f;
            playerData.resources.ConsumeResource(transaction.paymentType, lossAmount);

            // 增加系统怀疑
            IncreaseSystemSuspicion(playerData, 20f);

            // 可能被追踪
            if (UnityEngine.Random.Range(0f, 1f) < 0.3f)
            {
                var tracking = new StatusEffect(StatusEffectType.SystemTracking, 1f, 7, "黑市交易被发现");
                playerData.AddStatusEffect(tracking);
            }

            transaction.errorMessage = "交易失败，部分资源损失，可能被系统发现";
        }

        /// <summary>
        /// 应用物品效果
        /// </summary>
        private void ApplyItemEffects(PlayerData playerData, BlindBoxItem item)
        {
            foreach (var effect in item.effects)
            {
                if (effect.effectType == ItemEffectType.AttributeChange)
                {
                    playerData.attributes.ModifyAttribute(effect.targetAttribute, effect.value);
                }
                
                if (effect.resourceType.HasValue)
                {
                    playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                }
                
                if (effect.statusEffect.HasValue)
                {
                    var statusEffect = new StatusEffect(effect.statusEffect.Value, effect.value, effect.statusDuration);
                    playerData.AddStatusEffect(statusEffect);
                }
            }
        }

        /// <summary>
        /// 检查支付能力
        /// </summary>
        private bool CanAffordTransaction(PlayerData playerData, ResourceType paymentType, float amount)
        {
            return playerData.resources.GetResource(paymentType) >= amount;
        }

        /// <summary>
        /// 获取玩家声誉
        /// </summary>
        private float GetPlayerReputation(PlayerData playerData)
        {
            // 基于成功交易次数和特殊行为计算声誉
            int successfulTrades = transactionHistory.Count(t => t.success && t.playerId == playerData.playerName);
            float baseReputation = successfulTrades * 10f;

            // 天赋加成
            if (playerData.HasTalent(TalentType.HackerGenius))
            {
                baseReputation += 50f;
            }

            return baseReputation;
        }

        /// <summary>
        /// 增加玩家声誉
        /// </summary>
        private void IncreasePlayerReputation(PlayerData playerData, float amount)
        {
            // 这里可以添加声誉系统的具体实现
            Debug.Log($"玩家 {playerData.playerName} 黑市声誉增加 {amount}");
        }

        /// <summary>
        /// 增加系统怀疑度
        /// </summary>
        private void IncreaseSystemSuspicion(PlayerData playerData, float amount)
        {
            systemSuspicion = Mathf.Clamp(systemSuspicion + amount, 0f, 100f);
            
            if (systemSuspicion > 70f)
            {
                OnSystemAlert?.Invoke("系统怀疑度过高，建议暂时避免黑市活动");
            }
        }

        /// <summary>
        /// 更新风险等级
        /// </summary>
        private void UpdateRiskLevel(float transactionRisk)
        {
            riskLevel = Mathf.Lerp(riskLevel, transactionRisk, 0.1f);
            OnRiskLevelChanged?.Invoke(riskLevel);
        }

        /// <summary>
        /// 获取属性值
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 初始化黑市商贩
        /// </summary>
        private void InitializeVendors()
        {
            vendors["shadow_dealer"] = new BlackMarketVendor
            {
                id = "shadow_dealer",
                name = "影子商人",
                description = "神秘的商人，专门交易禁忌物品",
                trustLevel = 0.7f,
                specialties = new List<BlindBoxContentType> 
                { 
                    BlindBoxContentType.Technology, 
                    BlindBoxContentType.Memory 
                }
            };

            vendors["data_broker"] = new BlackMarketVendor
            {
                id = "data_broker",
                name = "数据掮客",
                description = "买卖各种机密信息",
                trustLevel = 0.5f,
                specialties = new List<BlindBoxContentType> 
                { 
                    BlindBoxContentType.Technology,
                    BlindBoxContentType.Talent 
                }
            };
        }

        /// <summary>
        /// 生成初始交易列表
        /// </summary>
        private void GenerateInitialListings()
        {
            // 清醒药剂
            availableListings.Add(new BlackMarketListing
            {
                id = "awakening_serum",
                vendorId = "shadow_dealer",
                item = CreateSpecialItem("awakening_serum", "清醒药剂", "暂时免疫概率欺诈的神秘药剂"),
                baseRisk = 0.4f,
                minimumSocialClass = SocialClass.Parasite,
                minimumReputation = 0f,
                reputationReward = 20f,
                conditions = new List<ListingCondition>()
            });

            // 系统代码片段
            availableListings.Add(new BlackMarketListing
            {
                id = "system_code_fragment",
                vendorId = "data_broker",
                item = CreateSpecialItem("system_code", "潘多拉代码片段", "系统核心代码的一部分"),
                baseRisk = 0.7f,
                minimumSocialClass = SocialClass.Worker,
                minimumReputation = 50f,
                reputationReward = 50f,
                conditions = new List<ListingCondition>
                {
                    new ListingCondition(ListingConditionType.HasTalent, TalentType.HackerGenius),
                    new ListingCondition(ListingConditionType.AttributeThreshold, AttributeType.Cognition, 70f)
                }
            });
        }

        /// <summary>
        /// 创建特殊物品
        /// </summary>
        private BlindBoxItem CreateSpecialItem(string id, string name, string description)
        {
            var item = new BlindBoxItem(id, name, BlindBoxContentType.Technology, 1000f, true);
            item.description = description;
            
            // 根据物品类型添加特殊效果
            switch (id)
            {
                case "awakening_serum":
                    item.effects.Add(new ItemEffect(AttributeType.Cognition, 20f));
                    item.effects.Add(new ItemEffect(StatusEffectType.CognitiveDamage, -5, 1f)); // 移除认知损伤
                    break;

                case "system_code":
                    item.effects.Add(new ItemEffect(AttributeType.Pollution, 10f)); // 接触系统代码会增加污染度
                    break;
            }
            
            return item;
        }

        public void Dispose()
        {
            availableListings?.Clear();
            transactionHistory?.Clear();
            vendors?.Clear();
        }
    }

    /// <summary>
    /// 黑市访问结果
    /// </summary>
    [Serializable]
    public class BlackMarketAccess
    {
        public bool canAccess;
        public string reason;
        public List<BlackMarketListing> availableListings;
        public float currentRiskLevel;
        public float playerReputation;

        public BlackMarketAccess()
        {
            availableListings = new List<BlackMarketListing>();
        }
    }

    /// <summary>
    /// 黑市交易列表
    /// </summary>
    [Serializable]
    public class BlackMarketListing
    {
        public string id;
        public string vendorId;
        public BlindBoxItem item;
        public float price;                     // 价格
        public float baseRisk;
        public SocialClass minimumSocialClass;
        public float minimumReputation;
        public float reputationReward;
        public List<ListingCondition> conditions;
        public BlackMarketMerchant merchant;    // 商贩信息

        public BlackMarketListing()
        {
            conditions = new List<ListingCondition>();
        }
    }

    /// <summary>
    /// 交易条件
    /// </summary>
    [Serializable]
    public class ListingCondition
    {
        public ListingConditionType type;
        public TalentType? talentType;
        public AttributeType attributeType;
        public float value;
        public string eventId;

        public ListingCondition(ListingConditionType type, TalentType talentType)
        {
            this.type = type;
            this.talentType = talentType;
        }

        public ListingCondition(ListingConditionType type, AttributeType attributeType, float value)
        {
            this.type = type;
            this.attributeType = attributeType;
            this.value = value;
        }

        public ListingCondition(ListingConditionType type, string eventId)
        {
            this.type = type;
            this.eventId = eventId;
        }
    }

    /// <summary>
    /// 黑市交易记录
    /// </summary>
    [Serializable]
    public class BlackMarketTransaction
    {
        public string id;
        public string playerId;
        public string listingId;
        public BlindBoxItem item;
        public ResourceType paymentType;
        public float paymentAmount;
        public float riskLevel;
        public DateTime timestamp;
        public bool success;
        public string resultMessage;
        public string errorMessage;
    }

    /// <summary>
    /// 黑市商贩
    /// </summary>
    [Serializable]
    public class BlackMarketVendor
    {
        public string id;
        public string name;
        public string description;
        public float trustLevel;
        public List<BlindBoxContentType> specialties;

        public BlackMarketVendor()
        {
            specialties = new List<BlindBoxContentType>();
        }
    }

    /// <summary>
    /// 黑市商人（简化版本）
    /// </summary>
    [Serializable]
    public class BlackMarketMerchant
    {
        public string name;
        public BlackMarketMerchantType type;
        public string description;
    }

    /// <summary>
    /// 交易条件类型
    /// </summary>
    public enum ListingConditionType
    {
        HasTalent,              // 拥有特定天赋
        AttributeThreshold,     // 属性阈值
        CompletedEvent,         // 完成特定事件
        NoSystemTracking        // 未被系统追踪
    }
}
