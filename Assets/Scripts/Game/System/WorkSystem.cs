using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 工作系统 - 管理玩家的工作相关操作
    /// </summary>
    public class WorkSystem : IDisposable
    {
        private Dictionary<JobType, JobConfig> jobConfigs;
        private List<WorkSession> workHistory;
        private float workEfficiencyModifier = 1f;

        public event Action<JobType, WorkResult> OnWorkCompleted;
        public event Action<JobType> OnJobStarted;
        public event Action<JobType> OnJobQuit;

        public WorkSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            workHistory = new List<WorkSession>();

            LoadFromDatabase();
        }

        /// <summary>
        /// 从数据库加载配置
        /// </summary>
        private void LoadFromDatabase()
        {
            try
            {
                WorkDatabase.LoadAllData();

                jobConfigs = WorkDatabase.GetJobConfigs();
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[WorkSystem] CSV数据加载失败: {e.Message}");
            }
        }

        /// <summary>
        /// 开始工作
        /// </summary>
        public WorkResult StartWork(PlayerData playerData, JobType jobType, float workHours = 8f)
        {
            if (!CanStartWork(playerData, jobType))
            {
                return new WorkResult
                {
                    success = false,
                    message = "无法开始工作：条件不满足或精力不足"
                };
            }

            var jobConfig = jobConfigs[jobType];
            var workResult = ProcessWork(playerData, jobConfig, workHours);
            
            // 记录工作历史
            RecordWorkSession(playerData, jobType, workHours, workResult);
            
            // 更新玩家状态
            UpdatePlayerAfterWork(playerData, workResult);
            
            OnWorkCompleted?.Invoke(jobType, workResult);
            return workResult;
        }

        /// <summary>
        /// 检查是否可以开始工作
        /// </summary>
        private bool CanStartWork(PlayerData playerData, JobType jobType)
        {
            if (jobType == JobType.None) return false;
            
            var jobConfig = jobConfigs[jobType];
            
            // 检查精力值
            if (playerData.attributes.energy < jobConfig.requiredEnergy)
                return false;
            
            // 检查社会阶层要求
            if ((int)playerData.socialClass < (int)jobConfig.requiredSocialClass)
                return false;
            
            // 检查特殊要求
            foreach (GameCondition condition in jobConfig.requirements)
            {
                if (!condition.Check(playerData))
                    return false;
            }
            
            return true;
        }

        /// <summary>
        /// 处理工作过程
        /// </summary>
        private WorkResult ProcessWork(PlayerData playerData, JobConfig jobConfig, float workHours)
        {
            var result = new WorkResult
            {
                success = true,
                jobType = jobConfig.jobType,
                hoursWorked = workHours
            };

            // 计算工作效率
            float efficiency = CalculateWorkEfficiency(playerData, jobConfig);
            result.efficiency = efficiency;

            // 计算基础收益
            float baseReward = jobConfig.baseHourlyPay * workHours * efficiency;
            result.creditsEarned = baseReward;

            // 计算额外奖励
            CalculateAdditionalRewards(playerData, jobConfig, workHours, efficiency, result);

            // 计算消耗
            CalculateWorkCosts(playerData, jobConfig, workHours, result);

            // 随机事件
            ProcessWorkEvents(playerData, jobConfig, result);

            return result;
        }

        /// <summary>
        /// 计算工作效率
        /// </summary>
        private float CalculateWorkEfficiency(PlayerData playerData, JobConfig jobConfig)
        {
            float efficiency = workEfficiencyModifier;

            // 基于属性的效率修正
            efficiency += (playerData.attributes.energy - 50f) * 0.01f;
            efficiency += (playerData.attributes.social - 50f) * 0.005f;

            // 天赋影响
            if (playerData.HasTalent(TalentType.Workaholic))
            {
                efficiency *= 1.5f;
            }

            // 状态效果影响
            if (playerData.HasStatusEffect(StatusEffectType.Fatigue))
            {
                efficiency *= 0.7f;
            }

            if (playerData.HasStatusEffect(StatusEffectType.Sickness))
            {
                efficiency *= 0.5f;
            }

            return Mathf.Clamp(efficiency, 0.1f, 3f);
        }

        /// <summary>
        /// 计算额外奖励
        /// </summary>
        private void CalculateAdditionalRewards(PlayerData playerData, JobConfig jobConfig, 
            float workHours, float efficiency, WorkResult result)
        {
            // 社会积分奖励
            result.socialScoreEarned = jobConfig.socialScoreReward * workHours * efficiency;

            // 特殊奖励
            if (efficiency > 1.5f)
            {
                result.bonusRewards.Add("高效工作奖励：额外信用点");
                result.creditsEarned *= 1.2f;
            }

            // 职业特殊奖励
            switch (jobConfig.jobType)
            {
                case JobType.Research:
                    if (UnityEngine.Random.Range(0f, 1f) < 0.1f)
                    {
                        result.bonusRewards.Add("研究突破：获得认知值提升");
                        result.attributeChanges[AttributeType.Cognition] = 2f;
                    }
                    break;

                case JobType.Security:
                    if (UnityEngine.Random.Range(0f, 1f) < 0.15f)
                    {
                        result.bonusRewards.Add("安保工作：获得内部信息");
                        result.specialRewards.Add("internal_info");
                    }
                    break;

                case JobType.Management:
                    result.bonusRewards.Add("管理经验：社交值提升");
                    result.attributeChanges[AttributeType.Social] = 1f;
                    break;
            }
        }

        /// <summary>
        /// 计算工作消耗
        /// </summary>
        private void CalculateWorkCosts(PlayerData playerData, JobConfig jobConfig, 
            float workHours, WorkResult result)
        {
            // 精力消耗
            float energyCost = jobConfig.energyCostPerHour * workHours;
            result.energyConsumed = energyCost;

            // 特殊消耗
            if (jobConfig.jobType == JobType.Enforcement)
            {
                // 执法工作消耗人性值
                result.attributeChanges[AttributeType.Humanity] = -1f * workHours;
            }

            if (jobConfig.jobType == JobType.Advertising)
            {
                // 广告工作增加依存度
                result.attributeChanges[AttributeType.Dependence] = 0.5f * workHours;
            }
        }

        /// <summary>
        /// 处理工作事件
        /// </summary>
        private void ProcessWorkEvents(PlayerData playerData, JobConfig jobConfig, WorkResult result)
        {
            float eventChance = UnityEngine.Random.Range(0f, 1f);

            if (eventChance < 0.1f) // 10%概率触发事件
            {
                var workEvent = GenerateWorkEvent(jobConfig.jobType, playerData);
                result.workEvent = workEvent;
                
                if (workEvent != null)
                {
                    result.message += $"\n工作事件：{workEvent.description}";
                }
            }
        }

        /// <summary>
        /// 生成工作事件
        /// </summary>
        private WorkEvent GenerateWorkEvent(JobType jobType, PlayerData playerData)
        {
            switch (jobType)
            {
                case JobType.DataEntry:
                    return new WorkEvent
                    {
                        id = "data_glitch",
                        title = "数据异常",
                        description = "发现了一些奇怪的数据模式...",
                        effects = new List<WorkEventEffect>
                        {
                            new WorkEventEffect { type = "attribute", target = "cognition", value = 1f }
                        }
                    };

                case JobType.SystemMaintenance:
                    return new WorkEvent
                    {
                        id = "system_access",
                        title = "系统权限",
                        description = "意外获得了更高的系统访问权限",
                        effects = new List<WorkEventEffect>
                        {
                            new WorkEventEffect { type = "unlock", target = "system_info", value = 1f }
                        }
                    };

                case JobType.Security:
                    if (playerData.attributes.cognition > 70f)
                    {
                        return new WorkEvent
                        {
                            id = "suspicious_activity",
                            title = "可疑活动",
                            description = "注意到了一些可疑的活动，是否要深入调查？",
                            effects = new List<WorkEventEffect>
                            {
                                new WorkEventEffect { type = "choice", target = "investigate_suspicious", value = 1f }
                            }
                        };
                    }
                    break;
            }

            return null;
        }

        /// <summary>
        /// 更新玩家工作后状态
        /// </summary>
        private void UpdatePlayerAfterWork(PlayerData playerData, WorkResult result)
        {
            // 应用资源变化
            playerData.resources.AddResource(ResourceType.Credits, result.creditsEarned);
            playerData.resources.AddResource(ResourceType.SocialScore, result.socialScoreEarned);

            // 应用属性变化
            playerData.attributes.ModifyAttribute(AttributeType.Energy, -result.energyConsumed);
            
            foreach (var change in result.attributeChanges)
            {
                playerData.attributes.ModifyAttribute(change.Key, change.Value);
            }

            // 设置当前工作
            playerData.currentJob = result.jobType;
        }

        /// <summary>
        /// 记录工作历史
        /// </summary>
        private void RecordWorkSession(PlayerData playerData, JobType jobType, 
            float workHours, WorkResult result)
        {
            var session = new WorkSession
            {
                playerId = playerData.playerName,
                jobType = jobType,
                startTime = DateTime.Now,
                hoursWorked = workHours,
                efficiency = result.efficiency,
                creditsEarned = result.creditsEarned,
                socialScoreEarned = result.socialScoreEarned
            };

            workHistory.Add(session);

            // 限制历史记录数量
            if (workHistory.Count > 100)
            {
                workHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType? attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 检查是否有负面状态效果
        /// </summary>
        private bool HasNegativeStatusEffects(PlayerData playerData)
        {
            var negativeEffects = new[]
            {
                StatusEffectType.Sickness,
                StatusEffectType.Fatigue,
                StatusEffectType.Depression,
                StatusEffectType.SystemTracking
            };

            foreach (var effect in negativeEffects)
            {
                if (playerData.HasStatusEffect(effect))
                    return true;
            }

            return false;
        }

        public void Dispose()
        {
            jobConfigs?.Clear();
            workHistory?.Clear();
        }
    }

    /// <summary>
    /// 工作配置
    /// </summary>
    [Serializable]
    public class JobConfig
    {
        public JobType jobType;
        public string name;
        public string description;
        public float baseHourlyPay;
        public float energyCostPerHour;
        public float socialScoreReward;
        public float requiredEnergy;
        public SocialClass requiredSocialClass;
        public List<GameCondition> requirements;

        public JobConfig()
        {
            requirements = new List<GameCondition>();
        }
    }

    /// <summary>
    /// 工作结果
    /// </summary>
    [Serializable]
    public class WorkResult
    {
        public bool success;
        public JobType jobType;
        public float hoursWorked;
        public float efficiency;
        public float creditsEarned;
        public float socialScoreEarned;
        public float energyConsumed;
        public string message;
        public List<string> bonusRewards;
        public List<string> specialRewards;
        public Dictionary<AttributeType, float> attributeChanges;
        public WorkEvent workEvent;

        public WorkResult()
        {
            bonusRewards = new List<string>();
            specialRewards = new List<string>();
            attributeChanges = new Dictionary<AttributeType, float>();
        }
    }

    /// <summary>
    /// 工作事件
    /// </summary>
    [Serializable]
    public class WorkEvent
    {
        public string id;
        public string title;
        public string description;
        public List<WorkEventEffect> effects;

        public WorkEvent()
        {
            effects = new List<WorkEventEffect>();
        }
    }

    /// <summary>
    /// 工作事件效果
    /// </summary>
    [Serializable]
    public class WorkEventEffect
    {
        public string type;      // "attribute", "resource", "unlock", "choice"
        public string target;
        public float value;
    }

    /// <summary>
    /// 工作历史记录
    /// </summary>
    [Serializable]
    public class WorkSession
    {
        public string playerId;
        public JobType jobType;
        public DateTime startTime;
        public float hoursWorked;
        public float efficiency;
        public float creditsEarned;
        public float socialScoreEarned;
    }

    /// <summary>
    /// 工作要求类型
    /// </summary>
    public enum JobRequirementType
    {
        Attribute,          // 属性要求
        Talent,             // 天赋要求
        NoNegativeStatus,   // 无负面状态
        SocialScore         // 社会积分要求
    }
}
