using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 事件管理器 - 管理游戏中的随机事件和剧情推进
    /// </summary>
    public class EventSystem : IDisposable
    {
        private Dictionary<string, GameEvent> eventDatabase;
        private List<ActiveEvent> activeEvents;
        private List<EventHistory> eventHistory;
        private StoryProgress storyProgress;
        private float eventTimer = 0f;
        private float eventInterval = 300f; // 5分钟触发一次事件检查

        public event Action<GameEvent> OnEventTriggered;
        public event Action<GameEvent, EventChoice> OnEventChoiceMade;
        public event Action<GameEndType> OnGameEndTriggered;

        public EventSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            eventDatabase = new Dictionary<string, GameEvent>();
            activeEvents = new List<ActiveEvent>();
            eventHistory = new List<EventHistory>();
            storyProgress = new StoryProgress();
            
            InitializeEventDatabase();
            InitializeStoryProgress();
        }

        /// <summary>
        /// 更新事件系统
        /// </summary>
        public void UpdateEvents(float deltaTime)
        {
            eventTimer += deltaTime;
            
            // 定期检查触发新事件
            if (eventTimer >= eventInterval)
            {
                eventTimer = 0f;
                CheckRandomEvents();
            }
            
            // 更新活跃事件
            UpdateActiveEvents(deltaTime);
        }

        /// <summary>
        /// 检查随机事件
        /// </summary>
        private void CheckRandomEvents()
        {
            var playerData = GameLogic.Instance?.playerData;
            if (playerData == null) return;

            // 根据玩家状态和进度选择可能的事件
            var possibleEvents = GetPossibleEvents(playerData);
            
            if (possibleEvents.Count > 0)
            {
                // 随机选择一个事件
                var selectedEvent = SelectRandomEvent(possibleEvents, playerData);
                if (selectedEvent != null)
                {
                    TriggerEvent(selectedEvent.id, playerData);
                }
            }
        }

        /// <summary>
        /// 获取可能触发的事件
        /// </summary>
        private List<GameEvent> GetPossibleEvents(PlayerData playerData)
        {
            var possibleEvents = new List<GameEvent>();

            foreach (var eventPair in eventDatabase)
            {
                var gameEvent = eventPair.Value;
                
                // 检查事件触发条件
                if (CanTriggerEvent(gameEvent, playerData))
                {
                    possibleEvents.Add(gameEvent);
                }
            }

            return possibleEvents;
        }

        /// <summary>
        /// 检查事件是否可以触发
        /// </summary>
        private bool CanTriggerEvent(GameEvent gameEvent, PlayerData playerData)
        {
            // 检查冷却时间
            var lastTrigger = eventHistory.LastOrDefault(h => h.eventId == gameEvent.id);
            if (lastTrigger != null && 
                (DateTime.Now - lastTrigger.timestamp).TotalHours < gameEvent.cooldownHours)
            {
                return false;
            }

            // 检查触发条件
            foreach (var condition in gameEvent.triggerConditions)
            {
                if (!CheckEventCondition(condition, playerData))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 检查事件条件
        /// </summary>
        private bool CheckEventCondition(EventCondition condition, PlayerData playerData)
        {
            switch (condition.type)
            {
                case EventConditionType.Attribute:
                    float attributeValue = GetAttributeValue(playerData, condition.parameter);
                    return CompareValue(attributeValue, condition.value, condition.comparison);
                
                case EventConditionType.Resource:
                    float resourceValue = playerData.resources.GetResource(condition.resourceType.Value);
                    return CompareValue(resourceValue, condition.value, condition.comparison);
                
                case EventConditionType.SocialClass:
                    return (int)playerData.socialClass >= (int)condition.socialClass.Value;
                
                case EventConditionType.DaysSurvived:
                    return CompareValue(playerData.daysSurvived, condition.value, condition.comparison);
                
                case EventConditionType.BoxesOpened:
                    return CompareValue(playerData.totalBoxesOpened, condition.value, condition.comparison);
                
                case EventConditionType.HasTalent:
                    return playerData.HasTalent(condition.talentType.Value);
                
                case EventConditionType.HasStatusEffect:
                    return playerData.HasStatusEffect(condition.statusEffectType.Value);
                
                case EventConditionType.StoryProgress:
                    return storyProgress.HasReachedStage(condition.parameter);
                
                case EventConditionType.Random:
                    return UnityEngine.Random.Range(0f, 1f) < condition.value;
                
                default:
                    return false;
            }
        }

        /// <summary>
        /// 比较数值
        /// </summary>
        private bool CompareValue(float actual, float target, ComparisonType comparison)
        {
            switch (comparison)
            {
                case ComparisonType.Equal: return Mathf.Approximately(actual, target);
                case ComparisonType.Greater: return actual > target;
                case ComparisonType.GreaterOrEqual: return actual >= target;
                case ComparisonType.Less: return actual < target;
                case ComparisonType.LessOrEqual: return actual <= target;
                default: return false;
            }
        }

        /// <summary>
        /// 随机选择事件
        /// </summary>
        private GameEvent SelectRandomEvent(List<GameEvent> possibleEvents, PlayerData playerData)
        {
            // 根据权重选择事件
            float totalWeight = possibleEvents.Sum(e => CalculateEventWeight(e, playerData));
            float randomValue = UnityEngine.Random.Range(0f, totalWeight);
            float currentWeight = 0f;

            foreach (var gameEvent in possibleEvents)
            {
                currentWeight += CalculateEventWeight(gameEvent, playerData);
                if (randomValue <= currentWeight)
                {
                    return gameEvent;
                }
            }

            return possibleEvents.LastOrDefault();
        }

        /// <summary>
        /// 计算事件权重
        /// </summary>
        private float CalculateEventWeight(GameEvent gameEvent, PlayerData playerData)
        {
            float weight = gameEvent.baseWeight;

            // 根据玩家状态调整权重
            if (gameEvent.type == EventType.Resistance && playerData.knowsTruth)
            {
                weight *= 2f; // 知道真相后更容易遇到反抗事件
            }

            if (gameEvent.type == EventType.Punishment && playerData.attributes.dependence < 30f)
            {
                weight *= 1.5f; // 依存度低的玩家更容易被惩罚
            }

            return weight;
        }

        /// <summary>
        /// 触发事件
        /// </summary>
        public void TriggerEvent(string eventId, PlayerData playerData)
        {
            if (!eventDatabase.ContainsKey(eventId))
            {
                Debug.LogError($"事件不存在: {eventId}");
                return;
            }

            var gameEvent = eventDatabase[eventId];
            
            // 创建活跃事件
            var activeEvent = new ActiveEvent
            {
                eventData = gameEvent,
                triggeredTime = DateTime.Now,
                playerData = playerData,
                isActive = true
            };
            
            activeEvents.Add(activeEvent);
            
            // 记录事件历史
            RecordEventHistory(eventId, playerData);
            
            // 应用即时效果
            ApplyEventEffects(gameEvent.immediateEffects, playerData);
            
            // 更新剧情进度
            UpdateStoryProgress(gameEvent, playerData);
            
            OnEventTriggered?.Invoke(gameEvent);
        }

        /// <summary>
        /// 处理事件选择
        /// </summary>
        public void MakeEventChoice(string eventId, int choiceIndex, PlayerData playerData)
        {
            var activeEvent = activeEvents.FirstOrDefault(e => e.eventData.id == eventId && e.isActive);
            if (activeEvent == null) return;

            var gameEvent = activeEvent.eventData;
            if (choiceIndex < 0 || choiceIndex >= gameEvent.choices.Count) return;

            var choice = gameEvent.choices[choiceIndex];
            
            // 应用选择效果
            ApplyEventEffects(choice.effects, playerData);
            
            // 触发后续事件
            if (!string.IsNullOrEmpty(choice.nextEventId))
            {
                TriggerEvent(choice.nextEventId, playerData);
            }
            
            // 标记事件完成
            activeEvent.isActive = false;
            activeEvent.choiceMade = choiceIndex;
            
            OnEventChoiceMade?.Invoke(gameEvent, choice);
        }

        /// <summary>
        /// 应用事件效果
        /// </summary>
        private void ApplyEventEffects(List<EventEffect> effects, PlayerData playerData)
        {
            foreach (var effect in effects)
            {
                ApplyEventEffect(effect, playerData);
            }
        }

        /// <summary>
        /// 应用单个事件效果
        /// </summary>
        private void ApplyEventEffect(EventEffect effect, PlayerData playerData)
        {
            switch (effect.type)
            {
                case EventEffectType.AttributeChange:
                    playerData.attributes.ModifyAttribute(effect.targetAttribute, effect.value);
                    break;
                
                case EventEffectType.ResourceChange:
                    if (effect.value > 0)
                    {
                        playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                    }
                    else
                    {
                        playerData.resources.ConsumeResource(effect.resourceType.Value, -effect.value);
                    }
                    break;
                
                case EventEffectType.AddStatusEffect:
                    var statusEffect = new StatusEffect(effect.statusEffectType.Value, effect.value, (int)effect.duration, effect.description);
                    playerData.AddStatusEffect(statusEffect);
                    break;
                
                case EventEffectType.AddTalent:
                    playerData.AddTalent(effect.talentType.Value);
                    break;
                
                case EventEffectType.UnlockEvent:
                    playerData.unlockedEvents.Add(effect.targetEventId);
                    break;
                
                case EventEffectType.StoryProgress:
                    storyProgress.AdvanceToStage(effect.storyStage);
                    break;
                
                case EventEffectType.GameEnd:
                    TriggerGameEnd(effect.gameEndType.Value);
                    break;
                
                case EventEffectType.SocialClassChange:
                    playerData.socialClass = effect.socialClass.Value;
                    break;
            }
        }

        /// <summary>
        /// 触发游戏结束
        /// </summary>
        public void TriggerGameEndEvent(GameEndType endType)
        {
            TriggerGameEnd(endType);
        }

        private void TriggerGameEnd(GameEndType endType)
        {
            OnGameEndTriggered?.Invoke(endType);
        }

        /// <summary>
        /// 更新剧情进度
        /// </summary>
        private void UpdateStoryProgress(GameEvent gameEvent, PlayerData playerData)
        {
            if (gameEvent.type == EventType.Story)
            {
                storyProgress.AdvanceToStage(gameEvent.storyStage);
            }

            // 特殊剧情进度检查
            if (gameEvent.id == "meet_resistance")
            {
                playerData.hasMetResistance = true;
            }
            
            if (gameEvent.id == "discover_truth")
            {
                playerData.knowsTruth = true;
            }
        }

        /// <summary>
        /// 更新活跃事件
        /// </summary>
        private void UpdateActiveEvents(float deltaTime)
        {
            for (int i = activeEvents.Count - 1; i >= 0; i--)
            {
                var activeEvent = activeEvents[i];
                
                // 检查事件是否过期
                if (!activeEvent.isActive || 
                    (DateTime.Now - activeEvent.triggeredTime).TotalMinutes > activeEvent.eventData.timeoutMinutes)
                {
                    activeEvents.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 记录事件历史
        /// </summary>
        private void RecordEventHistory(string eventId, PlayerData playerData)
        {
            var history = new EventHistory
            {
                eventId = eventId,
                playerId = playerData.playerName,
                timestamp = DateTime.Now,
                playerStateSnapshot = JsonUtility.ToJson(playerData)
            };
            
            eventHistory.Add(history);
            
            // 限制历史记录数量
            if (eventHistory.Count > 500)
            {
                eventHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 获取属性值（字符串版本，向后兼容）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, string attributeName)
        {
            return playerData.attributes.GetAttributeValue(attributeName);
        }

        /// <summary>
        /// 初始化事件数据库
        /// </summary>
        private void InitializeEventDatabase()
        {
            // 系统故障事件
            var systemGlitch = new GameEvent
            {
                id = "system_glitch",
                title = "系统故障",
                description = "盲盒分发系统出现故障，你看到了一些不该看到的数据...",
                type = EventType.System,
                baseWeight = 0.1f,
                cooldownHours = 24f,
                timeoutMinutes = 10f,
                triggerConditions = new List<EventCondition>
                {
                    new EventCondition(EventConditionType.BoxesOpened, 50f, ComparisonType.Greater),
                    new EventCondition(EventConditionType.Random, 0.05f, ComparisonType.Less)
                },
                immediateEffects = new List<EventEffect>
                {
                    new EventEffect(EventEffectType.AttributeChange, "cognition", 10f),
                    new EventEffect(EventEffectType.UnlockEvent, "system_glitch")
                },
                choices = new List<EventChoice>
                {
                    new EventChoice
                    {
                        text = "仔细观察数据",
                        description = "试图理解这些数据的含义",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.AttributeChange, "cognition", 5f),
                            new EventEffect(EventEffectType.AddStatusEffect, StatusEffectType.SystemTracking, 1f, 7)
                        }
                    },
                    new EventChoice
                    {
                        text = "假装没看见",
                        description = "当作什么都没发生",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.AttributeChange, "dependence", 5f)
                        }
                    }
                }
            };
            eventDatabase["system_glitch"] = systemGlitch;

            // 遇见破盒者
            var meetResistance = new GameEvent
            {
                id = "meet_resistance",
                title = "神秘接触",
                description = "一个戴着面具的人悄悄接近你，递给你一张纸条...",
                type = EventType.Resistance,
                baseWeight = 0.2f,
                cooldownHours = 72f,
                timeoutMinutes = 15f,
                triggerConditions = new List<EventCondition>
                {
                    new EventCondition(EventConditionType.Attribute, "cognition", 60f, ComparisonType.Greater),
                    new EventCondition(EventConditionType.DaysSurvived, 20, ComparisonType.Greater)
                },
                choices = new List<EventChoice>
                {
                    new EventChoice
                    {
                        text = "接受纸条",
                        description = "看看上面写了什么",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.StoryProgress, "resistance_contact"),
                            new EventEffect(EventEffectType.UnlockEvent, "resistance_mission")
                        },
                        nextEventId = "resistance_invitation"
                    },
                    new EventChoice
                    {
                        text = "拒绝并报告",
                        description = "向系统举报这个可疑人员",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.ResourceChange, ResourceType.SocialScore, 50f),
                            new EventEffect(EventEffectType.AttributeChange, "morality", -10f)
                        }
                    }
                }
            };
            eventDatabase["meet_resistance"] = meetResistance;

            // TODO: 添加更多事件
        }

        /// <summary>
        /// 初始化剧情进度
        /// </summary>
        private void InitializeStoryProgress()
        {
            storyProgress.currentStage = "beginning";
            storyProgress.unlockedStages = new List<string> { "beginning" };
        }

        public void Dispose()
        {
            eventDatabase?.Clear();
            activeEvents?.Clear();
            eventHistory?.Clear();
        }
    }

    /// <summary>
    /// 游戏事件
    /// </summary>
    [Serializable]
    public class GameEvent
    {
        public string id;
        public string title;
        public string description;
        public EventType type;
        public float baseWeight;
        public float cooldownHours;
        public float timeoutMinutes;
        public string storyStage;
        public List<EventCondition> triggerConditions;
        public List<EventEffect> immediateEffects;
        public List<EventChoice> choices;

        public GameEvent()
        {
            triggerConditions = new List<EventCondition>();
            immediateEffects = new List<EventEffect>();
            choices = new List<EventChoice>();
        }
    }

    /// <summary>
    /// 事件条件
    /// </summary>
    [Serializable]
    public class EventCondition
    {
        public EventConditionType type;
        public float value;
        public ComparisonType comparison;
        public string parameter;
        public ResourceType? resourceType;
        public SocialClass? socialClass;
        public TalentType? talentType;
        public StatusEffectType? statusEffectType;

        public EventCondition(EventConditionType type, float value, ComparisonType comparison)
        {
            this.type = type;
            this.value = value;
            this.comparison = comparison;
        }

        public EventCondition(EventConditionType type, string parameter, float value, ComparisonType comparison)
        {
            this.type = type;
            this.parameter = parameter;
            this.value = value;
            this.comparison = comparison;
        }
    }

    /// <summary>
    /// 事件效果
    /// </summary>
    [Serializable]
    public class EventEffect
    {
        public EventEffectType type;
        public string targetAttribute;
        public float value;
        public float duration;
        public string description;
        public ResourceType? resourceType;
        public StatusEffectType? statusEffectType;
        public TalentType? talentType;
        public string targetEventId;
        public string storyStage;
        public GameEndType? gameEndType;
        public SocialClass? socialClass;

        public EventEffect(EventEffectType type, string target, float value)
        {
            this.type = type;
            this.targetAttribute = target;
            this.value = value;
        }

        public EventEffect(EventEffectType type, ResourceType resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }

        public EventEffect(EventEffectType type, StatusEffectType statusEffect, float value, float duration)
        {
            this.type = type;
            this.statusEffectType = statusEffect;
            this.value = value;
            this.duration = duration;
        }

        public EventEffect(EventEffectType type, string eventId)
        {
            this.type = type;
            this.targetEventId = eventId;
        }
    }

    /// <summary>
    /// 事件选择
    /// </summary>
    [Serializable]
    public class EventChoice
    {
        public string text;
        public string description;
        public List<EventEffect> effects;
        public string nextEventId;

        public EventChoice()
        {
            effects = new List<EventEffect>();
        }
    }

    /// <summary>
    /// 活跃事件
    /// </summary>
    [Serializable]
    public class ActiveEvent
    {
        public GameEvent eventData;
        public DateTime triggeredTime;
        public PlayerData playerData;
        public bool isActive;
        public int choiceMade = -1;
    }

    /// <summary>
    /// 事件历史
    /// </summary>
    [Serializable]
    public class EventHistory
    {
        public string eventId;
        public string playerId;
        public DateTime timestamp;
        public string playerStateSnapshot;
    }

    /// <summary>
    /// 剧情进度
    /// </summary>
    [Serializable]
    public class StoryProgress
    {
        public string currentStage;
        public List<string> unlockedStages;

        public StoryProgress()
        {
            unlockedStages = new List<string>();
        }

        public bool HasReachedStage(string stage)
        {
            return unlockedStages.Contains(stage);
        }

        public void AdvanceToStage(string stage)
        {
            if (!unlockedStages.Contains(stage))
            {
                unlockedStages.Add(stage);
            }
            currentStage = stage;
        }
    }

    /// <summary>
    /// 事件条件类型
    /// </summary>
    public enum EventConditionType
    {
        Attribute,          // 属性条件
        Resource,           // 资源条件
        SocialClass,        // 社会阶层
        DaysSurvived,       // 生存天数
        BoxesOpened,        // 开盒数量
        HasTalent,          // 拥有天赋
        HasStatusEffect,    // 拥有状态效果
        StoryProgress,      // 剧情进度
        Random              // 随机概率
    }

    /// <summary>
    /// 事件效果类型
    /// </summary>
    public enum EventEffectType
    {
        AttributeChange,        // 属性变化
        ResourceChange,         // 资源变化
        AddStatusEffect,        // 添加状态效果
        RemoveStatusEffect,     // 移除状态效果
        AddTalent,              // 添加天赋
        UnlockEvent,            // 解锁事件
        UnlockContent,          // 解锁内容
        StoryProgress,          // 剧情进度
        TriggerEvent,           // 触发事件
        GameEnd,                // 游戏结束
        SocialClassChange       // 社会阶层变化
    }

    /// <summary>
    /// 比较类型
    /// </summary>
    public enum ComparisonType
    {
        Equal,              // 等于
        Greater,            // 大于
        GreaterOrEqual,     // 大于等于
        Less,               // 小于
        LessOrEqual         // 小于等于
    }
}
