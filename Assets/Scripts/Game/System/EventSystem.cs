using System;
using System.Collections.Generic;
using System.Linq;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 事件管理器 - 管理游戏中的随机事件和剧情推进
    /// </summary>
    public class EventSystem : IDisposable
    {
        private Dictionary<string, GameEvent> events;
        private List<ActiveEvent> activeEvents;
        private List<EventHistory> eventHistory;
        private StoryProgress storyProgress;
        private float eventTimer = 0f;
        private float eventInterval = 300f; // 5分钟触发一次事件检查

        public event Action<GameEvent> OnEventTriggered;
        public event Action<GameEvent, EventChoice> OnEventChoiceMade;
        public event Action<GameEndType> OnGameEndTriggered;

        public EventSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            events = new Dictionary<string, GameEvent>();
            activeEvents = new List<ActiveEvent>();
            eventHistory = new List<EventHistory>();
            storyProgress = new StoryProgress();

            LoadFromDatabase();
            InitializeStoryProgress();
        }

        /// <summary>
        /// 从数据库加载配置
        /// </summary>
        private void LoadFromDatabase()
        {
            try
            {
                EventDatabase.LoadAllData();

                this.events = EventDatabase.GetEvents();

            }
            catch (Exception e)
            {
                Debug.LogWarning($"[EventSystem] CSV数据加载失败: {e.Message}");
            }
        }

        /// <summary>
        /// 更新事件系统
        /// </summary>
        public void UpdateEvents(float deltaTime)
        {
            eventTimer += deltaTime;
            
            // 定期检查触发新事件
            if (eventTimer >= eventInterval)
            {
                eventTimer = 0f;
                CheckRandomEvents();
            }
            
            // 更新活跃事件
            UpdateActiveEvents(deltaTime);
        }

        /// <summary>
        /// 检查随机事件
        /// </summary>
        private void CheckRandomEvents()
        {
            var playerData = GameLogic.Instance?.playerData;
            if (playerData == null) return;

            // 根据玩家状态和进度选择可能的事件
            var possibleEvents = GetPossibleEvents(playerData);
            
            if (possibleEvents.Count > 0)
            {
                // 随机选择一个事件
                var selectedEvent = SelectRandomEvent(possibleEvents, playerData);
                if (selectedEvent != null)
                {
                    TriggerEvent(selectedEvent.id, playerData);
                }
            }
        }

        /// <summary>
        /// 获取可能触发的事件
        /// </summary>
        private List<GameEvent> GetPossibleEvents(PlayerData playerData)
        {
            var possibleEvents = new List<GameEvent>();

            foreach (var eventPair in events)
            {
                var gameEvent = eventPair.Value;
                
                // 检查事件触发条件
                if (CanTriggerEvent(gameEvent, playerData))
                {
                    possibleEvents.Add(gameEvent);
                }
            }

            return possibleEvents;
        }

        /// <summary>
        /// 检查事件是否可以触发
        /// </summary>
        private bool CanTriggerEvent(GameEvent gameEvent, PlayerData playerData)
        {
            // 检查冷却时间
            var lastTrigger = eventHistory.LastOrDefault(h => h.eventId == gameEvent.id);
            if (lastTrigger != null && 
                (DateTime.Now - lastTrigger.timestamp).TotalHours < gameEvent.cooldownHours)
            {
                return false;
            }

            // 检查触发条件
            foreach (var condition in gameEvent.triggerConditions)
            {
                if (!condition.Check(playerData))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 随机选择事件
        /// </summary>
        private GameEvent SelectRandomEvent(List<GameEvent> possibleEvents, PlayerData playerData)
        {
            // 根据权重选择事件
            float totalWeight = possibleEvents.Sum(e => CalculateEventWeight(e, playerData));
            float randomValue = UnityEngine.Random.Range(0f, totalWeight);
            float currentWeight = 0f;

            foreach (var gameEvent in possibleEvents)
            {
                currentWeight += CalculateEventWeight(gameEvent, playerData);
                if (randomValue <= currentWeight)
                {
                    return gameEvent;
                }
            }

            return possibleEvents.LastOrDefault();
        }

        /// <summary>
        /// 计算事件权重
        /// </summary>
        private float CalculateEventWeight(GameEvent gameEvent, PlayerData playerData)
        {
            float weight = gameEvent.baseWeight;

            // 根据玩家状态调整权重
            if (gameEvent.type == EventType.Resistance && playerData.knowsTruth)
            {
                weight *= 2f; // 知道真相后更容易遇到反抗事件
            }

            if (gameEvent.type == EventType.Punishment && playerData.attributes.dependence < 30f)
            {
                weight *= 1.5f; // 依存度低的玩家更容易被惩罚
            }

            return weight;
        }

        /// <summary>
        /// 触发事件
        /// </summary>
        public void TriggerEvent(string eventId, PlayerData playerData)
        {
            if (!events.ContainsKey(eventId))
            {
                Debug.LogError($"事件不存在: {eventId}");
                return;
            }

            var gameEvent = events[eventId];
            
            // 创建活跃事件
            var activeEvent = new ActiveEvent
            {
                eventData = gameEvent,
                triggeredTime = DateTime.Now,
                playerData = playerData,
                isActive = true
            };
            
            activeEvents.Add(activeEvent);
            
            // 记录事件历史
            RecordEventHistory(eventId, playerData);
            
            // 应用即时效果
            ApplyEventEffects(gameEvent.immediateEffects, playerData);
            
            // 更新剧情进度
            UpdateStoryProgress(gameEvent, playerData);
            
            OnEventTriggered?.Invoke(gameEvent);
        }

        /// <summary>
        /// 处理事件选择
        /// </summary>
        public void MakeEventChoice(string eventId, int choiceIndex, PlayerData playerData)
        {
            var activeEvent = activeEvents.FirstOrDefault(e => e.eventData.id == eventId && e.isActive);
            if (activeEvent == null) return;

            var gameEvent = activeEvent.eventData;
            if (choiceIndex < 0 || choiceIndex >= gameEvent.choices.Count) return;

            var choice = gameEvent.choices[choiceIndex];
            
            // 应用选择效果
            ApplyEventEffects(choice.effects, playerData);
            
            // 触发后续事件
            if (!string.IsNullOrEmpty(choice.nextEventId))
            {
                TriggerEvent(choice.nextEventId, playerData);
            }
            
            // 标记事件完成
            activeEvent.isActive = false;
            activeEvent.choiceMade = choiceIndex;
            
            OnEventChoiceMade?.Invoke(gameEvent, choice);
        }

        /// <summary>
        /// 应用事件效果
        /// </summary>
        private void ApplyEventEffects(List<GameEffect> effects, PlayerData playerData)
        {
            foreach (var effect in effects)
            {
                ApplyEventEffect(effect, playerData);
            }
        }

        /// <summary>
        /// 应用单个事件效果
        /// </summary>
        private void ApplyEventEffect(GameEffect effect, PlayerData playerData)
        {
            switch (effect.type)
            {
                case GameEffectType.AttributeChange:
                    playerData.attributes.ModifyAttribute(effect.attributeType, effect.value);
                    break;
                
                case GameEffectType.ResourceChange:
                    if (effect.value > 0)
                    {
                        playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                    }
                    else
                    {
                        playerData.resources.ConsumeResource(effect.resourceType.Value, -effect.value);
                    }
                    break;
                
                case GameEffectType.AddStatusEffect:
                    var statusEffect = new StatusEffect(effect.statusEffectType.Value, effect.value, (int)effect.duration, effect.description);
                    playerData.AddStatusEffect(statusEffect);
                    break;
                
                case GameEffectType.AddTalent:
                    playerData.AddTalent(effect.talentType.Value);
                    break;
                
                case GameEffectType.UnlockEvent:
                    playerData.unlockedEvents.Add(effect.stringValue);
                    break;
                
                case GameEffectType.StoryProgress:
                    storyProgress.AdvanceToStage(effect.stringValue);
                    break;
                
            }
        }

        /// <summary>
        /// 触发游戏结束
        /// </summary>
        public void TriggerGameEndEvent(GameEndType endType)
        {
            TriggerGameEnd(endType);
        }

        private void TriggerGameEnd(GameEndType endType)
        {
            OnGameEndTriggered?.Invoke(endType);
        }

        /// <summary>
        /// 更新剧情进度
        /// </summary>
        private void UpdateStoryProgress(GameEvent gameEvent, PlayerData playerData)
        {
            if (gameEvent.type == EventType.Story)
            {
                storyProgress.AdvanceToStage(gameEvent.storyStage);
            }

            // 特殊剧情进度检查
            if (gameEvent.id == "meet_resistance")
            {
                playerData.hasMetResistance = true;
            }
            
            if (gameEvent.id == "discover_truth")
            {
                playerData.knowsTruth = true;
            }
        }

        /// <summary>
        /// 更新活跃事件
        /// </summary>
        private void UpdateActiveEvents(float deltaTime)
        {
            for (int i = activeEvents.Count - 1; i >= 0; i--)
            {
                var activeEvent = activeEvents[i];
                
                // 检查事件是否过期
                if (!activeEvent.isActive)
                {
                    activeEvents.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 记录事件历史
        /// </summary>
        private void RecordEventHistory(string eventId, PlayerData playerData)
        {
            var history = new EventHistory
            {
                eventId = eventId,
                playerId = playerData.playerName,
                timestamp = DateTime.Now,
                playerStateSnapshot = JsonUtility.ToJson(playerData)
            };
            
            eventHistory.Add(history);
            
            // 限制历史记录数量
            if (eventHistory.Count > 500)
            {
                eventHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType? attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }


        /// <summary>
        /// 初始化剧情进度
        /// </summary>
        private void InitializeStoryProgress()
        {
            storyProgress.currentStage = "beginning";
            storyProgress.unlockedStages = new List<string> { "beginning" };
        }

        public void Dispose()
        {
            events?.Clear();
            activeEvents?.Clear();
            eventHistory?.Clear();
        }
    }

    /// <summary>
    /// 游戏事件
    /// </summary>
    [Serializable]
    public class GameEvent
    {
        public string id;
        public string title;
        public string description;
        public EventType type;
        public float baseWeight;
        public float cooldownHours;
        public string storyStage;
        public List<GameCondition> triggerConditions;
        public List<GameEffect> immediateEffects;
        public List<EventChoice> choices;

        public GameEvent()
        {
            triggerConditions = new List<GameCondition>();
            immediateEffects = new List<GameEffect>();
            choices = new List<EventChoice>();
        }
    }


    /// <summary>
    /// 事件选择
    /// </summary>
    [Serializable]
    public class EventChoice
    {
        public string id;
        public string text;
        public List<GameEffect> effects;
        public string nextEventId;

        public EventChoice()
        {
            effects = new List<GameEffect>();
        }
    }

    /// <summary>
    /// 活跃事件
    /// </summary>
    [Serializable]
    public class ActiveEvent
    {
        public GameEvent eventData;
        public DateTime triggeredTime;
        public PlayerData playerData;
        public bool isActive;
        public int choiceMade = -1;
    }

    /// <summary>
    /// 事件历史
    /// </summary>
    [Serializable]
    public class EventHistory
    {
        public string eventId;
        public string playerId;
        public DateTime timestamp;
        public string playerStateSnapshot;
    }

    /// <summary>
    /// 剧情进度
    /// </summary>
    [Serializable]
    public class StoryProgress
    {
        public string currentStage;
        public List<string> unlockedStages;

        public StoryProgress()
        {
            unlockedStages = new List<string>();
        }

        public bool HasReachedStage(string stage)
        {
            return unlockedStages.Contains(stage);
        }

        public void AdvanceToStage(string stage)
        {
            if (!unlockedStages.Contains(stage))
            {
                unlockedStages.Add(stage);
            }
            currentStage = stage;
        }
    }

}
