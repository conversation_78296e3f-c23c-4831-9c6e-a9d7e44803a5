using System;
using System.Collections.Generic;
using System.Linq;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 成就系统 - 管理玩家成就和进度追踪
    /// </summary>
    public class AchievementSystem : IDisposable
    {
        private Dictionary<string, Achievement> achievements;
        private Dictionary<string, ProgressTracker> progressTrackers;
        private List<AchievementUnlock> unlockHistory;

        public event Action<Achievement> OnAchievementUnlocked;
        public event Action<string, float> OnProgressUpdated;
        public event Action<Milestone> OnMilestoneReached;

        public AchievementSystem()
        {
            Initialize();
        }


        private void Initialize()
        {
            unlockHistory = new List<AchievementUnlock>();

            LoadFromDatabase();
        }

        /// <summary>
        /// 从数据库加载配置
        /// </summary>
        private void LoadFromDatabase()
        {
            try
            {
                AchievementDatabase.LoadAllData();
                achievements = AchievementDatabase.GetAchievements();
                progressTrackers = AchievementDatabase.GetProgressTrackers();

                Debug.Log($"[AchievementSystem] 从数据库加载: {achievements.Count} 个成就, {progressTrackers.Count} 个追踪器");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AchievementSystem] 数据库加载失败: {e.Message}");
            }
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        public void UpdateProgress(string trackerId, float value, PlayerData playerData)
        {
            if (!progressTrackers.ContainsKey(trackerId))
                return;

            var tracker = progressTrackers[trackerId];
            float oldValue = tracker.currentValue;
            tracker.currentValue = Mathf.Max(tracker.currentValue, value);

            // 检查是否有进度变化
            if (tracker.currentValue > oldValue)
            {
                OnProgressUpdated?.Invoke(trackerId, tracker.currentValue);
                
                // 检查相关成就
                CheckAchievements(trackerId, tracker.currentValue, playerData);
                
                // 检查里程碑
                CheckMilestones(tracker);
            }
        }

        /// <summary>
        /// 增加进度
        /// </summary>
        public void IncrementProgress(string trackerId, float increment, PlayerData playerData)
        {
            if (!progressTrackers.ContainsKey(trackerId))
                return;

            var tracker = progressTrackers[trackerId];
            tracker.currentValue += increment;
            
            OnProgressUpdated?.Invoke(trackerId, tracker.currentValue);
            
            // 检查相关成就
            CheckAchievements(trackerId, tracker.currentValue, playerData);
            
            // 检查里程碑
            CheckMilestones(tracker);
        }

        /// <summary>
        /// 检查成就
        /// </summary>
        private void CheckAchievements(string trackerId, float currentValue, PlayerData playerData)
        {
            foreach (var achievement in achievements.Values)
            {
                if (achievement.isUnlocked)
                    continue;

                if (CheckAchievementConditions(achievement, playerData))
                {
                    UnlockAchievement(achievement.id, playerData);
                }
            }
        }

        /// <summary>
        /// 检查成就条件
        /// </summary>
        private bool CheckAchievementConditions(Achievement achievement, PlayerData playerData)
        {
            foreach (GameCondition condition in achievement.conditions)
            {
                if (!condition.Check(playerData))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 检查特殊标志
        /// </summary>
        private bool CheckSpecialFlag(string flagName, PlayerData playerData)
        {
            switch (flagName)
            {
                case "met_resistance":
                    return playerData.hasMetResistance;
                case "knows_truth":
                    return playerData.knowsTruth;
                case "never_opened_box":
                    return playerData.totalBoxesOpened == 0;
                case "high_dependence":
                    return playerData.attributes.dependence >= 80f;
                case "low_humanity":
                    return playerData.attributes.humanity <= 20f;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 解锁成就
        /// </summary>
        public void UnlockAchievement(string achievementId, PlayerData playerData)
        {
            if (!achievements.ContainsKey(achievementId))
                return;

            var achievement = achievements[achievementId];
            if (achievement.isUnlocked)
                return;

            achievement.isUnlocked = true;
            achievement.unlockedDate = DateTime.Now;

            // 添加到玩家完成成就列表
            if (!playerData.completedAchievements.Contains(achievementId))
            {
                playerData.completedAchievements.Add(achievementId);
            }

            // 记录解锁历史
            var unlock = new AchievementUnlock
            {
                achievementId = achievementId,
                playerId = playerData.playerName,
                unlockedDate = DateTime.Now,
                daysSurvived = playerData.daysSurvived
            };
            unlockHistory.Add(unlock);

            // 应用奖励
            ApplyAchievementRewards(achievement, playerData);

            OnAchievementUnlocked?.Invoke(achievement);
            Debug.Log($"成就解锁: {achievement.name}");
        }

        /// <summary>
        /// 应用成就奖励
        /// </summary>
        private void ApplyAchievementRewards(Achievement achievement, PlayerData playerData)
        {
            foreach (GameEffect reward in achievement.rewards)
            {
                reward.Apply(playerData);

            }
        }


        /// <summary>
        /// 检查里程碑
        /// </summary>
        private void CheckMilestones(ProgressTracker tracker)
        {
            foreach (var milestone in tracker.milestones)
            {
                if (!milestone.isReached && tracker.currentValue >= milestone.threshold)
                {
                    milestone.isReached = true;
                    milestone.reachedDate = DateTime.Now;
                    OnMilestoneReached?.Invoke(milestone);
                }
            }
        }

        /// <summary>
        /// 获取成就进度
        /// </summary>
        public float GetAchievementProgress(string achievementId, PlayerData playerData)
        {
            if (!achievements.ContainsKey(achievementId))
                return 0f;

            var achievement = achievements[achievementId];
            if (achievement.isUnlocked)
                return 1f;

            // 计算进度百分比
            float totalConditions = achievement.conditions.Count;
            float metConditions = 0f;

            foreach (GameCondition condition in achievement.conditions)
            {
                if (condition.Check(playerData))
                {
                    metConditions += 1f;
                }
                else
                {
                    // 部分进度计算
                    float partialProgress = condition.GetConditionProgress(playerData);
                    metConditions += partialProgress;
                }
            }

            return metConditions / totalConditions;
        }


        /// <summary>
        /// 获取所有成就
        /// </summary>
        public List<Achievement> GetAllAchievements()
        {
            return new List<Achievement>(achievements.Values);
        }

        /// <summary>
        /// 获取已解锁成就
        /// </summary>
        public List<Achievement> GetUnlockedAchievements()
        {
            return achievements.Values.Where(a => a.isUnlocked).ToList();
        }

        /// <summary>
        /// 获取进度追踪器
        /// </summary>
        public ProgressTracker GetProgressTracker(string trackerId)
        {
            return progressTrackers.ContainsKey(trackerId) ? progressTrackers[trackerId] : null;
        }


        public void Dispose()
        {
            achievements?.Clear();
            progressTrackers?.Clear();
            unlockHistory?.Clear();
        }
    }

    /// <summary>
    /// 成就
    /// </summary>
    [Serializable]
    public class Achievement
    {
        public string id;
        public string name;
        public string description;
        public AchievementCategory category;
        public AchievementRarity rarity;
        public bool isHidden;
        public string iconPath;
        public bool isUnlocked;
        public DateTime unlockedDate;
        public List<GameCondition> conditions;
        public List<GameEffect> rewards;

        public Achievement()
        {
            conditions = new List<GameCondition>();
            rewards = new List<GameEffect>();
        }
    }

    /// <summary>
    /// 进度追踪器
    /// </summary>
    [Serializable]
    public class ProgressTracker
    {
        public string id;
        public string name;
        public string description;
        public float currentValue;
        public float maxValue;
        public List<Milestone> milestones;

        public ProgressTracker()
        {
            milestones = new List<Milestone>();
            maxValue = float.MaxValue;
        }
    }

    /// <summary>
    /// 里程碑
    /// </summary>
    [Serializable]
    public class Milestone
    {
        public float threshold;
        public string name;
        public string description;
        public bool isReached;
        public DateTime reachedDate;
    }

    /// <summary>
    /// 成就解锁记录
    /// </summary>
    [Serializable]
    public class AchievementUnlock
    {
        public string achievementId;
        public string playerId;
        public DateTime unlockedDate;
        public int daysSurvived;
    }
}
