using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 成就系统 - 管理玩家成就和进度追踪
    /// </summary>
    public class AchievementSystem : IDisposable
    {
        private Dictionary<string, Achievement> achievements;
        private Dictionary<string, ProgressTracker> progressTrackers;
        private List<AchievementUnlock> unlockHistory;
        private GameDataSystem dataSystem;

        public event Action<Achievement> OnAchievementUnlocked;
        public event Action<string, float> OnProgressUpdated;
        public event Action<Milestone> OnMilestoneReached;

        public AchievementSystem()
        {
            Initialize();
        }

        public AchievementSystem(GameDataSystem gameDataSystem)
        {
            dataSystem = gameDataSystem;
            Initialize();
        }

        private void Initialize()
        {
            unlockHistory = new List<AchievementUnlock>();

            // 获取数据系统实例
            if (dataSystem == null)
            {
                dataSystem = GameDataSystem.Instance;
            }

            // 从数据系统加载成就和进度追踪器
            if (dataSystem != null && dataSystem.IsDataLoaded())
            {
                LoadFromDataSystem();
            }
            else
            {
                LoadDefaultData();
            }
        }

        /// <summary>
        /// 从数据系统加载成就数据
        /// </summary>
        private void LoadFromDataSystem()
        {
            achievements = dataSystem.GetAllAchievements();
            progressTrackers = dataSystem.GetAllProgressTrackers();

            Debug.Log($"[AchievementSystem] 从数据系统加载: {achievements.Count} 个成就, {progressTrackers.Count} 个追踪器");
        }

        /// <summary>
        /// 加载默认数据（当数据系统未就绪时）
        /// </summary>
        private void LoadDefaultData()
        {
            achievements = new Dictionary<string, Achievement>();
            progressTrackers = new Dictionary<string, ProgressTracker>();

            InitializeAchievements();
            InitializeProgressTrackers();

            Debug.Log("[AchievementSystem] 数据系统未就绪，使用默认配置");
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        public void UpdateProgress(string trackerId, float value, PlayerData playerData)
        {
            if (!progressTrackers.ContainsKey(trackerId))
                return;

            var tracker = progressTrackers[trackerId];
            float oldValue = tracker.currentValue;
            tracker.currentValue = Mathf.Max(tracker.currentValue, value);

            // 检查是否有进度变化
            if (tracker.currentValue > oldValue)
            {
                OnProgressUpdated?.Invoke(trackerId, tracker.currentValue);
                
                // 检查相关成就
                CheckAchievements(trackerId, tracker.currentValue, playerData);
                
                // 检查里程碑
                CheckMilestones(tracker);
            }
        }

        /// <summary>
        /// 增加进度
        /// </summary>
        public void IncrementProgress(string trackerId, float increment, PlayerData playerData)
        {
            if (!progressTrackers.ContainsKey(trackerId))
                return;

            var tracker = progressTrackers[trackerId];
            tracker.currentValue += increment;
            
            OnProgressUpdated?.Invoke(trackerId, tracker.currentValue);
            
            // 检查相关成就
            CheckAchievements(trackerId, tracker.currentValue, playerData);
            
            // 检查里程碑
            CheckMilestones(tracker);
        }

        /// <summary>
        /// 检查成就
        /// </summary>
        private void CheckAchievements(string trackerId, float currentValue, PlayerData playerData)
        {
            foreach (var achievement in achievements.Values)
            {
                if (achievement.isUnlocked)
                    continue;

                if (CheckAchievementConditions(achievement, playerData))
                {
                    UnlockAchievement(achievement.id, playerData);
                }
            }
        }

        /// <summary>
        /// 检查成就条件
        /// </summary>
        private bool CheckAchievementConditions(Achievement achievement, PlayerData playerData)
        {
            foreach (var condition in achievement.conditions)
            {
                if (!CheckCondition(condition, playerData))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 检查单个条件
        /// </summary>
        private bool CheckCondition(AchievementCondition condition, PlayerData playerData)
        {
            switch (condition.type)
            {
                case AchievementConditionType.ProgressValue:
                    if (progressTrackers.ContainsKey(condition.trackerId))
                    {
                        return progressTrackers[condition.trackerId].currentValue >= condition.value;
                    }
                    return false;
                
                case AchievementConditionType.AttributeValue:
                    return GetAttributeValue(playerData, condition.attributeType) >= condition.value;
                
                case AchievementConditionType.ResourceValue:
                    return playerData.resources.GetResource(condition.resourceType.Value) >= condition.value;
                
                case AchievementConditionType.SocialClass:
                    return (int)playerData.socialClass >= (int)condition.socialClass.Value;
                
                case AchievementConditionType.HasTalent:
                    return playerData.HasTalent(condition.talentType.Value);
                
                case AchievementConditionType.CompletedEvent:
                    return playerData.unlockedEvents.Contains(condition.eventId);
                
                case AchievementConditionType.DaysSurvived:
                    return playerData.daysSurvived >= condition.value;
                
                case AchievementConditionType.BoxesOpened:
                    return playerData.totalBoxesOpened >= condition.value;
                
                case AchievementConditionType.SpecialFlag:
                    return CheckSpecialFlag(condition.flagName, playerData);
                
                default:
                    return false;
            }
        }

        /// <summary>
        /// 检查特殊标志
        /// </summary>
        private bool CheckSpecialFlag(string flagName, PlayerData playerData)
        {
            switch (flagName)
            {
                case "met_resistance":
                    return playerData.hasMetResistance;
                case "knows_truth":
                    return playerData.knowsTruth;
                case "never_opened_box":
                    return playerData.totalBoxesOpened == 0;
                case "high_dependence":
                    return playerData.attributes.dependence >= 80f;
                case "low_humanity":
                    return playerData.attributes.humanity <= 20f;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 解锁成就
        /// </summary>
        public void UnlockAchievement(string achievementId, PlayerData playerData)
        {
            if (!achievements.ContainsKey(achievementId))
                return;

            var achievement = achievements[achievementId];
            if (achievement.isUnlocked)
                return;

            achievement.isUnlocked = true;
            achievement.unlockedDate = DateTime.Now;

            // 添加到玩家完成成就列表
            if (!playerData.completedAchievements.Contains(achievementId))
            {
                playerData.completedAchievements.Add(achievementId);
            }

            // 记录解锁历史
            var unlock = new AchievementUnlock
            {
                achievementId = achievementId,
                playerId = playerData.playerName,
                unlockedDate = DateTime.Now,
                daysSurvived = playerData.daysSurvived
            };
            unlockHistory.Add(unlock);

            // 应用奖励
            ApplyAchievementRewards(achievement, playerData);

            OnAchievementUnlocked?.Invoke(achievement);
            Debug.Log($"成就解锁: {achievement.name}");
        }

        /// <summary>
        /// 应用成就奖励
        /// </summary>
        private void ApplyAchievementRewards(Achievement achievement, PlayerData playerData)
        {
            foreach (var reward in achievement.rewards)
            {
                ApplyReward(reward, playerData);
            }
        }

        /// <summary>
        /// 应用单个奖励
        /// </summary>
        private void ApplyReward(AchievementReward reward, PlayerData playerData)
        {
            switch (reward.type)
            {
                case AchievementRewardType.Resource:
                    playerData.resources.AddResource(reward.resourceType.Value, reward.value);
                    break;
                
                case AchievementRewardType.Attribute:
                    playerData.attributes.ModifyAttribute(reward.attributeType, reward.value);
                    break;
                
                case AchievementRewardType.Talent:
                    playerData.AddTalent(reward.talentType.Value);
                    break;
                
                case AchievementRewardType.UnlockEvent:
                    if (!playerData.unlockedEvents.Contains(reward.eventId))
                    {
                        playerData.unlockedEvents.Add(reward.eventId);
                    }
                    break;
                
                case AchievementRewardType.Title:
                    // 这里可以添加称号系统
                    Debug.Log($"获得称号: {reward.title}");
                    break;
            }
        }

        /// <summary>
        /// 检查里程碑
        /// </summary>
        private void CheckMilestones(ProgressTracker tracker)
        {
            foreach (var milestone in tracker.milestones)
            {
                if (!milestone.isReached && tracker.currentValue >= milestone.threshold)
                {
                    milestone.isReached = true;
                    milestone.reachedDate = DateTime.Now;
                    OnMilestoneReached?.Invoke(milestone);
                }
            }
        }

        /// <summary>
        /// 获取成就进度
        /// </summary>
        public float GetAchievementProgress(string achievementId, PlayerData playerData)
        {
            if (!achievements.ContainsKey(achievementId))
                return 0f;

            var achievement = achievements[achievementId];
            if (achievement.isUnlocked)
                return 1f;

            // 计算进度百分比
            float totalConditions = achievement.conditions.Count;
            float metConditions = 0f;

            foreach (var condition in achievement.conditions)
            {
                if (CheckCondition(condition, playerData))
                {
                    metConditions += 1f;
                }
                else
                {
                    // 部分进度计算
                    float partialProgress = GetConditionProgress(condition, playerData);
                    metConditions += partialProgress;
                }
            }

            return metConditions / totalConditions;
        }

        /// <summary>
        /// 获取条件进度
        /// </summary>
        private float GetConditionProgress(AchievementCondition condition, PlayerData playerData)
        {
            switch (condition.type)
            {
                case AchievementConditionType.ProgressValue:
                    if (progressTrackers.ContainsKey(condition.trackerId))
                    {
                        return Mathf.Clamp01(progressTrackers[condition.trackerId].currentValue / condition.value);
                    }
                    return 0f;
                
                case AchievementConditionType.AttributeValue:
                    return Mathf.Clamp01(GetAttributeValue(playerData, condition.attributeType) / condition.value);
                
                case AchievementConditionType.ResourceValue:
                    return Mathf.Clamp01(playerData.resources.GetResource(condition.resourceType.Value) / condition.value);
                
                case AchievementConditionType.DaysSurvived:
                    return Mathf.Clamp01(playerData.daysSurvived / condition.value);
                
                case AchievementConditionType.BoxesOpened:
                    return Mathf.Clamp01(playerData.totalBoxesOpened / condition.value);
                
                default:
                    return CheckCondition(condition, playerData) ? 1f : 0f;
            }
        }

        /// <summary>
        /// 获取所有成就
        /// </summary>
        public List<Achievement> GetAllAchievements()
        {
            return new List<Achievement>(achievements.Values);
        }

        /// <summary>
        /// 获取已解锁成就
        /// </summary>
        public List<Achievement> GetUnlockedAchievements()
        {
            return achievements.Values.Where(a => a.isUnlocked).ToList();
        }

        /// <summary>
        /// 获取进度追踪器
        /// </summary>
        public ProgressTracker GetProgressTracker(string trackerId)
        {
            return progressTrackers.ContainsKey(trackerId) ? progressTrackers[trackerId] : null;
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType? attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 初始化成就
        /// </summary>
        private void InitializeAchievements()
        {
            // 生存类成就
            achievements["first_week"] = new Achievement
            {
                id = "first_week",
                name = "初来乍到",
                description = "生存第一周",
                category = AchievementCategory.Survival,
                rarity = AchievementRarity.Common,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.DaysSurvived, 7f)
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Resource, ResourceType.Credits, 100f)
                }
            };

            achievements["month_survivor"] = new Achievement
            {
                id = "month_survivor",
                name = "月度幸存者",
                description = "生存一个月",
                category = AchievementCategory.Survival,
                rarity = AchievementRarity.Uncommon,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.DaysSurvived, 30f)
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Resource, ResourceType.Diamond, 1f),
                    new AchievementReward(AchievementRewardType.Title, "月度幸存者")
                }
            };

            // 盲盒类成就
            achievements["box_opener"] = new Achievement
            {
                id = "box_opener",
                name = "盲盒新手",
                description = "开启第一个盲盒",
                category = AchievementCategory.BlindBox,
                rarity = AchievementRarity.Common,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.BoxesOpened, 1f)
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Attribute, AttributeType.Luck, 1f)
                }
            };

            achievements["box_addict"] = new Achievement
            {
                id = "box_addict",
                name = "盲盒成瘾者",
                description = "开启100个盲盒",
                category = AchievementCategory.BlindBox,
                rarity = AchievementRarity.Rare,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.BoxesOpened, 100f)
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Talent, TalentType.GamblerGene),
                    new AchievementReward(AchievementRewardType.Title, "盲盒大师")
                }
            };

            // 社会类成就
            achievements["social_climber"] = new Achievement
            {
                id = "social_climber",
                name = "社会攀登者",
                description = "晋升到工蜂阶层",
                category = AchievementCategory.Social,
                rarity = AchievementRarity.Uncommon,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.SocialClass, SocialClass.Worker)
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Resource, ResourceType.SocialScore, 50f)
                }
            };

            achievements["chosen_one"] = new Achievement
            {
                id = "chosen_one",
                name = "天选之子",
                description = "晋升到神选者阶层",
                category = AchievementCategory.Social,
                rarity = AchievementRarity.Epic,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.SocialClass, SocialClass.Chosen)
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Resource, ResourceType.Diamond, 5f),
                    new AchievementReward(AchievementRewardType.Title, "神选者")
                }
            };

            // 觉醒类成就
            achievements["truth_seeker"] = new Achievement
            {
                id = "truth_seeker",
                name = "真相探寻者",
                description = "遇见破盒者组织",
                category = AchievementCategory.Story,
                rarity = AchievementRarity.Rare,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.SpecialFlag, "met_resistance")
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Attribute, AttributeType.Cognition, 10f),
                    new AchievementReward(AchievementRewardType.UnlockEvent, "resistance_missions")
                }
            };

            achievements["awakened"] = new Achievement
            {
                id = "awakened",
                name = "觉醒者",
                description = "发现系统的真相",
                category = AchievementCategory.Story,
                rarity = AchievementRarity.Epic,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.SpecialFlag, "knows_truth")
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Talent, TalentType.HackerGenius),
                    new AchievementReward(AchievementRewardType.Title, "觉醒者")
                }
            };

            // 特殊成就
            achievements["pacifist"] = new Achievement
            {
                id = "pacifist",
                name = "和平主义者",
                description = "从未开启过盲盒却生存30天",
                category = AchievementCategory.Special,
                rarity = AchievementRarity.Legendary,
                conditions = new List<AchievementCondition>
                {
                    new AchievementCondition(AchievementConditionType.DaysSurvived, 30f),
                    new AchievementCondition(AchievementConditionType.SpecialFlag, "never_opened_box")
                },
                rewards = new List<AchievementReward>
                {
                    new AchievementReward(AchievementRewardType.Resource, ResourceType.Diamond, 10f),
                    new AchievementReward(AchievementRewardType.Title, "盒外之人")
                }
            };
        }

        /// <summary>
        /// 初始化进度追踪器
        /// </summary>
        private void InitializeProgressTrackers()
        {
            // 生存天数追踪
            progressTrackers["days_survived"] = new ProgressTracker
            {
                id = "days_survived",
                name = "生存天数",
                description = "记录玩家的生存天数",
                currentValue = 0f,
                milestones = new List<Milestone>
                {
                    new Milestone { threshold = 7f, name = "第一周", description = "生存第一周" },
                    new Milestone { threshold = 30f, name = "第一月", description = "生存第一个月" },
                    new Milestone { threshold = 100f, name = "百日", description = "生存一百天" }
                }
            };

            // 开盒数量追踪
            progressTrackers["boxes_opened"] = new ProgressTracker
            {
                id = "boxes_opened",
                name = "开盒数量",
                description = "记录玩家开启的盲盒总数",
                currentValue = 0f,
                milestones = new List<Milestone>
                {
                    new Milestone { threshold = 1f, name = "初次开盒", description = "开启第一个盲盒" },
                    new Milestone { threshold = 10f, name = "盲盒爱好者", description = "开启10个盲盒" },
                    new Milestone { threshold = 50f, name = "盲盒专家", description = "开启50个盲盒" },
                    new Milestone { threshold = 100f, name = "盲盒大师", description = "开启100个盲盒" }
                }
            };

            // 信用点累计追踪
            progressTrackers["credits_earned"] = new ProgressTracker
            {
                id = "credits_earned",
                name = "累计信用点",
                description = "记录玩家累计获得的信用点",
                currentValue = 0f,
                milestones = new List<Milestone>
                {
                    new Milestone { threshold = 1000f, name = "小富", description = "累计获得1000信用点" },
                    new Milestone { threshold = 10000f, name = "富有", description = "累计获得10000信用点" },
                    new Milestone { threshold = 100000f, name = "巨富", description = "累计获得100000信用点" }
                }
            };
        }

        public void Dispose()
        {
            achievements?.Clear();
            progressTrackers?.Clear();
            unlockHistory?.Clear();
        }
    }

    /// <summary>
    /// 成就
    /// </summary>
    [Serializable]
    public class Achievement
    {
        public string id;
        public string name;
        public string description;
        public AchievementCategory category;
        public AchievementRarity rarity;
        public bool isHidden;
        public string iconPath;
        public bool isUnlocked;
        public DateTime unlockedDate;
        public List<AchievementCondition> conditions;
        public List<AchievementReward> rewards;

        public Achievement()
        {
            conditions = new List<AchievementCondition>();
            rewards = new List<AchievementReward>();
        }
    }

    /// <summary>
    /// 成就条件
    /// </summary>
    [Serializable]
    public class AchievementCondition
    {
        public AchievementConditionType type;
        public float value;
        public string trackerId;
        public AttributeType? attributeType;
        public ResourceType? resourceType;
        public SocialClass? socialClass;
        public TalentType? talentType;
        public string eventId;
        public string flagName;

        public AchievementCondition(AchievementConditionType type, float value)
        {
            this.type = type;
            this.value = value;
        }

        public AchievementCondition(AchievementConditionType type, SocialClass socialClass)
        {
            this.type = type;
            this.socialClass = socialClass;
        }

        public AchievementCondition(AchievementConditionType type, string parameter)
        {
            this.type = type;
            switch (type)
            {
                case AchievementConditionType.SpecialFlag:
                    this.flagName = parameter;
                    break;
                case AchievementConditionType.CompletedEvent:
                    this.eventId = parameter;
                    break;
                default:
                    this.trackerId = parameter;
                    break;
            }
        }
    }

    /// <summary>
    /// 成就奖励
    /// </summary>
    [Serializable]
    public class AchievementReward
    {
        public AchievementRewardType type;
        public float value;
        public ResourceType? resourceType;
        public AttributeType? attributeType;
        public TalentType? talentType;
        public string eventId;
        public string title;

        public AchievementReward(AchievementRewardType type, ResourceType? resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }

        public AchievementReward(AchievementRewardType type, AttributeType? attributeType, float value)
        {
            this.type = type;
            this.attributeType = attributeType;
            this.value = value;
        }

        public AchievementReward(AchievementRewardType type, TalentType talentType)
        {
            this.type = type;
            this.talentType = talentType;
        }

        public AchievementReward(AchievementRewardType type, string parameter)
        {
            this.type = type;
            switch (type)
            {
                case AchievementRewardType.Title:
                    this.title = parameter;
                    break;
                case AchievementRewardType.UnlockEvent:
                    this.eventId = parameter;
                    break;
            }
        }
    }

    /// <summary>
    /// 进度追踪器
    /// </summary>
    [Serializable]
    public class ProgressTracker
    {
        public string id;
        public string name;
        public string description;
        public float currentValue;
        public float maxValue;
        public List<Milestone> milestones;

        public ProgressTracker()
        {
            milestones = new List<Milestone>();
            maxValue = float.MaxValue;
        }
    }

    /// <summary>
    /// 里程碑
    /// </summary>
    [Serializable]
    public class Milestone
    {
        public float threshold;
        public string name;
        public string description;
        public bool isReached;
        public DateTime reachedDate;
    }

    /// <summary>
    /// 成就解锁记录
    /// </summary>
    [Serializable]
    public class AchievementUnlock
    {
        public string achievementId;
        public string playerId;
        public DateTime unlockedDate;
        public int daysSurvived;
    }
}
