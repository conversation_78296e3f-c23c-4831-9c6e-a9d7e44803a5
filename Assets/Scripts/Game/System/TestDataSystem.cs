using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 测试数据系统 - 提供完整的测试数据和场景
    /// </summary>
    public class TestDataSystem : IDisposable
    {
        private Dictionary<string, TestScenario> testScenarios;
        private Dictionary<string, BlindBoxItem> testItems;
        private Dictionary<string, GameEvent> testEvents;
        private List<TestPlayerProfile> testProfiles;

        public TestDataSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            testScenarios = new Dictionary<string, TestScenario>();
            testItems = new Dictionary<string, BlindBoxItem>();
            testEvents = new Dictionary<string, GameEvent>();
            testProfiles = new List<TestPlayerProfile>();
            
            InitializeTestItems();
            InitializeTestEvents();
            InitializeTestScenarios();
            InitializeTestProfiles();
        }

        /// <summary>
        /// 获取测试场景
        /// </summary>
        public TestScenario GetTestScenario(string scenarioId)
        {
            return testScenarios.ContainsKey(scenarioId) ? testScenarios[scenarioId] : null;
        }

        /// <summary>
        /// 获取所有测试场景
        /// </summary>
        public List<TestScenario> GetAllTestScenarios()
        {
            return new List<TestScenario>(testScenarios.Values);
        }

        /// <summary>
        /// 应用测试场景
        /// </summary>
        public void ApplyTestScenario(string scenarioId, PlayerData playerData)
        {
            var scenario = GetTestScenario(scenarioId);
            if (scenario == null)
            {
                Debug.LogError($"测试场景不存在: {scenarioId}");
                return;
            }

            // 重置玩家数据
            playerData.Initialize();

            // 应用场景设置
            ApplyScenarioSettings(scenario, playerData);

            Debug.Log($"已应用测试场景: {scenario.name}");
        }

        /// <summary>
        /// 应用场景设置
        /// </summary>
        private void ApplyScenarioSettings(TestScenario scenario, PlayerData playerData)
        {
            // 设置基础属性
            foreach (var setting in scenario.attributeSettings)
            {
                playerData.attributes.SetAttributeValue(setting.Key, setting.Value);
            }

            // 设置资源
            foreach (var setting in scenario.resourceSettings)
            {
                var currentValue = playerData.resources.GetResource(setting.Key);
                if (setting.Value > currentValue)
                {
                    playerData.resources.AddResource(setting.Key, setting.Value - currentValue);
                }
                else if (setting.Value < currentValue)
                {
                    playerData.resources.ConsumeResource(setting.Key, currentValue - setting.Value);
                }
            }

            // 设置社会阶层
            if (scenario.socialClass.HasValue)
            {
                playerData.socialClass = scenario.socialClass.Value;
            }

            // 添加天赋
            foreach (var talent in scenario.talents)
            {
                playerData.AddTalent(talent);
            }

            // 添加状态效果
            foreach (var effect in scenario.statusEffects)
            {
                playerData.AddStatusEffect(effect);
            }

            // 设置进度
            playerData.daysSurvived = scenario.daysSurvived;
            playerData.totalBoxesOpened = scenario.totalBoxesOpened;

            // 解锁事件
            foreach (var eventId in scenario.unlockedEvents)
            {
                if (!playerData.unlockedEvents.Contains(eventId))
                {
                    playerData.unlockedEvents.Add(eventId);
                }
            }

            // 设置特殊标志
            playerData.hasMetResistance = scenario.hasMetResistance;
            playerData.knowsTruth = scenario.knowsTruth;
        }



        /// <summary>
        /// 创建随机测试玩家
        /// </summary>
        public PlayerData CreateRandomTestPlayer()
        {
            var playerData = new PlayerData();
            playerData.Initialize();

            // 随机化属性
            playerData.attributes.health = UnityEngine.Random.Range(20f, 100f);
            playerData.attributes.energy = UnityEngine.Random.Range(30f, 100f);
            playerData.attributes.luck = UnityEngine.Random.Range(1f, 9f);
            playerData.attributes.social = UnityEngine.Random.Range(20f, 80f);
            playerData.attributes.dependence = UnityEngine.Random.Range(0f, 80f);
            playerData.attributes.cognition = UnityEngine.Random.Range(40f, 90f);
            playerData.attributes.pollution = UnityEngine.Random.Range(0f, 60f);
            playerData.attributes.humanity = UnityEngine.Random.Range(30f, 100f);
            playerData.attributes.morality = UnityEngine.Random.Range(20f, 80f);

            // 随机化资源
            playerData.resources.credits = UnityEngine.Random.Range(50f, 1000f);
            playerData.resources.time = UnityEngine.Random.Range(20f, 200f);
            playerData.resources.diamond = UnityEngine.Random.Range(0f, 10f);
            playerData.resources.socialScore = UnityEngine.Random.Range(0f, 300f);

            // 随机进度
            playerData.daysSurvived = UnityEngine.Random.Range(1, 50);
            playerData.totalBoxesOpened = UnityEngine.Random.Range(5, 100);

            // 随机社会阶层
            var classes = new[] { SocialClass.Parasite, SocialClass.Worker, SocialClass.Chosen };
            playerData.socialClass = classes[UnityEngine.Random.Range(0, classes.Length)];

            return playerData;
        }

        /// <summary>
        /// 获取测试物品
        /// </summary>
        public BlindBoxItem GetTestItem(string itemId)
        {
            return testItems.ContainsKey(itemId) ? testItems[itemId] : null;
        }

        /// <summary>
        /// 获取测试事件
        /// </summary>
        public GameEvent GetTestEvent(string eventId)
        {
            return testEvents.ContainsKey(eventId) ? testEvents[eventId] : null;
        }

        /// <summary>
        /// 初始化测试物品
        /// </summary>
        private void InitializeTestItems()
        {
            // 基础物品
            testItems["test_food"] = CreateTestItem("test_food", "测试食物", BlindBoxContentType.Food, 20f, true,
                new List<ItemEffect> { new ItemEffect("health", 10f), new ItemEffect("energy", 5f) });

            testItems["test_poison"] = CreateTestItem("test_poison", "有毒食物", BlindBoxContentType.Food, -10f, false,
                new List<ItemEffect> { new ItemEffect("health", -15f), new ItemEffect(StatusEffectType.Sickness, 3, 1f) });

            testItems["test_medicine"] = CreateTestItem("test_medicine", "治疗药剂", BlindBoxContentType.Medicine, 50f, true,
                new List<ItemEffect> { new ItemEffect("health", 30f), new ItemEffect(StatusEffectType.Sickness, -1, 1f) });

            testItems["test_job"] = CreateTestItem("test_job", "临时工作", BlindBoxContentType.Job, 100f, true,
                new List<ItemEffect> { new ItemEffect(ResourceType.Credits, 200f), new ItemEffect("social", 5f) });

            testItems["test_tech"] = CreateTestItem("test_tech", "神秘科技", BlindBoxContentType.Technology, 200f, true,
                new List<ItemEffect> { new ItemEffect("cognition", 10f), new ItemEffect("pollution", 5f) });

            testItems["test_memory"] = CreateTestItem("test_memory", "记忆碎片", BlindBoxContentType.Memory, 150f, true,
                new List<ItemEffect> { new ItemEffect("cognition", 5f), new ItemEffect("humanity", -2f) });

            testItems["test_talent"] = CreateTestItem("test_talent", "天赋觉醒", BlindBoxContentType.Talent, 500f, true,
                new List<ItemEffect> { new ItemEffect("luck", 2f) });

            testItems["test_currency"] = CreateTestItem("test_currency", "钻石奖励", BlindBoxContentType.Currency, 1000f, true,
                new List<ItemEffect> { new ItemEffect(ResourceType.Diamond, 3f) });

            testItems["test_trap"] = CreateTestItem("test_trap", "系统陷阱", BlindBoxContentType.Trap, -50f, false,
                new List<ItemEffect> { new ItemEffect(StatusEffectType.SystemTracking, 7, 1f), new ItemEffect("morality", -10f) });

            testItems["test_virus"] = CreateTestItem("test_virus", "认知病毒", BlindBoxContentType.Virus, -100f, false,
                new List<ItemEffect> { new ItemEffect(StatusEffectType.CognitiveDamage, 5, 2f), new ItemEffect("cognition", -20f) });
        }

        /// <summary>
        /// 创建测试物品
        /// </summary>
        private BlindBoxItem CreateTestItem(string id, string name, BlindBoxContentType contentType, 
            float value, bool isPositive, List<ItemEffect> effects)
        {
            var item = new BlindBoxItem(id, name, contentType, value, isPositive);
            item.description = $"测试物品：{name}";
            item.effects = effects ?? new List<ItemEffect>();
            return item;
        }

        /// <summary>
        /// 初始化测试事件
        /// </summary>
        private void InitializeTestEvents()
        {
            // 测试系统故障事件
            testEvents["test_system_glitch"] = new GameEvent
            {
                id = "test_system_glitch",
                title = "测试系统故障",
                description = "这是一个测试用的系统故障事件",
                type = EventType.System,
                baseWeight = 1f,
                cooldownHours = 1f,
                timeoutMinutes = 5f,
                triggerConditions = new List<EventCondition>(),
                immediateEffects = new List<EventEffect>
                {
                    new EventEffect(EventEffectType.AttributeChange, "cognition", 5f)
                },
                choices = new List<EventChoice>
                {
                    new EventChoice
                    {
                        text = "调查故障",
                        description = "深入调查系统故障的原因",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.AttributeChange, "cognition", 10f),
                            new EventEffect(EventEffectType.AddStatusEffect, StatusEffectType.SystemTracking, 1f, 3f)
                        }
                    },
                    new EventChoice
                    {
                        text = "忽略故障",
                        description = "假装没有发现任何异常",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.AttributeChange, "dependence", 5f)
                        }
                    }
                }
            };

            // 测试反抗事件
            testEvents["test_resistance_contact"] = new GameEvent
            {
                id = "test_resistance_contact",
                title = "测试反抗接触",
                description = "破盒者组织的测试接触事件",
                type = EventType.Resistance,
                baseWeight = 0.5f,
                cooldownHours = 24f,
                timeoutMinutes = 10f,
                triggerConditions = new List<EventCondition>(),
                immediateEffects = new List<EventEffect>(),
                choices = new List<EventChoice>
                {
                    new EventChoice
                    {
                        text = "加入反抗",
                        description = "决定加入破盒者组织",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.StoryProgress, "resistance_member"),
                            new EventEffect(EventEffectType.AttributeChange, "morality", 10f)
                        }
                    },
                    new EventChoice
                    {
                        text = "举报组织",
                        description = "向系统举报反抗组织",
                        effects = new List<EventEffect>
                        {
                            new EventEffect(EventEffectType.ResourceChange, ResourceType.SocialScore, 50f),
                            new EventEffect(EventEffectType.AttributeChange, "morality", -20f)
                        }
                    }
                }
            };
        }

        /// <summary>
        /// 初始化测试场景
        /// </summary>
        private void InitializeTestScenarios()
        {
            // 新手场景
            testScenarios["newbie"] = new TestScenario
            {
                id = "newbie",
                name = "新手玩家",
                description = "刚进入盲盒世界的新玩家",
                attributeSettings = new Dictionary<AttributeType, float>
                {
                    [AttributeType.Health] = 100f,
                    [AttributeType.Energy] = 100f,
                    [AttributeType.Luck] = 5f,
                    [AttributeType.Social] = 50f,
                    [AttributeType.Dependence] = 0f,
                    [AttributeType.Cognition] = 100f,
                    [AttributeType.Pollution] = 0f,
                    [AttributeType.Humanity] = 100f,
                    [AttributeType.Morality] = 50f
                },
                resourceSettings = new Dictionary<ResourceType, float>
                {
                    [ResourceType.Credits] = 200f,
                    [ResourceType.Time] = 100f,
                    [ResourceType.Diamond] = 0f,
                    [ResourceType.SocialScore] = 0f
                },
                socialClass = SocialClass.Parasite,
                daysSurvived = 0,
                totalBoxesOpened = 0
            };

            // 中期玩家场景
            testScenarios["mid_game"] = new TestScenario
            {
                id = "mid_game",
                name = "中期玩家",
                description = "已经适应系统的中期玩家",
                attributeSettings = new Dictionary<AttributeType, float>
                {
                    [AttributeType.Health] = 80f,
                    [AttributeType.Energy] = 70f,
                    [AttributeType.Luck] = 6f,
                    [AttributeType.Social] = 65f,
                    [AttributeType.Dependence] = 40f,
                    [AttributeType.Cognition] = 85f,
                    [AttributeType.Pollution] = 20f,
                    [AttributeType.Humanity] = 80f,
                    [AttributeType.Morality] = 45f
                },
                resourceSettings = new Dictionary<ResourceType, float>
                {
                    [ResourceType.Credits] = 800f,
                    [ResourceType.Time] = 150f,
                    [ResourceType.Diamond] = 2f,
                    [ResourceType.SocialScore] = 120f
                },
                socialClass = SocialClass.Worker,
                daysSurvived = 25,
                totalBoxesOpened = 50,
                talents = new List<TalentType> { TalentType.SocialMaster },
                statusEffects = new List<StatusEffect>
                {
                    new StatusEffect(StatusEffectType.WorkEfficiency, 1.2f, 5, "工作效率提升")
                }
            };

            // 觉醒者场景
            testScenarios["awakened"] = new TestScenario
            {
                id = "awakened",
                name = "觉醒者",
                description = "开始质疑系统的觉醒玩家",
                attributeSettings = new Dictionary<AttributeType, float>
                {
                    [AttributeType.Health] = 60f,
                    [AttributeType.Energy] = 50f,
                    [AttributeType.Luck] = 7f,
                    [AttributeType.Social] = 70f,
                    [AttributeType.Dependence] = 30f,
                    [AttributeType.Cognition] = 90f,
                    [AttributeType.Pollution] = 40f,
                    [AttributeType.Humanity] = 70f,
                    [AttributeType.Morality] = 60f
                },
                resourceSettings = new Dictionary<ResourceType, float>
                {
                    [ResourceType.Credits] = 500f,
                    [ResourceType.Time] = 80f,
                    [ResourceType.Diamond] = 5f,
                    [ResourceType.SocialScore] = 200f
                },
                socialClass = SocialClass.Worker,
                daysSurvived = 45,
                totalBoxesOpened = 80,
                hasMetResistance = true,
                talents = new List<TalentType> { TalentType.HackerGenius, TalentType.SocialMaster },
                unlockedEvents = new List<string> { "system_glitch", "resistance_contact" }
            };

            // 神选者场景
            testScenarios["chosen_one"] = new TestScenario
            {
                id = "chosen_one",
                name = "神选者",
                description = "已经晋升到顶层的神选者",
                attributeSettings = new Dictionary<AttributeType, float>
                {
                    [AttributeType.Health] = 90f,
                    [AttributeType.Energy] = 80f,
                    [AttributeType.Luck] = 8f,
                    [AttributeType.Social] = 85f,
                    [AttributeType.Dependence] = 60f,
                    [AttributeType.Cognition] = 95f,
                    [AttributeType.Pollution] = 70f,
                    [AttributeType.Humanity] = 40f,
                    [AttributeType.Morality] = 30f
                },
                resourceSettings = new Dictionary<ResourceType, float>
                {
                    [ResourceType.Credits] = 2000f,
                    [ResourceType.Time] = 200f,
                    [ResourceType.Diamond] = 15f,
                    [ResourceType.SocialScore] = 500f
                },
                socialClass = SocialClass.Chosen,
                daysSurvived = 80,
                totalBoxesOpened = 150,
                talents = new List<TalentType> { TalentType.RichKid, TalentType.SocialMaster },
                unlockedEvents = new List<string> { "privilege_probability_edit", "system_access" }
            };

            // 危机场景
            testScenarios["crisis"] = new TestScenario
            {
                id = "crisis",
                name = "生存危机",
                description = "面临生存危机的玩家",
                attributeSettings = new Dictionary<AttributeType, float>
                {
                    [AttributeType.Health] = 15f,
                    [AttributeType.Energy] = 20f,
                    [AttributeType.Luck] = 3f,
                    [AttributeType.Social] = 30f,
                    [AttributeType.Dependence] = 80f,
                    [AttributeType.Cognition] = 40f,
                    [AttributeType.Pollution] = 60f,
                    [AttributeType.Humanity] = 30f,
                    [AttributeType.Morality] = 20f
                },
                resourceSettings = new Dictionary<ResourceType, float>
                {
                    [ResourceType.Credits] = 10f,
                    [ResourceType.Time] = 5f,
                    [ResourceType.Diamond] = 0f,
                    [ResourceType.SocialScore] = 0f
                },
                socialClass = SocialClass.Parasite,
                daysSurvived = 30,
                totalBoxesOpened = 100,
                statusEffects = new List<StatusEffect>
                {
                    new StatusEffect(StatusEffectType.Sickness, 2f, 5, "严重疾病"),
                    new StatusEffect(StatusEffectType.Fatigue, 1.5f, 3, "极度疲劳"),
                    new StatusEffect(StatusEffectType.SystemTracking, 1f, 10, "被系统监控")
                }
            };
        }

        /// <summary>
        /// 初始化测试玩家档案
        /// </summary>
        private void InitializeTestProfiles()
        {
            testProfiles.Add(new TestPlayerProfile
            {
                name = "幸运儿",
                description = "拥有极高幸运值的测试玩家",
                baseAttributes = new Dictionary<string, float> { ["luck"] = 10f },
                startingTalents = new List<TalentType> { TalentType.GamblerGene }
            });

            testProfiles.Add(new TestPlayerProfile
            {
                name = "社交达人",
                description = "专精社交的测试玩家",
                baseAttributes = new Dictionary<string, float> { ["social"] = 90f },
                startingTalents = new List<TalentType> { TalentType.SocialMaster }
            });

            testProfiles.Add(new TestPlayerProfile
            {
                name = "黑客",
                description = "具有黑客技能的测试玩家",
                baseAttributes = new Dictionary<string, float> { ["cognition"] = 95f },
                startingTalents = new List<TalentType> { TalentType.HackerGenius }
            });
        }

        public void Dispose()
        {
            testScenarios?.Clear();
            testItems?.Clear();
            testEvents?.Clear();
            testProfiles?.Clear();
        }
    }

    /// <summary>
    /// 测试场景
    /// </summary>
    [Serializable]
    public class TestScenario
    {
        public string id;
        public string name;
        public string description;
        public Dictionary<AttributeType, float> attributeSettings;
        public Dictionary<ResourceType, float> resourceSettings;
        public SocialClass? socialClass;
        public int daysSurvived;
        public int totalBoxesOpened;
        public bool hasMetResistance;
        public bool knowsTruth;
        public List<TalentType> talents;
        public List<StatusEffect> statusEffects;
        public List<string> unlockedEvents;

        public TestScenario()
        {
            attributeSettings = new Dictionary<AttributeType, float>();
            resourceSettings = new Dictionary<ResourceType, float>();
            talents = new List<TalentType>();
            statusEffects = new List<StatusEffect>();
            unlockedEvents = new List<string>();
        }
    }

    /// <summary>
    /// 测试玩家档案
    /// </summary>
    [Serializable]
    public class TestPlayerProfile
    {
        public string name;
        public string description;
        public Dictionary<string, float> baseAttributes;
        public Dictionary<ResourceType, float> baseResources;
        public List<TalentType> startingTalents;
        public List<StatusEffect> startingEffects;

        public TestPlayerProfile()
        {
            baseAttributes = new Dictionary<string, float>();
            baseResources = new Dictionary<ResourceType, float>();
            startingTalents = new List<TalentType>();
            startingEffects = new List<StatusEffect>();
        }
    }

    /// <summary>
    /// 测试配置
    /// </summary>
    [Serializable]
    public class TestConfiguration
    {
        public bool enableDebugMode;
        public bool skipTutorial;
        public float timeScale;
        public bool unlimitedResources;
        public bool godMode;
        public List<string> enabledCheats;

        public TestConfiguration()
        {
            enabledCheats = new List<string>();
            timeScale = 1f;
        }
    }

    /// <summary>
    /// 测试结果
    /// </summary>
    [Serializable]
    public class TestResult
    {
        public string testName;
        public bool success;
        public string errorMessage;
        public float executionTime;
        public Dictionary<string, object> metrics;
        public DateTime timestamp;

        public TestResult()
        {
            metrics = new Dictionary<string, object>();
            timestamp = DateTime.Now;
        }
    }
}
