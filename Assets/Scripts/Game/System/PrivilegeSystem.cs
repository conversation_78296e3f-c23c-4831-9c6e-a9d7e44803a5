using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 特权系统 - 管理社会阶层特权的使用
    /// </summary>
    public class PrivilegeSystem : IDisposable
    {
        private Dictionary<string, PrivilegeConfig> privilegeConfigs;
        private List<PrivilegeUsage> usageHistory;
        private Dictionary<string, float> privilegeCooldowns;

        public event Action<string, PrivilegeResult> OnPrivilegeUsed;
        public event Action<string> OnPrivilegeUnlocked;

        public PrivilegeSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            privilegeConfigs = new Dictionary<string, PrivilegeConfig>();
            usageHistory = new List<PrivilegeUsage>();
            privilegeCooldowns = new Dictionary<string, float>();
            
            InitializePrivilegeConfigs();
        }

        /// <summary>
        /// 使用特权
        /// </summary>
        public PrivilegeResult UsePrivilege(PlayerData playerData, string privilegeId, 
            Dictionary<string, object> parameters = null)
        {
            var result = new PrivilegeResult
            {
                privilegeId = privilegeId,
                success = false
            };

            // 检查特权是否存在
            if (!privilegeConfigs.ContainsKey(privilegeId))
            {
                result.errorMessage = "特权不存在";
                return result;
            }

            var config = privilegeConfigs[privilegeId];

            // 检查玩家是否拥有此特权
            if (!HasPrivilege(playerData, privilegeId))
            {
                result.errorMessage = "您没有此特权";
                return result;
            }

            // 检查冷却时间
            if (IsOnCooldown(privilegeId))
            {
                result.errorMessage = $"特权冷却中，剩余时间：{GetRemainingCooldown(privilegeId):F1}秒";
                return result;
            }

            // 检查使用条件
            if (!CheckPrivilegeConditions(playerData, config))
            {
                result.errorMessage = "不满足使用条件";
                return result;
            }

            // 检查消耗
            if (!CanAffordPrivilege(playerData, config))
            {
                result.errorMessage = "资源不足";
                return result;
            }

            // 执行特权效果
            result = ExecutePrivilege(playerData, config, parameters);

            // 记录使用历史
            RecordPrivilegeUsage(playerData, privilegeId, result);

            // 设置冷却时间
            SetCooldown(privilegeId, config.cooldownSeconds);

            OnPrivilegeUsed?.Invoke(privilegeId, result);
            return result;
        }

        /// <summary>
        /// 检查玩家是否拥有特权
        /// </summary>
        private bool HasPrivilege(PlayerData playerData, string privilegeId)
        {
            var config = privilegeConfigs[privilegeId];
            
            // 检查社会阶层要求
            if ((int)playerData.socialClass < (int)config.requiredSocialClass)
                return false;

            // 检查是否已解锁
            return playerData.unlockedEvents.Contains($"privilege_{privilegeId}");
        }

        /// <summary>
        /// 检查特权使用条件
        /// </summary>
        private bool CheckPrivilegeConditions(PlayerData playerData, PrivilegeConfig config)
        {
            foreach (var condition in config.conditions)
            {
                if (!CheckCondition(playerData, condition))
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 检查单个条件
        /// </summary>
        private bool CheckCondition(PlayerData playerData, PrivilegeCondition condition)
        {
            switch (condition.type)
            {
                case PrivilegeConditionType.AttributeThreshold:
                    return GetAttributeValue(playerData, condition.attributeType) >= condition.value;
                
                case PrivilegeConditionType.ResourceThreshold:
                    return playerData.resources.GetResource(condition.resourceType.Value) >= condition.value;
                
                case PrivilegeConditionType.NoNegativeStatus:
                    return !HasNegativeStatusEffects(playerData);
                
                case PrivilegeConditionType.TimeOfDay:
                    // 简化实现：假设总是满足时间条件
                    return true;
                
                default:
                    return true;
            }
        }

        /// <summary>
        /// 检查是否能承担特权消耗
        /// </summary>
        private bool CanAffordPrivilege(PlayerData playerData, PrivilegeConfig config)
        {
            foreach (var cost in config.costs)
            {
                switch (cost.type)
                {
                    case PrivilegeCostType.Resource:
                        if (playerData.resources.GetResource(cost.resourceType.Value) < cost.value)
                            return false;
                        break;
                    
                    case PrivilegeCostType.Attribute:
                        if (GetAttributeValue(playerData, cost.attributeType) < cost.value)
                            return false;
                        break;
                }
            }
            return true;
        }

        /// <summary>
        /// 执行特权效果
        /// </summary>
        private PrivilegeResult ExecutePrivilege(PlayerData playerData, PrivilegeConfig config, 
            Dictionary<string, object> parameters)
        {
            var result = new PrivilegeResult
            {
                privilegeId = config.id,
                success = true
            };

            // 消耗资源
            ApplyPrivilegeCosts(playerData, config);

            // 执行特权效果
            switch (config.id)
            {
                case "skip_queue":
                    result = ExecuteSkipQueue(playerData, parameters);
                    break;
                
                case "probability_hint":
                    result = ExecuteProbabilityHint(playerData, parameters);
                    break;
                
                case "resource_bonus":
                    result = ExecuteResourceBonus(playerData, parameters);
                    break;
                
                case "probability_edit":
                    result = ExecuteProbabilityEdit(playerData, parameters);
                    break;
                
                case "system_override":
                    result = ExecuteSystemOverride(playerData, parameters);
                    break;
                
                case "memory_access":
                    result = ExecuteMemoryAccess(playerData, parameters);
                    break;
                
                default:
                    result.success = false;
                    result.errorMessage = "未知的特权类型";
                    break;
            }

            return result;
        }

        /// <summary>
        /// 执行跳过排队特权
        /// </summary>
        private PrivilegeResult ExecuteSkipQueue(PlayerData playerData, Dictionary<string, object> parameters)
        {
            return new PrivilegeResult
            {
                privilegeId = "skip_queue",
                success = true,
                message = "成功跳过排队，可以立即进行下一个操作",
                effects = new List<string> { "下次操作无需等待时间" }
            };
        }

        /// <summary>
        /// 执行概率提示特权
        /// </summary>
        private PrivilegeResult ExecuteProbabilityHint(PlayerData playerData, Dictionary<string, object> parameters)
        {
            var result = new PrivilegeResult
            {
                privilegeId = "probability_hint",
                success = true,
                message = "获得了盲盒概率的内部信息"
            };

            // 模拟概率提示
            var hints = new List<string>
            {
                "基础盲盒：当前空盒概率较高，建议谨慎",
                "生活盲盒：系统资源池充足，中奖率正常",
                "高级盲盒：检测到概率调整，实际中奖率可能偏低"
            };

            result.effects = hints;
            return result;
        }

        /// <summary>
        /// 执行资源奖励特权
        /// </summary>
        private PrivilegeResult ExecuteResourceBonus(PlayerData playerData, Dictionary<string, object> parameters)
        {
            var bonusAmount = 200f + (playerData.attributes.social * 2f);
            playerData.resources.AddResource(ResourceType.Credits, bonusAmount);

            return new PrivilegeResult
            {
                privilegeId = "resource_bonus",
                success = true,
                message = $"获得特权资源奖励：{bonusAmount:F0} 信用点",
                effects = new List<string> { $"信用点 +{bonusAmount:F0}" }
            };
        }

        /// <summary>
        /// 执行概率编辑特权
        /// </summary>
        private PrivilegeResult ExecuteProbabilityEdit(PlayerData playerData, Dictionary<string, object> parameters)
        {
            // 这是最高级的特权，只有神选者才能使用
            if (playerData.socialClass != SocialClass.Chosen)
            {
                return new PrivilegeResult
                {
                    privilegeId = "probability_edit",
                    success = false,
                    errorMessage = "权限不足"
                };
            }

            // 增加污染度作为代价
            playerData.attributes.ModifyAttribute(AttributeType.Pollution, 10f);

            return new PrivilegeResult
            {
                privilegeId = "probability_edit",
                success = true,
                message = "暂时获得了概率编辑权限，下次开盒时可以微调概率",
                effects = new List<string> 
                { 
                    "下次盲盒开启时概率+15%",
                    "污染度 +10"
                }
            };
        }

        /// <summary>
        /// 执行系统覆盖特权
        /// </summary>
        private PrivilegeResult ExecuteSystemOverride(PlayerData playerData, Dictionary<string, object> parameters)
        {
            return new PrivilegeResult
            {
                privilegeId = "system_override",
                success = true,
                message = "临时覆盖了系统限制",
                effects = new List<string> { "接下来24小时内免疫负面随机事件" }
            };
        }

        /// <summary>
        /// 执行记忆访问特权
        /// </summary>
        private PrivilegeResult ExecuteMemoryAccess(PlayerData playerData, Dictionary<string, object> parameters)
        {
            // 恢复一些被抹除的记忆，但可能带来痛苦
            playerData.attributes.ModifyAttribute(AttributeType.Cognition, 5f);
            playerData.attributes.ModifyAttribute(AttributeType.Humanity, -2f);

            return new PrivilegeResult
            {
                privilegeId = "memory_access",
                success = true,
                message = "访问了被封存的记忆片段，获得了一些真相",
                effects = new List<string> 
                { 
                    "认知值 +5",
                    "人性值 -2",
                    "解锁记忆片段"
                }
            };
        }

        /// <summary>
        /// 应用特权消耗
        /// </summary>
        private void ApplyPrivilegeCosts(PlayerData playerData, PrivilegeConfig config)
        {
            foreach (var cost in config.costs)
            {
                switch (cost.type)
                {
                    case PrivilegeCostType.Resource:
                        playerData.resources.ConsumeResource(cost.resourceType.Value, cost.value);
                        break;
                    
                    case PrivilegeCostType.Attribute:
                        playerData.attributes.ModifyAttribute(cost.attributeType, -cost.value);
                        break;
                }
            }
        }

        /// <summary>
        /// 检查冷却时间
        /// </summary>
        private bool IsOnCooldown(string privilegeId)
        {
            if (!privilegeCooldowns.ContainsKey(privilegeId))
                return false;
            
            return privilegeCooldowns[privilegeId] > Time.time;
        }

        /// <summary>
        /// 获取剩余冷却时间
        /// </summary>
        private float GetRemainingCooldown(string privilegeId)
        {
            if (!privilegeCooldowns.ContainsKey(privilegeId))
                return 0f;
            
            return Mathf.Max(0f, privilegeCooldowns[privilegeId] - Time.time);
        }

        /// <summary>
        /// 设置冷却时间
        /// </summary>
        private void SetCooldown(string privilegeId, float cooldownSeconds)
        {
            privilegeCooldowns[privilegeId] = Time.time + cooldownSeconds;
        }

        /// <summary>
        /// 记录特权使用历史
        /// </summary>
        private void RecordPrivilegeUsage(PlayerData playerData, string privilegeId, PrivilegeResult result)
        {
            var usage = new PrivilegeUsage
            {
                playerId = playerData.playerName,
                privilegeId = privilegeId,
                timestamp = DateTime.Now,
                success = result.success,
                result = result.message
            };

            usageHistory.Add(usage);

            // 限制历史记录数量
            if (usageHistory.Count > 100)
            {
                usageHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }


        /// <summary>
        /// 检查是否有负面状态效果
        /// </summary>
        private bool HasNegativeStatusEffects(PlayerData playerData)
        {
            var negativeEffects = new[]
            {
                StatusEffectType.Sickness,
                StatusEffectType.Fatigue,
                StatusEffectType.Depression,
                StatusEffectType.SystemTracking
            };

            foreach (var effect in negativeEffects)
            {
                if (playerData.HasStatusEffect(effect))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 初始化特权配置
        /// </summary>
        private void InitializePrivilegeConfigs()
        {
            // 跳过排队
            privilegeConfigs["skip_queue"] = new PrivilegeConfig
            {
                id = "skip_queue",
                name = "跳过排队",
                description = "跳过等待时间，立即执行操作",
                requiredSocialClass = SocialClass.Worker,
                cooldownSeconds = 3600f, // 1小时
                costs = new List<PrivilegeCost>
                {
                    new PrivilegeCost(PrivilegeCostType.Resource, ResourceType.SocialScore, 10f)
                },
                conditions = new List<PrivilegeCondition>()
            };

            // 概率提示
            privilegeConfigs["probability_hint"] = new PrivilegeConfig
            {
                id = "probability_hint",
                name = "概率提示",
                description = "获得盲盒真实概率的内部信息",
                requiredSocialClass = SocialClass.Worker,
                cooldownSeconds = 7200f, // 2小时
                costs = new List<PrivilegeCost>
                {
                    new PrivilegeCost(PrivilegeCostType.Resource, ResourceType.Credits, 100f)
                },
                conditions = new List<PrivilegeCondition>()
            };

            // 资源奖励
            privilegeConfigs["resource_bonus"] = new PrivilegeConfig
            {
                id = "resource_bonus",
                name = "特权津贴",
                description = "获得额外的资源奖励",
                requiredSocialClass = SocialClass.Worker,
                cooldownSeconds = 86400f, // 24小时
                costs = new List<PrivilegeCost>(),
                conditions = new List<PrivilegeCondition>()
            };

            // 概率编辑
            privilegeConfigs["probability_edit"] = new PrivilegeConfig
            {
                id = "probability_edit",
                name = "概率编辑",
                description = "直接编辑盲盒概率（有限制）",
                requiredSocialClass = SocialClass.Chosen,
                cooldownSeconds = 172800f, // 48小时
                costs = new List<PrivilegeCost>
                {
                    new PrivilegeCost(PrivilegeCostType.Resource, ResourceType.Diamond, 1f),
                    new PrivilegeCost(PrivilegeCostType.Attribute, AttributeType.Humanity, 5f)
                },
                conditions = new List<PrivilegeCondition>
                {
                    new PrivilegeCondition(PrivilegeConditionType.AttributeThreshold, AttributeType.Cognition, 80f)
                }
            };
        }

        public void Dispose()
        {
            privilegeConfigs?.Clear();
            usageHistory?.Clear();
            privilegeCooldowns?.Clear();
        }
    }

    /// <summary>
    /// 特权配置
    /// </summary>
    [Serializable]
    public class PrivilegeConfig
    {
        public string id;
        public string name;
        public string description;
        public SocialClass requiredSocialClass;
        public float cooldownSeconds;
        public List<PrivilegeCost> costs;
        public List<PrivilegeCondition> conditions;

        public PrivilegeConfig()
        {
            costs = new List<PrivilegeCost>();
            conditions = new List<PrivilegeCondition>();
        }
    }

    /// <summary>
    /// 特权消耗
    /// </summary>
    [Serializable]
    public class PrivilegeCost
    {
        public PrivilegeCostType type;
        public ResourceType? resourceType;
        public AttributeType attributeType;
        public float value;

        public PrivilegeCost(PrivilegeCostType type, ResourceType resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }

        public PrivilegeCost(PrivilegeCostType type, AttributeType attributeType, float value)
        {
            this.type = type;
            this.attributeType = attributeType;
            this.value = value;
        }
    }

    /// <summary>
    /// 特权使用条件
    /// </summary>
    [Serializable]
    public class PrivilegeCondition
    {
        public PrivilegeConditionType type;
        public AttributeType attributeType;
        public ResourceType? resourceType;
        public float value;

        public PrivilegeCondition(PrivilegeConditionType type, AttributeType attributeType, float value)
        {
            this.type = type;
            this.attributeType = attributeType;
            this.value = value;
        }

        public PrivilegeCondition(PrivilegeConditionType type, ResourceType resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }
    }

    /// <summary>
    /// 特权使用结果
    /// </summary>
    [Serializable]
    public class PrivilegeResult
    {
        public string privilegeId;
        public bool success;
        public string message;
        public string errorMessage;
        public List<string> effects;

        public PrivilegeResult()
        {
            effects = new List<string>();
        }
    }

    /// <summary>
    /// 特权使用历史
    /// </summary>
    [Serializable]
    public class PrivilegeUsage
    {
        public string playerId;
        public string privilegeId;
        public DateTime timestamp;
        public bool success;
        public string result;
    }

    /// <summary>
    /// 特权消耗类型
    /// </summary>
    public enum PrivilegeCostType
    {
        Resource,       // 资源消耗
        Attribute       // 属性消耗
    }

    /// <summary>
    /// 特权条件类型
    /// </summary>
    public enum PrivilegeConditionType
    {
        AttributeThreshold,     // 属性阈值
        ResourceThreshold,      // 资源阈值
        NoNegativeStatus,       // 无负面状态
        TimeOfDay              // 时间条件
    }
}
