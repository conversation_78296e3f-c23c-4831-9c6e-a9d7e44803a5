using System;
using System.Collections.Generic;
using System.Linq;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 天赋管理器 - 管理玩家天赋和状态效果
    /// </summary>
    public class TalentSystem : IDisposable
    {
        private Dictionary<TalentType, TalentConfig> talentConfigs;
        private Dictionary<StatusEffectType, StatusEffectConfig> statusEffectConfigs;

        public event Action<TalentType> OnTalentAcquired;
        public event Action<StatusEffect> OnStatusEffectAdded;
        public event Action<StatusEffectType> OnStatusEffectRemoved;

        public TalentSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            // 从CSV文件加载数据
            BoxOfFate.Game.TalentDataLoader.LoadAllData();
            talentConfigs = BoxOfFate.Game.TalentDataLoader.GetTalentConfigs();
            statusEffectConfigs = BoxOfFate.Game.TalentDataLoader.GetStatusEffectConfigs();
        }

        /// <summary>
        /// 获得天赋
        /// </summary>
        public bool AcquireTalent(PlayerData playerData, TalentType talentType)
        {
            if (playerData.HasTalent(talentType))
            {
                return false; // 已经拥有该天赋
            }

            var config = talentConfigs[talentType];
            
            // 检查获得条件
            if (!CheckTalentRequirements(playerData, config))
            {
                return false;
            }

            // 添加天赋
            playerData.AddTalent(talentType);
            
            // 应用天赋效果
            ApplyTalentEffects(playerData, config);
            
            OnTalentAcquired?.Invoke(talentType);
            return true;
        }

        /// <summary>
        /// 检查天赋获得条件
        /// </summary>
        private bool CheckTalentRequirements(PlayerData playerData, TalentConfig config)
        {
            foreach (GameCondition condition in config.requirements)
            {
                if (!condition.Check(playerData))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 应用天赋效果
        /// </summary>
        private void ApplyTalentEffects(PlayerData playerData, TalentConfig config)
        {
            foreach (GameEffect effect in config.effects)
            {
                effect.Apply(playerData);
            }
        }


        /// <summary>
        /// 授予特殊能力
        /// </summary>
        private void GrantSpecialAbility(PlayerData playerData, string abilityId)
        {
            switch (abilityId)
            {
                case "work_efficiency_boost":
                    // 工作效率提升：工作收入增加50%
                    playerData.SetSpecialFlag("work_efficiency_boost", true);
                    Debug.Log("授予特殊能力: 工作效率提升");
                    break;

                case "genius_insight":
                    // 天才洞察：可以预知某些事件结果
                    playerData.SetSpecialFlag("genius_insight", true);
                    Debug.Log("授予特殊能力: 天才洞察");
                    break;

                case "mental_instability":
                    // 精神不稳定：随机触发特殊事件
                    playerData.SetSpecialFlag("mental_instability", true);
                    Debug.Log("授予特殊能力: 精神不稳定");
                    break;

                case "box_content_preview":
                    // 盲盒内容预览：可以看到盲盒内容
                    playerData.SetSpecialFlag("box_content_preview", true);
                    Debug.Log("授予特殊能力: 盲盒内容预览");
                    break;

                case "trap_avoidance":
                    // 陷阱规避：自动避开负面内容
                    playerData.SetSpecialFlag("trap_avoidance", true);
                    Debug.Log("授予特殊能力: 陷阱规避");
                    break;

                case "rare_event_trigger":
                    // 稀有事件触发：更容易遇到稀有事件
                    playerData.SetSpecialFlag("rare_event_trigger", true);
                    Debug.Log("授予特殊能力: 稀有事件触发");
                    break;

                default:
                    Debug.Log($"未知特殊能力: {abilityId}");
                    break;
            }
        }

        /// <summary>
        /// 获取天赋对盲盒概率的影响
        /// </summary>
        public float GetTalentProbabilityModifier(PlayerData playerData, BlindBoxContentType contentType)
        {
            float modifier = 0f;

            foreach (var talentType in playerData.talents)
            {
                var config = talentConfigs[talentType];
                foreach (var effect in config.effects)
                {
                    if (effect.type == GameEffectType.ProbabilityModifier)
                    {
                        modifier += CalculateProbabilityModifier(effect, contentType);
                    }
                }
            }

            return modifier;
        }

        /// <summary>
        /// 计算概率修正值
        /// </summary>
        private float CalculateProbabilityModifier(GameEffect effect, BlindBoxContentType contentType)
        {
            // 根据天赋类型和盲盒类型计算修正值
            switch (effect.stringValue)
            {
                case "gambler_gene":
                    // 赌徒基因：惊喜和惩罚概率都增加
                    return contentType == BlindBoxContentType.Empty ? effect.value : -effect.value;

                case "lucky_boost":
                    // 幸运提升：正面物品概率增加
                    return IsPositiveContent(contentType) ? effect.value : 0f;

                case "intuition":
                    // 直觉：所有概率轻微提升
                    return effect.value * 0.5f;

                case "positive_outcome":
                    // 盲盒直觉：大幅提升正面结果概率
                    return IsPositiveContent(contentType) ? effect.value : -effect.value * 0.5f;

                default:
                    return 0f;
            }
        }

        /// <summary>
        /// 判断是否为正面内容
        /// </summary>
        private bool IsPositiveContent(BlindBoxContentType contentType)
        {
            var positiveTypes = new[]
            {
                BlindBoxContentType.Food,
                BlindBoxContentType.Water,
                BlindBoxContentType.Medicine,
                BlindBoxContentType.Job,
                BlindBoxContentType.Housing,
                BlindBoxContentType.Technology,
                BlindBoxContentType.Identity,
                BlindBoxContentType.Talent,
                BlindBoxContentType.Currency
            };

            return positiveTypes.Contains(contentType);
        }

        /// <summary>
        /// 添加状态效果
        /// </summary>
        public void AddStatusEffect(PlayerData playerData, StatusEffect effect)
        {
            // 检查是否有免疫
            if (HasStatusImmunity(playerData, effect.type))
            {
                return;
            }

            // 检查是否已存在相同类型的效果
            var existingEffect = playerData.statusEffects.FirstOrDefault(e => e.type == effect.type);
            if (existingEffect != null)
            {
                // 更新现有效果
                existingEffect.intensity = Mathf.Max(existingEffect.intensity, effect.intensity);
                existingEffect.duration = Mathf.Max(existingEffect.duration, effect.duration);
            }
            else
            {
                // 添加新效果
                playerData.AddStatusEffect(effect);
            }

            // 应用状态效果
            ApplyStatusEffect(playerData, effect);
            
            OnStatusEffectAdded?.Invoke(effect);
        }

        /// <summary>
        /// 检查状态免疫
        /// </summary>
        private bool HasStatusImmunity(PlayerData playerData, StatusEffectType effectType)
        {
            foreach (var talentType in playerData.talents)
            {
                var config = talentConfigs[talentType];
                foreach (GameEffect effect in config.effects)
                {
                    if (effect.type == GameEffectType.StatusImmunity && effect.statusEffectType == effectType)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 应用状态效果
        /// </summary>
        private void ApplyStatusEffect(PlayerData playerData, StatusEffect effect)
        {
            var config = statusEffectConfigs[effect.type];
            
            foreach (GameEffect statusEffect in config.effects)
            {
                statusEffect.Apply(playerData);
                ApplyStatusEffectModifier(playerData, statusEffect, effect.intensity);
            }
        }

        /// <summary>
        /// 应用状态效果修正
        /// </summary>
        private void ApplyStatusEffectModifier(PlayerData playerData, GameEffect modifier, float intensity)
        {
            float actualValue = modifier.value * intensity;
            modifier.Apply(playerData, intensity);

            switch (modifier.type)
            {
                case GameEffectType.AttributeChange:
                    playerData.attributes.ModifyAttribute(modifier.attributeType.Value, actualValue);
                    break;

                case GameEffectType.ResourceChange:
                    if (actualValue > 0)
                    {
                        playerData.resources.AddResource(modifier.resourceType.Value, actualValue);
                    }
                    else
                    {
                        playerData.resources.ConsumeResource(modifier.resourceType.Value, -actualValue);
                    }
                    break;

                case GameEffectType.ProbabilityModifier:
                    // 概率修正在盲盒系统中处理
                    break;
            }
        }

        /// <summary>
        /// 应用特殊状态效果
        /// </summary>
        private void ApplySpecialStatusEffect(PlayerData playerData, string effectId, float intensity)
        {
            switch (effectId)
            {
                case "memory_corruption":
                    // 记忆损坏
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, -intensity * 2f);
                    break;
                
                case "system_integration":
                    // 系统整合
                    playerData.attributes.ModifyAttribute(AttributeType.Pollution, intensity * 5f);
                    break;
                
                case "addiction_spiral":
                    // 成瘾螺旋
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, intensity * 3f);
                    break;
                
                case "cognitive_enhancement":
                    // 认知增强
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, intensity * 2f);
                    break;
            }
        }

        /// <summary>
        /// 移除状态效果
        /// </summary>
        public void RemoveStatusEffect(PlayerData playerData, StatusEffectType effectType)
        {
            playerData.RemoveStatusEffect(effectType);
            OnStatusEffectRemoved?.Invoke(effectType);
        }

        /// <summary>
        /// 更新状态效果（每日调用）
        /// </summary>
        public void UpdateStatusEffects(PlayerData playerData)
        {
            for (int i = playerData.statusEffects.Count - 1; i >= 0; i--)
            {
                var effect = playerData.statusEffects[i];
                
                // 应用持续效果
                ApplyStatusEffect(playerData, effect);
                
                // 减少持续时间
                effect.duration--;
                
                // 移除过期效果
                if (effect.duration <= 0)
                {
                    playerData.statusEffects.RemoveAt(i);
                    OnStatusEffectRemoved?.Invoke(effect.type);
                }
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType? attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 重新加载天赋数据
        /// </summary>
        public void ReloadTalentData()
        {
            TalentDataLoader.LoadAllData();
            talentConfigs = TalentDataLoader.GetTalentConfigs();
            statusEffectConfigs = TalentDataLoader.GetStatusEffectConfigs();
            Debug.Log("天赋数据已重新加载");
        }

        public void Dispose()
        {
            talentConfigs?.Clear();
            statusEffectConfigs?.Clear();
        }
    }

    /// <summary>
    /// 天赋配置
    /// </summary>
    [Serializable]
    public class TalentConfig
    {
        public string name;
        public string description;
        public TalentRarity rarity;
        public string iconPath;
        public int unlockLevel;
        public bool isStartingTalent;
        public List<GameCondition> requirements;
        public List<GameEffect> effects;

        public TalentConfig()
        {
            requirements = new List<GameCondition>();
            effects = new List<GameEffect>();
        }
    }


    /// <summary>
    /// 状态效果配置
    /// </summary>
    [Serializable]
    public class StatusEffectConfig
    {
        public string name;
        public string description;
        public bool isPositive;
        public string iconPath;
        public bool stackable;
        public int maxStacks;
        public List<GameEffect> effects;

        public StatusEffectConfig()
        {
            effects = new List<GameEffect>();
        }
    }


    /// <summary>
    /// 天赋稀有度
    /// </summary>
    public enum TalentRarity
    {
        Common,     // 普通
        Rare,       // 稀有
        Epic,       // 史诗
        Legendary   // 传说
    }

}
