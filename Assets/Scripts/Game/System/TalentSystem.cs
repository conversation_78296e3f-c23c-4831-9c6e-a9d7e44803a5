using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 天赋管理器 - 管理玩家天赋和状态效果
    /// </summary>
    public class TalentSystem : IDisposable
    {
        private Dictionary<TalentType, TalentConfig> talentConfigs;
        private Dictionary<StatusEffectType, StatusEffectConfig> statusEffectConfigs;

        public event Action<TalentType> OnTalentAcquired;
        public event Action<StatusEffect> OnStatusEffectAdded;
        public event Action<StatusEffectType> OnStatusEffectRemoved;

        public TalentSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            talentConfigs = new Dictionary<TalentType, TalentConfig>();
            statusEffectConfigs = new Dictionary<StatusEffectType, StatusEffectConfig>();
            
            InitializeTalentConfigs();
            InitializeStatusEffectConfigs();
        }

        /// <summary>
        /// 获得天赋
        /// </summary>
        public bool AcquireTalent(PlayerData playerData, TalentType talentType)
        {
            if (playerData.HasTalent(talentType))
            {
                return false; // 已经拥有该天赋
            }

            var config = talentConfigs[talentType];
            
            // 检查获得条件
            if (!CheckTalentRequirements(playerData, config))
            {
                return false;
            }

            // 添加天赋
            playerData.AddTalent(talentType);
            
            // 应用天赋效果
            ApplyTalentEffects(playerData, config);
            
            OnTalentAcquired?.Invoke(talentType);
            return true;
        }

        /// <summary>
        /// 检查天赋获得条件
        /// </summary>
        private bool CheckTalentRequirements(PlayerData playerData, TalentConfig config)
        {
            foreach (var requirement in config.requirements)
            {
                if (!CheckTalentRequirement(playerData, requirement))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 检查单个天赋条件
        /// </summary>
        private bool CheckTalentRequirement(PlayerData playerData, TalentRequirement requirement)
        {
            switch (requirement.type)
            {
                case TalentRequirementType.Attribute:
                    return GetAttributeValue(playerData, requirement.parameter) >= requirement.value;
                
                case TalentRequirementType.Resource:
                    return playerData.resources.GetResource(requirement.resourceType.Value) >= requirement.value;
                
                case TalentRequirementType.SocialClass:
                    return (int)playerData.socialClass >= (int)requirement.socialClass.Value;
                
                case TalentRequirementType.BoxesOpened:
                    return playerData.totalBoxesOpened >= requirement.value;
                
                case TalentRequirementType.DaysSurvived:
                    return playerData.daysSurvived >= requirement.value;
                
                case TalentRequirementType.SpecialEvent:
                    return playerData.unlockedEvents.Contains(requirement.parameter);
                
                default:
                    return false;
            }
        }

        /// <summary>
        /// 应用天赋效果
        /// </summary>
        private void ApplyTalentEffects(PlayerData playerData, TalentConfig config)
        {
            foreach (var effect in config.effects)
            {
                ApplyTalentEffect(playerData, effect);
            }
        }

        /// <summary>
        /// 应用单个天赋效果
        /// </summary>
        private void ApplyTalentEffect(PlayerData playerData, TalentEffect effect)
        {
            switch (effect.type)
            {
                case TalentEffectType.AttributeModifier:
                    playerData.attributes.ModifyAttribute(effect.targetAttribute, effect.value);
                    break;
                
                case TalentEffectType.ResourceBonus:
                    playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                    break;
                
                case TalentEffectType.ProbabilityModifier:
                    // 概率修正效果在盲盒系统中处理
                    break;
                
                case TalentEffectType.StatusImmunity:
                    // 状态免疫效果：设置免疫标志
                    if (effect.immunityType.HasValue)
                    {
                        string immunityFlag = $"immunity_{effect.immunityType.Value}";
                        playerData.SetSpecialFlag(immunityFlag, true);
                        Debug.Log($"授予状态免疫: {effect.immunityType.Value}");
                    }
                    break;
                
                case TalentEffectType.SpecialAbility:
                    // 特殊能力效果
                    GrantSpecialAbility(playerData, effect.abilityId);
                    break;
            }
        }

        /// <summary>
        /// 授予特殊能力
        /// </summary>
        private void GrantSpecialAbility(PlayerData playerData, string abilityId)
        {
            switch (abilityId)
            {
                case "work_efficiency_boost":
                    // 工作效率提升：工作收入增加50%
                    playerData.SetSpecialFlag("work_efficiency_boost", true);
                    Debug.Log("授予特殊能力: 工作效率提升");
                    break;

                case "genius_insight":
                    // 天才洞察：可以预知某些事件结果
                    playerData.SetSpecialFlag("genius_insight", true);
                    Debug.Log("授予特殊能力: 天才洞察");
                    break;

                case "mental_instability":
                    // 精神不稳定：随机触发特殊事件
                    playerData.SetSpecialFlag("mental_instability", true);
                    Debug.Log("授予特殊能力: 精神不稳定");
                    break;

                case "box_content_preview":
                    // 盲盒内容预览：可以看到盲盒内容
                    playerData.SetSpecialFlag("box_content_preview", true);
                    Debug.Log("授予特殊能力: 盲盒内容预览");
                    break;

                case "trap_avoidance":
                    // 陷阱规避：自动避开负面内容
                    playerData.SetSpecialFlag("trap_avoidance", true);
                    Debug.Log("授予特殊能力: 陷阱规避");
                    break;

                case "rare_event_trigger":
                    // 稀有事件触发：更容易遇到稀有事件
                    playerData.SetSpecialFlag("rare_event_trigger", true);
                    Debug.Log("授予特殊能力: 稀有事件触发");
                    break;

                default:
                    Debug.Log($"未知特殊能力: {abilityId}");
                    break;
            }
        }

        /// <summary>
        /// 获取天赋对盲盒概率的影响
        /// </summary>
        public float GetTalentProbabilityModifier(PlayerData playerData, BlindBoxContentType contentType)
        {
            float modifier = 0f;

            foreach (var talentType in playerData.talents)
            {
                var config = talentConfigs[talentType];
                foreach (var effect in config.effects)
                {
                    if (effect.type == TalentEffectType.ProbabilityModifier)
                    {
                        modifier += CalculateProbabilityModifier(effect, contentType);
                    }
                }
            }

            return modifier;
        }

        /// <summary>
        /// 计算概率修正值
        /// </summary>
        private float CalculateProbabilityModifier(TalentEffect effect, BlindBoxContentType contentType)
        {
            // 根据天赋类型和盲盒类型计算修正值
            switch (effect.abilityId)
            {
                case "gambler_gene":
                    // 赌徒基因：惊喜和惩罚概率都增加
                    return contentType == BlindBoxContentType.Empty ? effect.value : -effect.value;

                case "lucky_boost":
                    // 幸运提升：正面物品概率增加
                    return IsPositiveContent(contentType) ? effect.value : 0f;

                case "intuition":
                    // 直觉：所有概率轻微提升
                    return effect.value * 0.5f;

                case "positive_outcome":
                    // 盲盒直觉：大幅提升正面结果概率
                    return IsPositiveContent(contentType) ? effect.value : -effect.value * 0.5f;

                default:
                    return 0f;
            }
        }

        /// <summary>
        /// 判断是否为正面内容
        /// </summary>
        private bool IsPositiveContent(BlindBoxContentType contentType)
        {
            var positiveTypes = new[]
            {
                BlindBoxContentType.Food,
                BlindBoxContentType.Water,
                BlindBoxContentType.Medicine,
                BlindBoxContentType.Job,
                BlindBoxContentType.Housing,
                BlindBoxContentType.Technology,
                BlindBoxContentType.Identity,
                BlindBoxContentType.Talent,
                BlindBoxContentType.Currency
            };

            return positiveTypes.Contains(contentType);
        }

        /// <summary>
        /// 添加状态效果
        /// </summary>
        public void AddStatusEffect(PlayerData playerData, StatusEffect effect)
        {
            // 检查是否有免疫
            if (HasStatusImmunity(playerData, effect.type))
            {
                return;
            }

            // 检查是否已存在相同类型的效果
            var existingEffect = playerData.statusEffects.FirstOrDefault(e => e.type == effect.type);
            if (existingEffect != null)
            {
                // 更新现有效果
                existingEffect.intensity = Mathf.Max(existingEffect.intensity, effect.intensity);
                existingEffect.duration = Mathf.Max(existingEffect.duration, effect.duration);
            }
            else
            {
                // 添加新效果
                playerData.AddStatusEffect(effect);
            }

            // 应用状态效果
            ApplyStatusEffect(playerData, effect);
            
            OnStatusEffectAdded?.Invoke(effect);
        }

        /// <summary>
        /// 检查状态免疫
        /// </summary>
        private bool HasStatusImmunity(PlayerData playerData, StatusEffectType effectType)
        {
            foreach (var talentType in playerData.talents)
            {
                var config = talentConfigs[talentType];
                foreach (var effect in config.effects)
                {
                    if (effect.type == TalentEffectType.StatusImmunity && 
                        effect.immunityType == effectType)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 应用状态效果
        /// </summary>
        private void ApplyStatusEffect(PlayerData playerData, StatusEffect effect)
        {
            var config = statusEffectConfigs[effect.type];
            
            foreach (var statusEffect in config.effects)
            {
                ApplyStatusEffectModifier(playerData, statusEffect, effect.intensity);
            }
        }

        /// <summary>
        /// 应用状态效果修正
        /// </summary>
        private void ApplyStatusEffectModifier(PlayerData playerData, StatusEffectModifier modifier, float intensity)
        {
            float actualValue = modifier.value * intensity;

            switch (modifier.type)
            {
                case StatusModifierType.AttributeChange:
                    playerData.attributes.ModifyAttribute(modifier.targetAttribute, actualValue);
                    break;
                
                case StatusModifierType.ResourceChange:
                    if (actualValue > 0)
                    {
                        playerData.resources.AddResource(modifier.resourceType.Value, actualValue);
                    }
                    else
                    {
                        playerData.resources.ConsumeResource(modifier.resourceType.Value, -actualValue);
                    }
                    break;
                
                case StatusModifierType.ProbabilityModifier:
                    // 概率修正在盲盒系统中处理
                    break;
                
                case StatusModifierType.SpecialEffect:
                    ApplySpecialStatusEffect(playerData, modifier.effectId, intensity);
                    break;
            }
        }

        /// <summary>
        /// 应用特殊状态效果
        /// </summary>
        private void ApplySpecialStatusEffect(PlayerData playerData, string effectId, float intensity)
        {
            switch (effectId)
            {
                case "memory_corruption":
                    // 记忆损坏
                    playerData.attributes.ModifyAttribute("cognition", -intensity * 2f);
                    break;
                
                case "system_integration":
                    // 系统整合
                    playerData.attributes.ModifyAttribute("pollution", intensity * 5f);
                    break;
                
                case "addiction_spiral":
                    // 成瘾螺旋
                    playerData.attributes.ModifyAttribute("dependence", intensity * 3f);
                    break;
                
                case "cognitive_enhancement":
                    // 认知增强
                    playerData.attributes.ModifyAttribute("cognition", intensity * 2f);
                    break;
            }
        }

        /// <summary>
        /// 移除状态效果
        /// </summary>
        public void RemoveStatusEffect(PlayerData playerData, StatusEffectType effectType)
        {
            playerData.RemoveStatusEffect(effectType);
            OnStatusEffectRemoved?.Invoke(effectType);
        }

        /// <summary>
        /// 更新状态效果（每日调用）
        /// </summary>
        public void UpdateStatusEffects(PlayerData playerData)
        {
            for (int i = playerData.statusEffects.Count - 1; i >= 0; i--)
            {
                var effect = playerData.statusEffects[i];
                
                // 应用持续效果
                ApplyStatusEffect(playerData, effect);
                
                // 减少持续时间
                effect.duration--;
                
                // 移除过期效果
                if (effect.duration <= 0)
                {
                    playerData.statusEffects.RemoveAt(i);
                    OnStatusEffectRemoved?.Invoke(effect.type);
                }
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 获取属性值（字符串版本，向后兼容）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, string attributeName)
        {
            return playerData.attributes.GetAttributeValue(attributeName);
        }

        /// <summary>
        /// 初始化天赋配置
        /// </summary>
        private void InitializeTalentConfigs()
        {
            // 赌徒基因
            var gamblerGene = new TalentConfig
            {
                name = "赌徒基因",
                description = "盲盒惊喜概率提升，但惩罚概率也大幅增加",
                rarity = TalentRarity.Common,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.BoxesOpened, 20f, "开启20个盲盒")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.ProbabilityModifier, "gambler_gene", 0.1f),
                    new TalentEffect(TalentEffectType.AttributeModifier, "luck", 2f)
                }
            };
            talentConfigs[TalentType.GamblerGene] = gamblerGene;

            // 社交达人
            var socialMaster = new TalentConfig
            {
                name = "社交达人",
                description = "社交值基础提升，职业晋升成功率提高",
                rarity = TalentRarity.Common,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.Attribute, 70f, "social")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.AttributeModifier, "social", 20f),
                    new TalentEffect(TalentEffectType.SpecialAbility, "promotion_bonus")
                }
            };
            talentConfigs[TalentType.SocialMaster] = socialMaster;

            // 富二代
            var richKid = new TalentConfig
            {
                name = "富二代",
                description = "初始资金和资源较高",
                rarity = TalentRarity.Rare,
                requirements = new List<TalentRequirement>(), // 开局天赋，无条件
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.ResourceBonus, ResourceType.Credits, 1000f),
                    new TalentEffect(TalentEffectType.ResourceBonus, ResourceType.Diamond, 5f)
                }
            };
            talentConfigs[TalentType.RichKid] = richKid;

            // 黑客天才
            var hackerGenius = new TalentConfig
            {
                name = "黑客天才",
                description = "获得破解盲盒系统的特殊能力",
                rarity = TalentRarity.Epic,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.Attribute, 80f, "cognition"),
                    new TalentRequirement(TalentRequirementType.SpecialEvent, 0f, "system_glitch")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.SpecialAbility, "probability_hack"),
                    new TalentEffect(TalentEffectType.StatusImmunity, StatusEffectType.SystemTracking)
                }
            };
            talentConfigs[TalentType.HackerGenius] = hackerGenius;

            // 倒霉蛋
            var unluckyOne = new TalentConfig
            {
                name = "倒霉蛋",
                description = "初始幸运值低，但更容易触发稀有事件",
                rarity = TalentRarity.Common,
                requirements = new List<TalentRequirement>(), // 开局天赋
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.AttributeModifier, "luck", -3f),
                    new TalentEffect(TalentEffectType.SpecialAbility, "rare_event_trigger")
                }
            };
            talentConfigs[TalentType.UnluckyOne] = unluckyOne;

            // 天生劳模
            var workaholic = new TalentConfig
            {
                name = "天生劳模",
                description = "工作效率大幅提升，但精力消耗增加",
                rarity = TalentRarity.Common,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.DaysSurvived, 10f, "days_survived"),
                    new TalentRequirement(TalentRequirementType.SpecialEvent, 0f, "work_overtime")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.SpecialAbility, "work_efficiency_boost"),
                    new TalentEffect(TalentEffectType.ResourceBonus, ResourceType.Credits, 50f),
                    new TalentEffect(TalentEffectType.AttributeModifier, "energy", -10f) // 精力上限降低
                }
            };
            talentConfigs[TalentType.Workaholic] = workaholic;

            // 天才疯子
            var madGenius = new TalentConfig
            {
                name = "天才疯子",
                description = "认知值极高但精神不稳定，容易触发特殊事件",
                rarity = TalentRarity.Epic,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.Attribute, 90f, "cognition"),
                    new TalentRequirement(TalentRequirementType.Resource, 30f, "humanity")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.AttributeModifier, "cognition", 15f),
                    new TalentEffect(TalentEffectType.SpecialAbility, "genius_insight"),
                    new TalentEffect(TalentEffectType.SpecialAbility, "mental_instability"),
                    new TalentEffect(TalentEffectType.StatusImmunity, StatusEffectType.CognitiveDamage)
                }
            };
            talentConfigs[TalentType.MadGenius] = madGenius;

            // 免疫异变
            var immuneVariant = new TalentConfig
            {
                name = "免疫异变",
                description = "对系统病毒和负面状态具有强大抗性",
                rarity = TalentRarity.Rare,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.SpecialEvent, 0f, "virus_infection"),
                    new TalentRequirement(TalentRequirementType.Attribute, 60f, "humanity")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.StatusImmunity, StatusEffectType.Sickness),
                    new TalentEffect(TalentEffectType.StatusImmunity, StatusEffectType.DataCorruption),
                    new TalentEffect(TalentEffectType.StatusImmunity, StatusEffectType.CognitiveDamage),
                    new TalentEffect(TalentEffectType.AttributeModifier, "health", 20f)
                }
            };
            talentConfigs[TalentType.ImmuneVariant] = immuneVariant;

            // 盲盒直觉
            var blindBoxIntuition = new TalentConfig
            {
                name = "盲盒直觉",
                description = "能够感知盲盒内容，避开陷阱，提升好运概率",
                rarity = TalentRarity.Legendary,
                requirements = new List<TalentRequirement>
                {
                    new TalentRequirement(TalentRequirementType.BoxesOpened, 100f, "boxes_opened"),
                    new TalentRequirement(TalentRequirementType.Attribute, 75f, "luck"),
                    new TalentRequirement(TalentRequirementType.SpecialEvent, 0f, "perfect_streak")
                },
                effects = new List<TalentEffect>
                {
                    new TalentEffect(TalentEffectType.SpecialAbility, "box_content_preview"),
                    new TalentEffect(TalentEffectType.SpecialAbility, "trap_avoidance"),
                    new TalentEffect(TalentEffectType.ProbabilityModifier, "positive_outcome", 0.25f),
                    new TalentEffect(TalentEffectType.AttributeModifier, "luck", 10f)
                }
            };
            talentConfigs[TalentType.BlindBoxIntuition] = blindBoxIntuition;
        }

        /// <summary>
        /// 初始化状态效果配置
        /// </summary>
        private void InitializeStatusEffectConfigs()
        {
            // 疾病
            var sickness = new StatusEffectConfig
            {
                name = "疾病",
                description = "健康状况不佳，影响各项能力",
                isPositive = false,
                effects = new List<StatusEffectModifier>
                {
                    new StatusEffectModifier(StatusModifierType.AttributeChange, "health", -2f),
                    new StatusEffectModifier(StatusModifierType.AttributeChange, "energy", -1f)
                }
            };
            statusEffectConfigs[StatusEffectType.Sickness] = sickness;

            // 疲劳
            var fatigue = new StatusEffectConfig
            {
                name = "疲劳",
                description = "精力不足，工作效率下降",
                isPositive = false,
                effects = new List<StatusEffectModifier>
                {
                    new StatusEffectModifier(StatusModifierType.AttributeChange, "energy", -3f)
                }
            };
            statusEffectConfigs[StatusEffectType.Fatigue] = fatigue;

            // 幸运提升
            var luckyBoost = new StatusEffectConfig
            {
                name = "幸运提升",
                description = "暂时提升幸运值",
                isPositive = true,
                effects = new List<StatusEffectModifier>
                {
                    new StatusEffectModifier(StatusModifierType.AttributeChange, "luck", 2f)
                }
            };
            statusEffectConfigs[StatusEffectType.LuckyBoost] = luckyBoost;

            // 系统追踪
            var systemTracking = new StatusEffectConfig
            {
                name = "系统追踪",
                description = "被系统监控，行动受限",
                isPositive = false,
                effects = new List<StatusEffectModifier>
                {
                    new StatusEffectModifier(StatusModifierType.SpecialEffect, "tracking_penalty"),
                    new StatusEffectModifier(StatusModifierType.AttributeChange, "social", -5f)
                }
            };
            statusEffectConfigs[StatusEffectType.SystemTracking] = systemTracking;

            // TODO: 添加其他状态效果配置
        }

        public void Dispose()
        {
            talentConfigs?.Clear();
            statusEffectConfigs?.Clear();
        }
    }

    /// <summary>
    /// 天赋配置
    /// </summary>
    [Serializable]
    public class TalentConfig
    {
        public string name;
        public string description;
        public TalentRarity rarity;
        public List<TalentRequirement> requirements;
        public List<TalentEffect> effects;

        public TalentConfig()
        {
            requirements = new List<TalentRequirement>();
            effects = new List<TalentEffect>();
        }
    }

    /// <summary>
    /// 天赋条件
    /// </summary>
    [Serializable]
    public class TalentRequirement
    {
        public TalentRequirementType type;
        public float value;
        public string parameter;
        public ResourceType? resourceType;
        public SocialClass? socialClass;

        public TalentRequirement(TalentRequirementType type, float value, string parameter)
        {
            this.type = type;
            this.value = value;
            this.parameter = parameter;
        }
    }

    /// <summary>
    /// 天赋效果
    /// </summary>
    [Serializable]
    public class TalentEffect
    {
        public TalentEffectType type;
        public string targetAttribute;
        public float value;
        public ResourceType? resourceType;
        public string abilityId;
        public StatusEffectType? immunityType;

        public TalentEffect(TalentEffectType type, string target, float value)
        {
            this.type = type;
            targetAttribute = target;
            this.value = value;
        }

        public TalentEffect(TalentEffectType type, ResourceType resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }

        public TalentEffect(TalentEffectType type, string abilityId)
        {
            this.type = type;
            this.abilityId = abilityId;
        }

        public TalentEffect(TalentEffectType type, StatusEffectType immunityType)
        {
            this.type = type;
            this.immunityType = immunityType;
        }
    }

    /// <summary>
    /// 状态效果配置
    /// </summary>
    [Serializable]
    public class StatusEffectConfig
    {
        public string name;
        public string description;
        public bool isPositive;
        public List<StatusEffectModifier> effects;

        public StatusEffectConfig()
        {
            effects = new List<StatusEffectModifier>();
        }
    }

    /// <summary>
    /// 状态效果修正器
    /// </summary>
    [Serializable]
    public class StatusEffectModifier
    {
        public StatusModifierType type;
        public string targetAttribute;
        public float value;
        public ResourceType? resourceType;
        public string effectId;

        public StatusEffectModifier(StatusModifierType type, string target, float value)
        {
            this.type = type;
            targetAttribute = target;
            this.value = value;
        }

        public StatusEffectModifier(StatusModifierType type, ResourceType resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }

        public StatusEffectModifier(StatusModifierType type, string effectId)
        {
            this.type = type;
            this.effectId = effectId;
        }
    }

    /// <summary>
    /// 天赋稀有度
    /// </summary>
    public enum TalentRarity
    {
        Common,     // 普通
        Rare,       // 稀有
        Epic,       // 史诗
        Legendary   // 传说
    }

    /// <summary>
    /// 天赋条件类型
    /// </summary>
    public enum TalentRequirementType
    {
        Attribute,      // 属性条件
        Resource,       // 资源条件
        SocialClass,    // 社会阶层
        BoxesOpened,    // 开盒数量
        DaysSurvived,   // 生存天数
        SpecialEvent    // 特殊事件
    }

    /// <summary>
    /// 天赋效果类型
    /// </summary>
    public enum TalentEffectType
    {
        AttributeModifier,      // 属性修正
        ResourceBonus,          // 资源奖励
        ProbabilityModifier,    // 概率修正
        StatusImmunity,         // 状态免疫
        SpecialAbility          // 特殊能力
    }

    /// <summary>
    /// 状态修正类型
    /// </summary>
    public enum StatusModifierType
    {
        AttributeChange,        // 属性变化
        ResourceChange,         // 资源变化
        ProbabilityModifier,    // 概率修正
        SpecialEffect           // 特殊效果
    }
}
