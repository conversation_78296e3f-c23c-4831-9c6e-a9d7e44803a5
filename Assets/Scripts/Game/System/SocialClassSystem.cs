using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 社会阶层管理器 - 管理玩家的社会地位和阶层晋升
    /// </summary>
    public class SocialClassSystem : IDisposable
    {
        private Dictionary<SocialClass, SocialClassConfig> classConfigs;
        private List<PromotionAttempt> promotionHistory;

        public event Action<SocialClass, SocialClass> OnClassChanged; // 旧阶层, 新阶层
        public event Action<PromotionAttempt> OnPromotionAttempted;
        public event Action<string> OnClassPrivilegeUsed;

        public SocialClassSystem()
        {
            Initialize();
        }

        private void Initialize()
        {
            classConfigs = new Dictionary<SocialClass, SocialClassConfig>();
            promotionHistory = new List<PromotionAttempt>();
            
            InitializeClassConfigs();
        }

        /// <summary>
        /// 尝试晋升社会阶层
        /// </summary>
        public PromotionResult AttemptPromotion(PlayerData playerData, SocialClass targetClass)
        {
            var currentClass = playerData.socialClass;
            var result = new PromotionResult
            {
                fromClass = currentClass,
                toClass = targetClass,
                success = false,
                timestamp = DateTime.Now
            };

            // 检查是否可以晋升
            if (!CanPromoteToClass(playerData, targetClass))
            {
                result.failureReason = GetPromotionFailureReason(playerData, targetClass);
                RecordPromotionAttempt(playerData, result);
                return result;
            }

            // 计算晋升成功率
            float successRate = CalculatePromotionSuccessRate(playerData, targetClass);
            bool promotionSuccess = UnityEngine.Random.Range(0f, 1f) < successRate;

            if (promotionSuccess)
            {
                // 晋升成功
                result.success = true;
                ExecutePromotion(playerData, targetClass);
                
                // 应用晋升代价
                ApplyPromotionCosts(playerData, targetClass);
                
                result.successMessage = GetPromotionSuccessMessage(currentClass, targetClass);
            }
            else
            {
                // 晋升失败
                result.failureReason = "晋升评估未通过，请继续努力";
                
                // 应用失败惩罚
                ApplyPromotionFailurePenalty(playerData, targetClass);
            }

            RecordPromotionAttempt(playerData, result);
            OnPromotionAttempted?.Invoke(new PromotionAttempt(playerData, result));

            return result;
        }

        /// <summary>
        /// 检查是否满足晋升条件
        /// </summary>
        public bool CanPromoteToClass(PlayerData playerData, SocialClass targetClass)
        {
            var currentClass = playerData.socialClass;
            
            // 不能降级
            if ((int)targetClass <= (int)currentClass)
                return false;

            // 只能逐级晋升
            if ((int)targetClass > (int)currentClass + 1)
                return false;

            var targetConfig = classConfigs[targetClass];
            
            // 检查基础条件
            foreach (var requirement in targetConfig.promotionRequirements)
            {
                if (!CheckRequirement(playerData, requirement))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 检查单个晋升条件
        /// </summary>
        private bool CheckRequirement(PlayerData playerData, PromotionRequirement requirement)
        {
            switch (requirement.type)
            {
                case RequirementType.Resource:
                    return playerData.resources.GetResource(requirement.resourceType.Value) >= requirement.value;
                
                case RequirementType.Attribute:
                    return GetAttributeValue(playerData, requirement.attributeName) >= requirement.value;
                
                case RequirementType.SocialScore:
                    return playerData.resources.socialScore >= requirement.value;
                
                case RequirementType.DaysSurvived:
                    return playerData.daysSurvived >= requirement.value;
                
                case RequirementType.BoxesOpened:
                    return playerData.totalBoxesOpened >= requirement.value;
                
                case RequirementType.SpecialTask:
                    return playerData.completedAchievements.Contains(requirement.taskId);
                
                case RequirementType.NoNegativeStatus:
                    return !HasNegativeStatusEffects(playerData);
                
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, AttributeType attributeType)
        {
            return playerData.attributes.GetAttributeValue(attributeType);
        }

        /// <summary>
        /// 获取属性值（字符串版本，向后兼容）
        /// </summary>
        private float GetAttributeValue(PlayerData playerData, string attributeName)
        {
            return playerData.attributes.GetAttributeValue(attributeName);
        }

        /// <summary>
        /// 检查是否有负面状态效果
        /// </summary>
        private bool HasNegativeStatusEffects(PlayerData playerData)
        {
            var negativeEffects = new[]
            {
                StatusEffectType.Sickness,
                StatusEffectType.Fatigue,
                StatusEffectType.Depression,
                StatusEffectType.SystemTracking,
                StatusEffectType.CognitiveDamage
            };

            foreach (var effect in negativeEffects)
            {
                if (playerData.HasStatusEffect(effect))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 计算晋升成功率
        /// </summary>
        private float CalculatePromotionSuccessRate(PlayerData playerData, SocialClass targetClass)
        {
            float baseRate = 0.7f; // 基础成功率

            // 根据目标阶层调整
            switch (targetClass)
            {
                case SocialClass.Worker:
                    baseRate = 0.8f;
                    break;
                case SocialClass.Chosen:
                    baseRate = 0.3f; // 顶层晋升极其困难
                    break;
            }

            // 社交值影响
            float socialBonus = (playerData.attributes.social - 50f) * 0.01f;
            baseRate += socialBonus;

            // 幸运值影响
            float luckBonus = (playerData.attributes.luck - 5f) * 0.05f;
            baseRate += luckBonus;

            // 天赋影响
            if (playerData.HasTalent(TalentType.SocialMaster))
            {
                baseRate += 0.2f;
            }

            if (playerData.HasTalent(TalentType.RichKid))
            {
                baseRate += 0.15f;
            }

            // 负面状态影响
            if (playerData.HasStatusEffect(StatusEffectType.SystemTracking))
            {
                baseRate -= 0.3f;
            }

            if (playerData.attributes.dependence > 70f)
            {
                baseRate -= 0.2f;
            }

            return Mathf.Clamp(baseRate, 0.05f, 0.95f);
        }

        /// <summary>
        /// 执行晋升
        /// </summary>
        private void ExecutePromotion(PlayerData playerData, SocialClass newClass)
        {
            var oldClass = playerData.socialClass;
            playerData.socialClass = newClass;

            // 解锁新权限
            UnlockClassPrivileges(playerData, newClass);

            // 触发事件
            OnClassChanged?.Invoke(oldClass, newClass);
        }

        /// <summary>
        /// 应用晋升代价
        /// </summary>
        private void ApplyPromotionCosts(PlayerData playerData, SocialClass newClass)
        {
            var config = classConfigs[newClass];

            foreach (var cost in config.promotionCosts)
            {
                switch (cost.type)
                {
                    case CostType.Resource:
                        playerData.resources.ConsumeResource(cost.resourceType.Value, cost.value);
                        break;
                    
                    case CostType.Attribute:
                        playerData.attributes.ModifyAttribute(cost.attributeName, -cost.value);
                        break;
                    
                    case CostType.Memory:
                        ApplyMemoryLoss(playerData, cost.description);
                        break;
                    
                    case CostType.StatusEffect:
                        var effect = new StatusEffect(cost.statusEffect.Value, cost.value, (int)cost.duration, cost.description);
                        playerData.AddStatusEffect(effect);
                        break;
                    
                    case CostType.Humanity:
                        playerData.attributes.ModifyAttribute("humanity", -cost.value);
                        break;
                }
            }
        }

        /// <summary>
        /// 应用记忆丢失
        /// </summary>
        private void ApplyMemoryLoss(PlayerData playerData, string memoryType)
        {
            // 添加记忆丢失状态效果
            var memoryLoss = new StatusEffect(StatusEffectType.MemoryLoss, 1f, -1, $"丢失了关于{memoryType}的记忆");
            playerData.AddStatusEffect(memoryLoss);

            // 可能影响认知值
            playerData.attributes.ModifyAttribute("cognition", -5f);
        }

        /// <summary>
        /// 应用晋升失败惩罚
        /// </summary>
        private void ApplyPromotionFailurePenalty(PlayerData playerData, SocialClass targetClass)
        {
            // 消耗部分资源
            playerData.resources.ConsumeResource(ResourceType.Credits, 100f);
            playerData.resources.ConsumeResource(ResourceType.SocialScore, 10f);

            // 降低社交值
            playerData.attributes.ModifyAttribute("social", -5f);

            // 根据目标阶层调整惩罚力度
            float penaltyMultiplier = targetClass == SocialClass.Chosen ? 2f : 1f;
            playerData.attributes.ModifyAttribute("social", -5f * penaltyMultiplier);

            // 可能被系统关注
            if (UnityEngine.Random.Range(0f, 1f) < 0.3f)
            {
                var tracking = new StatusEffect(StatusEffectType.SystemTracking, 1f, 7, "因晋升失败被系统关注");
                playerData.AddStatusEffect(tracking);
            }
        }

        /// <summary>
        /// 解锁阶层权限
        /// </summary>
        private void UnlockClassPrivileges(PlayerData playerData, SocialClass newClass)
        {
            var config = classConfigs[newClass];

            foreach (var privilege in config.privileges)
            {
                // 这里可以添加具体的权限解锁逻辑
                Debug.Log($"玩家 {playerData.playerName} 解锁权限: {privilege}");

                // 可以在这里添加权限到玩家数据中
                if (!playerData.unlockedEvents.Contains($"privilege_{privilege}"))
                {
                    playerData.unlockedEvents.Add($"privilege_{privilege}");
                }
            }
        }

        /// <summary>
        /// 使用阶层特权
        /// </summary>
        public bool UseClassPrivilege(PlayerData playerData, string privilegeId)
        {
            var config = classConfigs[playerData.socialClass];
            
            if (!config.privileges.Contains(privilegeId))
            {
                return false;
            }

            // 执行特权效果
            bool success = ExecutePrivilege(playerData, privilegeId);
            
            if (success)
            {
                OnClassPrivilegeUsed?.Invoke(privilegeId);
            }

            return success;
        }

        /// <summary>
        /// 执行特权效果
        /// </summary>
        private bool ExecutePrivilege(PlayerData playerData, string privilegeId)
        {
            switch (privilegeId)
            {
                case "skip_queue":
                    // 跳过排队等待
                    return true;
                
                case "probability_hint":
                    // 获得概率提示
                    return true;
                
                case "resource_bonus":
                    // 资源奖励
                    playerData.resources.AddResource(ResourceType.Credits, 200f);
                    return true;
                
                case "probability_edit":
                    // 概率编辑权（有限制）
                    return true;
                
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取晋升失败原因
        /// </summary>
        private string GetPromotionFailureReason(PlayerData playerData, SocialClass targetClass)
        {
            var config = classConfigs[targetClass];
            
            foreach (var requirement in config.promotionRequirements)
            {
                if (!CheckRequirement(playerData, requirement))
                {
                    return $"不满足条件: {requirement.description}";
                }
            }

            return "未知原因";
        }

        /// <summary>
        /// 获取晋升成功消息
        /// </summary>
        private string GetPromotionSuccessMessage(SocialClass fromClass, SocialClass toClass)
        {
            return $"恭喜！您已从{GetClassDisplayName(fromClass)}晋升为{GetClassDisplayName(toClass)}";
        }

        /// <summary>
        /// 获取阶层显示名称
        /// </summary>
        private string GetClassDisplayName(SocialClass socialClass)
        {
            switch (socialClass)
            {
                case SocialClass.Parasite: return "蛀虫";
                case SocialClass.Worker: return "工蜂";
                case SocialClass.Chosen: return "神选者";
                default: return "未知";
            }
        }

        /// <summary>
        /// 记录晋升尝试
        /// </summary>
        private void RecordPromotionAttempt(PlayerData playerData, PromotionResult result)
        {
            var attempt = new PromotionAttempt(playerData, result);
            promotionHistory.Add(attempt);

            // 限制历史记录数量
            if (promotionHistory.Count > 100)
            {
                promotionHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 初始化阶层配置
        /// </summary>
        private void InitializeClassConfigs()
        {
            // 蛀虫阶层配置
            var parasiteConfig = new SocialClassConfig
            {
                className = "蛀虫",
                description = "社会底层，依赖救济生存",
                dailyCredits = 50f,
                privileges = new List<string>(),
                promotionRequirements = new List<PromotionRequirement>(),
                promotionCosts = new List<PromotionCost>()
            };
            classConfigs[SocialClass.Parasite] = parasiteConfig;

            // 工蜂阶层配置
            var workerConfig = new SocialClassConfig
            {
                className = "工蜂",
                description = "中层劳动者，有稳定工作",
                dailyCredits = 150f,
                privileges = new List<string> { "skip_queue", "probability_hint" },
                promotionRequirements = new List<PromotionRequirement>
                {
                    new PromotionRequirement(RequirementType.SocialScore, 100f, "社会积分达到100"),
                    new PromotionRequirement(RequirementType.DaysSurvived, 30, "生存30天"),
                    new PromotionRequirement(RequirementType.Attribute, 60f, "social", "社交值达到60")
                },
                promotionCosts = new List<PromotionCost>
                {
                    new PromotionCost(CostType.Resource, ResourceType.Credits, 500f, "晋升费用"),
                    new PromotionCost(CostType.Memory, "童年记忆", 0f, "部分童年记忆被抹除")
                }
            };
            classConfigs[SocialClass.Worker] = workerConfig;

            // 神选者阶层配置
            var chosenConfig = new SocialClassConfig
            {
                className = "神选者",
                description = "顶层精英，拥有特殊权限",
                dailyCredits = 500f,
                privileges = new List<string> { "skip_queue", "probability_hint", "resource_bonus", "probability_edit" },
                promotionRequirements = new List<PromotionRequirement>
                {
                    new PromotionRequirement(RequirementType.Resource, ResourceType.Diamond, 10f, "拥有10钻石"),
                    new PromotionRequirement(RequirementType.SocialScore, 500f, "社会积分达到500"),
                    new PromotionRequirement(RequirementType.SpecialTask, "loyalty_proof", "提交忠诚证明"),
                    new PromotionRequirement(RequirementType.NoNegativeStatus, 0f, "无负面状态")
                },
                promotionCosts = new List<PromotionCost>
                {
                    new PromotionCost(CostType.Humanity, "humanity", 20f, "失去部分人性"),
                    new PromotionCost(CostType.StatusEffect, StatusEffectType.SystemTracking, 1f, -1, "永久系统监控")
                }
            };
            classConfigs[SocialClass.Chosen] = chosenConfig;
        }

        public void Dispose()
        {
            classConfigs?.Clear();
            promotionHistory?.Clear();
        }
    }

    /// <summary>
    /// 社会阶层配置
    /// </summary>
    [Serializable]
    public class SocialClassConfig
    {
        public string className;
        public string description;
        public float dailyCredits;
        public List<string> privileges;
        public List<PromotionRequirement> promotionRequirements;
        public List<PromotionCost> promotionCosts;

        public SocialClassConfig()
        {
            privileges = new List<string>();
            promotionRequirements = new List<PromotionRequirement>();
            promotionCosts = new List<PromotionCost>();
        }
    }

    /// <summary>
    /// 晋升条件
    /// </summary>
    [Serializable]
    public class PromotionRequirement
    {
        public RequirementType type;
        public float value;
        public string description;
        public ResourceType? resourceType;
        public string attributeName;
        public string taskId;

        public PromotionRequirement(RequirementType type, float value, string description)
        {
            this.type = type;
            this.value = value;
            this.description = description;
        }

        public PromotionRequirement(RequirementType type, float value, string attributeName, string description)
        {
            this.type = type;
            this.value = value;
            this.attributeName = attributeName;
            this.description = description;
        }

        public PromotionRequirement(RequirementType type, ResourceType resourceType, float value, string description)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
            this.description = description;
        }

        public PromotionRequirement(RequirementType type, string taskId, string description)
        {
            this.type = type;
            this.taskId = taskId;
            this.description = description;
            this.value = 0f;
        }
    }

    /// <summary>
    /// 晋升代价
    /// </summary>
    [Serializable]
    public class PromotionCost
    {
        public CostType type;
        public float value;
        public string description;
        public ResourceType? resourceType;
        public string attributeName;
        public StatusEffectType? statusEffect;
        public float duration;

        public PromotionCost(CostType type, float value, string description)
        {
            this.type = type;
            this.value = value;
            this.description = description;
        }

        public PromotionCost(CostType type, ResourceType resourceType, float value, string description)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
            this.description = description;
        }

        public PromotionCost(CostType type, StatusEffectType statusEffect, float value, float duration, string description)
        {
            this.type = type;
            this.statusEffect = statusEffect;
            this.value = value;
            this.duration = duration;
            this.description = description;
        }

        public PromotionCost(CostType type, string attributeName, float value, string description)
        {
            this.type = type;
            this.attributeName = attributeName;
            this.value = value;
            this.description = description;
        }
    }

    /// <summary>
    /// 晋升结果
    /// </summary>
    [Serializable]
    public class PromotionResult
    {
        public SocialClass fromClass;
        public SocialClass toClass;
        public bool success;
        public string successMessage;
        public string failureReason;
        public DateTime timestamp;
    }

    /// <summary>
    /// 晋升尝试记录
    /// </summary>
    [Serializable]
    public class PromotionAttempt
    {
        public string playerId;
        public PromotionResult result;
        public string playerStateSnapshot;
        public DateTime timestamp;

        public PromotionAttempt(PlayerData playerData, PromotionResult result)
        {
            this.playerId = playerData.playerName;
            this.result = result;
            this.playerStateSnapshot = JsonUtility.ToJson(playerData);
            this.timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 条件类型
    /// </summary>
    public enum RequirementType
    {
        Resource,           // 资源条件
        Attribute,          // 属性条件
        SocialScore,        // 社会积分
        DaysSurvived,       // 生存天数
        BoxesOpened,        // 开盒数量
        SpecialTask,        // 特殊任务
        NoNegativeStatus    // 无负面状态
    }

    /// <summary>
    /// 代价类型
    /// </summary>
    public enum CostType
    {
        Resource,       // 资源代价
        Attribute,      // 属性代价
        Memory,         // 记忆代价
        StatusEffect,   // 状态效果
        Humanity        // 人性代价
    }
}
