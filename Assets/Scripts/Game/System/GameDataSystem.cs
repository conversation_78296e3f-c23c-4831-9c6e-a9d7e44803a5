using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System.Linq;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 游戏数据管理系统 - 支持CSV和JSON配置
    /// </summary>
    public class GameDataSystem : MonoBehaviour
    {
        public static GameDataSystem Instance { get; private set; }

        [Header("ScriptableObject配置")]
        public BlindBoxDataConfig blindBoxDataConfig;
        public AchievementConfigSO achievementConfig;

        [Header("JSON配置文件")]
        public TextAsset gameBalanceJson;
        public TextAsset metaGameConfigJson;

        [Header("调试选项")]
        public bool enableDataValidation = true;
        public bool logDataLoading = true;

        // 运行时数据
        private GameBalanceData gameBalance;
        private MetaGameConfigData metaGameConfig;
        private Dictionary<BlindBoxType, BlindBoxConfig> blindBoxConfigs;
        private Dictionary<string, BlindBoxItem> itemDatabase;
        private Dictionary<string, Achievement> achievements;
        private Dictionary<string, ProgressTracker> progressTrackers;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                LoadAllData();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 加载所有游戏数据
        /// </summary>
        private void LoadAllData()
        {
            if (logDataLoading)
                Debug.Log("[GameDataSystem] 开始加载游戏数据...");

            try
            {
                // 加载JSON配置
                LoadJsonConfigs();

                // 加载ScriptableObject数据
                LoadScriptableObjectData();

                // 加载成就数据
                LoadAchievementData();

                if (logDataLoading)
                    Debug.Log("[GameDataSystem] 游戏数据加载完成");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[GameDataSystem] 数据加载失败: {e.Message}");
            }
        }

        /// <summary>
        /// 加载JSON配置文件
        /// </summary>
        private void LoadJsonConfigs()
        {
            if (gameBalanceJson != null)
            {
                try
                {
                    gameBalance = JsonUtility.FromJson<GameBalanceData>(gameBalanceJson.text);
                    if (logDataLoading)
                        Debug.Log("[GameDataSystem] 加载游戏平衡配置成功");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[GameDataSystem] 加载游戏平衡配置失败: {e.Message}");
                }
            }

            if (metaGameConfigJson != null)
            {
                try
                {
                    metaGameConfig = JsonUtility.FromJson<MetaGameConfigData>(metaGameConfigJson.text);
                    if (logDataLoading)
                        Debug.Log("[GameDataSystem] 加载元游戏配置成功");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[GameDataSystem] 加载元游戏配置失败: {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载ScriptableObject数据
        /// </summary>
        private void LoadScriptableObjectData()
        {
            if (blindBoxDataConfig != null)
            {
                // 加载盲盒配置
                blindBoxConfigs = blindBoxDataConfig.GetBoxConfigDictionary();

                // 加载物品数据库
                itemDatabase = blindBoxDataConfig.GetItemDictionary();

                if (logDataLoading)
                {
                    Debug.Log($"[GameDataSystem] 加载盲盒配置: {blindBoxConfigs.Count} 个盲盒");
                    Debug.Log($"[GameDataSystem] 加载物品数据库: {itemDatabase.Count} 个物品");
                }
            }
            else
            {
                Debug.LogWarning("[GameDataSystem] BlindBoxDataConfig未配置，使用默认数据");
                LoadDefaultData();
            }
        }

        /// <summary>
        /// 加载默认数据（当ScriptableObject未配置时）
        /// </summary>
        private void LoadDefaultData()
        {
            // 使用现有的静态数据库
            var boxConfigList = BlindBoxConfigDatabase.GetAllBoxConfigs();
            blindBoxConfigs = new Dictionary<BlindBoxType, BlindBoxConfig>();
            foreach (var config in boxConfigList)
            {
                blindBoxConfigs[config.type] = config.ToBlindBoxConfig();
            }

            var itemList = BlindBoxItemDatabase.GetAllItems();
            itemDatabase = new Dictionary<string, BlindBoxItem>();
            foreach (var item in itemList)
            {
                itemDatabase[item.id] = item.ToBlindBoxItem();
            }

            if (logDataLoading)
            {
                Debug.Log($"[GameDataSystem] 使用默认数据: {blindBoxConfigs.Count} 个盲盒, {itemDatabase.Count} 个物品");
            }
        }

        /// <summary>
        /// 加载成就数据
        /// </summary>
        private void LoadAchievementData()
        {
            if (achievementConfig != null)
            {
                // 加载成就配置
                achievements = achievementConfig.GetAchievementDictionary();

                // 加载进度追踪器
                progressTrackers = achievementConfig.GetProgressTrackerDictionary();

                if (logDataLoading)
                {
                    Debug.Log($"[GameDataSystem] 加载成就配置: {achievements.Count} 个成就");
                    Debug.Log($"[GameDataSystem] 加载进度追踪器: {progressTrackers.Count} 个追踪器");
                }
            }
            else
            {
                Debug.LogWarning("[GameDataSystem] AchievementConfig未配置，使用空数据");
                achievements = new Dictionary<string, Achievement>();
                progressTrackers = new Dictionary<string, ProgressTracker>();
            }
        }

        // ===== 公共访问接口 =====

        /// <summary>
        /// 获取游戏平衡配置
        /// </summary>
        public GameBalanceData GetGameBalance()
        {
            return gameBalance;
        }

        /// <summary>
        /// 获取元游戏配置
        /// </summary>
        public MetaGameConfigData GetMetaGameConfig()
        {
            return metaGameConfig;
        }

        /// <summary>
        /// 获取盲盒配置
        /// </summary>
        public BlindBoxConfig GetBlindBoxConfig(BlindBoxType boxType)
        {
            return blindBoxConfigs?.TryGetValue(boxType, out var config) == true ? config : null;
        }

        /// <summary>
        /// 获取所有盲盒配置
        /// </summary>
        public Dictionary<BlindBoxType, BlindBoxConfig> GetAllBlindBoxConfigs()
        {
            return blindBoxConfigs ?? new Dictionary<BlindBoxType, BlindBoxConfig>();
        }

        /// <summary>
        /// 获取物品数据
        /// </summary>
        public BlindBoxItem GetItemData(string itemId)
        {
            return itemDatabase?.TryGetValue(itemId, out var item) == true ? item : null;
        }

        /// <summary>
        /// 获取所有物品数据
        /// </summary>
        public Dictionary<string, BlindBoxItem> GetAllItems()
        {
            return itemDatabase ?? new Dictionary<string, BlindBoxItem>();
        }

        /// <summary>
        /// 获取指定类型的物品
        /// </summary>
        public List<BlindBoxItem> GetItemsByType(BlindBoxContentType contentType)
        {
            if (itemDatabase == null) return new List<BlindBoxItem>();

            return itemDatabase.Values.Where(item => item.contentType == contentType).ToList();
        }

        /// <summary>
        /// 获取成就数据
        /// </summary>
        public Achievement GetAchievement(string achievementId)
        {
            return achievements?.TryGetValue(achievementId, out var achievement) == true ? achievement : null;
        }

        /// <summary>
        /// 获取所有成就数据
        /// </summary>
        public Dictionary<string, Achievement> GetAllAchievements()
        {
            return achievements ?? new Dictionary<string, Achievement>();
        }

        /// <summary>
        /// 获取指定类别的成就
        /// </summary>
        public List<Achievement> GetAchievementsByCategory(AchievementCategory category)
        {
            if (achievements == null) return new List<Achievement>();

            return achievements.Values.Where(achievement => achievement.category == category).ToList();
        }

        /// <summary>
        /// 获取进度追踪器
        /// </summary>
        public ProgressTracker GetProgressTracker(string trackerId)
        {
            return progressTrackers?.TryGetValue(trackerId, out var tracker) == true ? tracker : null;
        }

        /// <summary>
        /// 获取所有进度追踪器
        /// </summary>
        public Dictionary<string, ProgressTracker> GetAllProgressTrackers()
        {
            return progressTrackers ?? new Dictionary<string, ProgressTracker>();
        }

        /// <summary>
        /// 重新加载数据（用于热更新）
        /// </summary>
        public void ReloadData()
        {
            LoadAllData();
        }

        /// <summary>
        /// 检查数据是否已加载
        /// </summary>
        public bool IsDataLoaded()
        {
            return (gameBalance != null || metaGameConfig != null) &&
                   (blindBoxConfigs != null && itemDatabase != null) &&
                   (achievements != null && progressTrackers != null);
        }

#if UNITY_EDITOR
        /// <summary>
        /// 编辑器专用：强制重新加载数据
        /// </summary>
        [ContextMenu("重新加载数据")]
        public void EditorReloadData()
        {
            LoadAllData();
        }
#endif
    }
}
