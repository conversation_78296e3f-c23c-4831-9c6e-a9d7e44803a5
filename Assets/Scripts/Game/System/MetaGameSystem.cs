using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 元游戏管理器 - 管理跨游戏局的进度和觉醒系统
    /// </summary>
    public class MetaGameSystem : MonoBehaviour
    {
        public static MetaGameSystem Instance { get; private set; }

        [Header("配置")]
        public Dictionary<string,MetaFeature> featureConfigs;
        public List<TruthFragmentConfig> truthConfigs = new List<TruthFragmentConfig>();

        [Header("调试")]
        public bool enableDebugMode = false;
        public bool resetProgressOnStart = false;

        private MetaGameProgress currentProgress;
        private string saveFilePath;
        private DateTime gameStartTime;

        // 事件
        public event Action<string> OnFeatureUnlocked;
        public event Action<string> OnTruthDiscovered;
        public event Action<MetaGamePhase> OnPhaseChanged;
        public event Action<float> OnAwakeningLevelChanged;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                Initialize();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Initialize()
        {
            saveFilePath = Path.Combine(Application.persistentDataPath, "metagame_progress.json");

            LoadFromDatabase();
            if (resetProgressOnStart)
            {
                ResetProgress();
            }
            else
            {
                LoadProgress();
            }
            
            gameStartTime = DateTime.Now;
        }

        /// <summary>
        /// 从数据库加载配置
        /// </summary>
        private void LoadFromDatabase()
        {
            try
            {

                MetaGameDatabase.LoadAllData();

                featureConfigs = MetaGameDatabase.GetFeatureCongfigs();

            }
            catch (Exception e)
            {
                Debug.LogWarning($"[MetaGameSystem] CSV数据加载失败: {e.Message}，使用默认配置");
            }
        }

        /// <summary>
        /// 开始新游戏
        /// </summary>
        public void StartNewGame()
        {
            currentProgress.totalGamesPlayed++;
            currentProgress.lastPlayTime = DateTime.Now;
            
            if (currentProgress.totalGamesPlayed == 1)
            {
                currentProgress.firstPlayTime = DateTime.Now;
            }

            gameStartTime = DateTime.Now;
            
            Debug.Log($"[MetaGame] 开始第 {currentProgress.totalGamesPlayed} 次游戏");
            SaveProgress();
        }

        /// <summary>
        /// 结束游戏
        /// </summary>
        public void EndGame(GameEndType endingType, PlayerData playerData)
        {
            // 更新游戏时间
            var playTime = (DateTime.Now - gameStartTime).TotalHours;
            currentProgress.totalPlayTimeHours += (float)playTime;

            // 更新统计数据
            currentProgress.totalLifetimeBoxes += playerData.totalBoxesOpened;
            currentProgress.maxDaysSurvived = Mathf.Max(currentProgress.maxDaysSurvived, playerData.daysSurvived);

            if (endingType == GameEndType.Death)
            {
                currentProgress.totalDeaths++;
            }

            // 记录结局
            currentProgress.RecordEnding(endingType, playerData);

            // 计算觉醒值增长
            float awakeningGain = CalculateAwakeningGain(endingType, playerData);
            AddAwakeningLevel(awakeningGain);

            // 检查解锁
            CheckFeatureUnlocks(playerData);
            CheckTruthDiscoveries(endingType, playerData);

            SaveProgress();
            
            Debug.Log($"[MetaGame] 游戏结束: {endingType}, 觉醒值增长: {awakeningGain:F1}");
        }

        /// <summary>
        /// 计算觉醒值增长
        /// </summary>
        private float CalculateAwakeningGain(GameEndType endingType, PlayerData finalPlayerData)
        {
            float baseGain = 0f;

            // 根据结局类型
            switch (endingType)
            {
                case GameEndType.Death:
                    baseGain = 2f;
                    break;
                case GameEndType.SystemAssimilation:
                    baseGain = 5f;
                    break;
                case GameEndType.DataCorruption:
                    baseGain = 8f;
                    break;
                case GameEndType.Rebellion:
                    baseGain = 12f;
                    break;
                case GameEndType.Transcendence:
                    baseGain = 15f;
                    break;
                case GameEndType.Sacrifice:
                    baseGain = 20f;
                    break;
                case GameEndType.TrumanShow:
                    baseGain = 25f;
                    break;
                case GameEndType.UltimateSupport:
                    baseGain = 30f;
                    break;
            }


            // 根据游戏表现
            float performanceMultiplier = 1f;
            performanceMultiplier += finalPlayerData.daysSurvived * 0.1f;
            performanceMultiplier += finalPlayerData.totalBoxesOpened * 0.05f;

            // 根据当前觉醒程度调整（越高越难获得）
            float difficultyMultiplier = Mathf.Max(0.1f, 1f - (currentProgress.awakeningLevel / 200f));

            return baseGain * performanceMultiplier * difficultyMultiplier;
        }

        /// <summary>
        /// 增加觉醒值
        /// </summary>
        public void AddAwakeningLevel(float amount)
        {
            var oldPhase = currentProgress.GetCurrentPhase();
            currentProgress.awakeningLevel = Mathf.Clamp(currentProgress.awakeningLevel + amount, 0f, 100f);
            var newPhase = currentProgress.GetCurrentPhase();

            OnAwakeningLevelChanged?.Invoke(currentProgress.awakeningLevel);

            if (oldPhase != newPhase)
            {
                OnPhaseChanged?.Invoke(newPhase);
                Debug.Log($"[MetaGame] 阶段变化: {oldPhase} -> {newPhase}");
            }
        }

        /// <summary>
        /// 检测到操控
        /// </summary>
        public void DetectManipulation(string manipulationType, float severity)
        {
            currentProgress.manipulationDetections++;
            
            // 增加欺诈认知程度
            float awarenessGain = severity * 2f;
            currentProgress.fraudAwarenessLevel = Mathf.Clamp(
                currentProgress.fraudAwarenessLevel + awarenessGain, 0f, 100f);

            // 可能触发真相发现
            if (currentProgress.fraudAwarenessLevel > 30f && !currentProgress.HasFeature("manipulation_detection"))
            {
                DiscoverTruth("system_manipulation_exists");
            }

            Debug.Log($"[MetaGame] 检测到操控: {manipulationType}, 严重程度: {severity}");
        }

        /// <summary>
        /// 发现真相
        /// </summary>
        public void DiscoverTruth(string truthId)
        {
            if (currentProgress.discoveredTruths.Contains(truthId))
                return;

            var truthConfig = truthConfigs.Find(t => t.truthId == truthId);
            if (truthConfig != null)
            {
                currentProgress.DiscoverTruth(truthId);
                AddAwakeningLevel(truthConfig.awakeningValue);
                
                OnTruthDiscovered?.Invoke(truthId);
                
                // 显示真相发现UI
                if (UIManager.Instance != null)
                {
                    UIManager.Instance.ShowTruthDiscovery(truthConfig.title, truthConfig.content);
                }
            }
        }

        /// <summary>
        /// 检查功能解锁
        /// </summary>
        private void CheckFeatureUnlocks(PlayerData playerData)
        {
            foreach (var config in featureConfigs)
            {
                if (currentProgress.HasFeature(config.Key))
                    continue;

                bool canUnlock = true;

                foreach (var condition in config.Value.unlockConditions)
                {
                    if (!condition.Check(playerData))
                    {
                        canUnlock = false;
                        break;
                    }
                }

                if (canUnlock)
                {
                    UnlockFeature(config.Key);
                }
            }
        }

        /// <summary>
        /// 解锁功能
        /// </summary>
        private void UnlockFeature(string featureId)
        {
            currentProgress.UnlockFeature(featureId);
            OnFeatureUnlocked?.Invoke(featureId);

            if (UIManager.Instance != null)
            {
                UIManager.Instance.ShowFeatureUnlock(featureId);
            }
        }

        /// <summary>
        /// 检查真相发现
        /// </summary>
        private void CheckTruthDiscoveries(GameEndType endingType, PlayerData finalPlayerData)
        {
            // 根据结局类型触发特定真相
            switch (endingType)
            {
                case GameEndType.SystemAssimilation:
                    DiscoverTruth("system_assimilation_truth");
                    break;
                case GameEndType.Rebellion:
                    DiscoverTruth("rebellion_consequences");
                    break;
                case GameEndType.Transcendence:
                    DiscoverTruth("transcendence_possibility");
                    break;
            }

            // 根据游戏表现触发真相
            if (finalPlayerData.totalBoxesOpened > 100)
            {
                DiscoverTruth("addiction_mechanism");
            }

            if (currentProgress.manipulationDetections > 10)
            {
                DiscoverTruth("systematic_fraud");
            }
        }

        /// <summary>
        /// 获取当前进度
        /// </summary>
        public MetaGameProgress GetProgress()
        {
            return currentProgress;
        }

        /// <summary>
        /// 保存进度
        /// </summary>
        public void SaveProgress()
        {
            try
            {
                string json = JsonUtility.ToJson(currentProgress, true);
                File.WriteAllText(saveFilePath, json);
                
                if (enableDebugMode)
                {
                    Debug.Log($"[MetaGame] 进度已保存: {saveFilePath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[MetaGame] 保存进度失败: {e.Message}");
            }
        }

        /// <summary>
        /// 加载进度
        /// </summary>
        private void LoadProgress()
        {
            try
            {
                if (File.Exists(saveFilePath))
                {
                    string json = File.ReadAllText(saveFilePath);
                    currentProgress = JsonUtility.FromJson<MetaGameProgress>(json);
                    Debug.Log($"[MetaGame] 进度已加载，游戏次数: {currentProgress.totalGamesPlayed}");
                }
                else
                {
                    currentProgress = new MetaGameProgress();
                    Debug.Log("[MetaGame] 创建新的进度记录");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[MetaGame] 加载进度失败: {e.Message}");
                currentProgress = new MetaGameProgress();
            }
        }

        /// <summary>
        /// 重置进度
        /// </summary>
        public void ResetProgress()
        {
            currentProgress = new MetaGameProgress();
            SaveProgress();
            Debug.Log("[MetaGame] 进度已重置");
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                SaveProgress();
            }
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                SaveProgress();
            }
        }


        private void OnDestroy()
        {
            SaveProgress();
        }
    }
}
