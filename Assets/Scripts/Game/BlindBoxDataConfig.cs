using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒数据配置 - 所有盲盒相关的数据定义
    /// 设计为可从外部文件或数据库加载的结构
    /// </summary>
    [CreateAssetMenu(fileName = "BlindBoxDataConfig", menuName = "BoxOfFate/BlindBox Data Config")]
    public class BlindBoxDataConfig : ScriptableObject
    {
        [Header("盲盒配置")]
        public List<BlindBoxConfigData> boxConfigs = new List<BlindBoxConfigData>();
        
        [Header("物品配置")]
        public List<BlindBoxItemData> itemConfigs = new List<BlindBoxItemData>();
        
        [Header("欺诈算法配置")]
        public FraudAlgorithmConfigData fraudConfig = new FraudAlgorithmConfigData();
        
        [Header("全局设置")]
        public BlindBoxGlobalSettings globalSettings = new BlindBoxGlobalSettings();

        /// <summary>
        /// 获取盲盒配置字典
        /// </summary>
        public Dictionary<BlindBoxType, BlindBoxConfig> GetBoxConfigDictionary()
        {
            var dict = new Dictionary<BlindBoxType, BlindBoxConfig>();
            foreach (var config in boxConfigs)
            {
                dict[config.type] = config.ToBlindBoxConfig();
            }
            return dict;
        }

        /// <summary>
        /// 获取物品数据库字典
        /// </summary>
        public Dictionary<string, BlindBoxItem> GetItemDictionary()
        {
            var dict = new Dictionary<string, BlindBoxItem>();
            foreach (var item in itemConfigs)
            {
                dict[item.id] = item.ToBlindBoxItem();
            }
            return dict;
        }

        /// <summary>
        /// 获取欺诈算法配置
        /// </summary>
        public FraudAlgorithmConfig GetFraudConfig()
        {
            return fraudConfig.ToFraudAlgorithmConfig();
        }
    }

    /// <summary>
    /// 盲盒配置数据（可序列化）
    /// </summary>
    [Serializable]
    public class BlindBoxConfigData
    {
        public BlindBoxType type;
        public string name;
        public string description;
        public float basePrice;
        public ResourceType priceType;
        public List<BlindBoxLootTableData> lootTables = new List<BlindBoxLootTableData>();
        public List<string> unlockConditions = new List<string>();
        public int requiredSocialClass = 0; // 0=Parasite, 1=Worker, 2=Chosen
        public bool isLimited = false;
        public int dailyLimit = -1; // -1 = unlimited

        public BlindBoxConfig ToBlindBoxConfig()
        {
            var config = new BlindBoxConfig
            {
                type = type,
                name = name,
                description = description,
                basePrice = basePrice,
                priceType = priceType
            };

            foreach (var loot in lootTables)
            {
                config.lootTables.Add(loot.ToBlindBoxLootTable());
            }

            return config;
        }
    }

    /// <summary>
    /// 盲盒掉落表数据（可序列化）
    /// </summary>
    [Serializable]
    public class BlindBoxLootTableData
    {
        public string itemId;
        public float baseWeight;
        public float displayProbability;
        public float realProbability;
        public List<string> conditions = new List<string>();

        public BlindBoxLootTable ToBlindBoxLootTable()
        {
            var loot = new BlindBoxLootTable
            {
                itemId = itemId,
                baseWeight = baseWeight,
                displayProbability = displayProbability,
                realProbability = realProbability
            };

            // Convert conditions if needed
            foreach (var condition in conditions)
            {
                // Parse condition string and create LootCondition objects
                // Format: "type:parameter:value" e.g., "attribute:luck:>5"
                var parts = condition.Split(':');
                if (parts.Length >= 3)
                {
                    // Add condition parsing logic here
                }
            }

            return loot;
        }
    }

    /// <summary>
    /// 盲盒物品数据（可序列化）
    /// </summary>
    [Serializable]
    public class BlindBoxItemData
    {
        public string id;
        public string name;
        public string description;
        public BlindBoxContentType contentType;
        public float value;
        public bool isConsumable = true;
        public List<GameEffect> effects = new List<GameEffect>();
        public string iconPath;
        public ItemRarity rarity = ItemRarity.Common;

        public BlindBoxItem ToBlindBoxItem()
        {
            var item = new BlindBoxItem(id, name, contentType, value, isConsumable)
            {
                description = description
            };

            foreach (GameEffect effect in effects)
            {
                item.effects.Add(effect);
            }

            return item;
        }
    }

    /// <summary>
    /// 欺诈算法配置数据（可序列化）
    /// </summary>
    [Serializable]
    public class FraudAlgorithmConfigData
    {
        [Header("基础参数")]
        public float baseFraudRate = 0.3f;
        public float maxProbabilityDeviation = 0.5f;
        
        [Header("玩家状态影响")]
        public float dependenceInfluence = 0.2f;
        public float luckInfluence = 0.1f;
        public float socialClassInfluence = 0.3f;
        
        [Header("心理控制参数")]
        public float addictionThreshold = 60f;
        public float frustrationThreshold = 70f;
        public float hopeManipulationRate = 0.4f;
        
        [Header("系统平衡")]
        public float resourcePoolInfluence = 0.25f;
        public float globalBalanceWeight = 0.15f;

        public FraudAlgorithmConfig ToFraudAlgorithmConfig()
        {
            return new FraudAlgorithmConfig
            {
                baseFraudRate = baseFraudRate,
                maxProbabilityDeviation = maxProbabilityDeviation,
                dependenceInfluence = dependenceInfluence,
                luckInfluence = luckInfluence,
                socialClassInfluence = socialClassInfluence,
                addictionThreshold = addictionThreshold,
                frustrationThreshold = frustrationThreshold,
                hopeManipulationRate = hopeManipulationRate,
                resourcePoolInfluence = resourcePoolInfluence,
                globalBalanceWeight = globalBalanceWeight
            };
        }
    }

    /// <summary>
    /// 盲盒全局设置
    /// </summary>
    [Serializable]
    public class BlindBoxGlobalSettings
    {
        [Header("历史记录")]
        public int maxHistoryRecords = 1000;
        public bool enableDetailedLogging = true;
        
        [Header("概率显示")]
        public bool showRealProbabilities = false; // 开发模式
        public bool enableProbabilityHints = true;
        
        [Header("动画设置")]
        public float openAnimationDuration = 2f;
        public bool enableParticleEffects = true;
        
        [Header("音效设置")]
        public bool enableSoundEffects = true;
        public float soundVolume = 1f;
    }

    /// <summary>
    /// 物品稀有度
    /// </summary>
    public enum ItemRarity
    {
        Common,     // 普通
        Uncommon,   // 不常见
        Rare,       // 稀有
        Epic,       // 史诗
        Legendary   // 传说
    }


}
