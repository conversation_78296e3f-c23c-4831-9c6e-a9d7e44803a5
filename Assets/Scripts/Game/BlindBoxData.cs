using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒内容物品
    /// </summary>
    [Serializable]
    public class BlindBoxItem
    {
        public string id;
        public string name;
        public string description;
        public BlindBoxContentType contentType;
        public float value;                     // 价值
        public bool isPositive = true;          // 是否为正面物品
        public List<ItemEffect> effects;        // 物品效果

        public BlindBoxItem()
        {
            effects = new List<ItemEffect>();
        }

        public BlindBoxItem(string id, string name, BlindBoxContentType contentType, float value, bool isPositive = true)
        {
            this.id = id;
            this.name = name;
            this.contentType = contentType;
            this.value = value;
            this.isPositive = isPositive;
            this.effects = new List<ItemEffect>();
        }
    }

    /// <summary>
    /// 物品效果
    /// </summary>
    [Serializable]
    public class ItemEffect
    {
        public ItemEffectType effectType;   // 效果类型
        public AttributeType targetAttribute;      // 目标属性
        public float value;                 // 效果值
        public float duration;              // 持续时间
        public bool isPercentage;           // 是否为百分比
        public ResourceType? resourceType;  // 资源类型（如果是资源效果）
        public StatusEffectType? statusEffect; // 状态效果类型
        public StatusEffectType? statusEffectType; // 状态效果类型（兼容性）
        public int statusDuration;          // 状态持续时间

        public ItemEffect()
        {
            effectType = ItemEffectType.AttributeChange;
        }

        public ItemEffect(AttributeType targetAttribute, float value, bool isPercentage = false)
        {
            effectType = ItemEffectType.AttributeChange;
            this.targetAttribute = targetAttribute;
            this.value = value;
            this.isPercentage = isPercentage;
        }

        public ItemEffect(ResourceType resourceType, float value)
        {
            this.effectType = ItemEffectType.ResourceChange;
            this.resourceType = resourceType;
            this.value = value;
        }

        public ItemEffect(StatusEffectType statusEffect, int duration, float intensity = 1f)
        {
            this.effectType = ItemEffectType.StatusEffect;
            this.statusEffect = statusEffect;
            this.statusEffectType = statusEffect;
            this.statusDuration = duration;
            this.duration = duration;
            this.value = intensity;
        }
    }

    /// <summary>
    /// 盲盒配置
    /// </summary>
    [Serializable]
    public class BlindBoxConfig
    {
        public BlindBoxType type;
        public string name;
        public string description;
        public float basePrice;                 // 基础价格
        public ResourceType priceType;          // 价格类型
        public List<BlindBoxLootTable> lootTables; // 掉落表

        public BlindBoxConfig()
        {
            lootTables = new List<BlindBoxLootTable>();
        }
    }

    /// <summary>
    /// 盲盒掉落表
    /// </summary>
    [Serializable]
    public class BlindBoxLootTable
    {
        public string itemId;
        public float baseWeight;        // 基础权重
        public float displayProbability; // 显示概率（可能是虚假的）
        public float realProbability;   // 真实概率
        public List<LootCondition> conditions; // 掉落条件

        public BlindBoxLootTable()
        {
            conditions = new List<LootCondition>();
        }
    }

    /// <summary>
    /// 掉落条件
    /// </summary>
    [Serializable]
    public class LootCondition
    {
        public string conditionType;    // 条件类型
        public string parameter;        // 参数
        public float value;             // 值
        public bool isRequired;         // 是否必须满足

        public LootCondition(string conditionType, string parameter, float value, bool isRequired = false)
        {
            this.conditionType = conditionType;
            this.parameter = parameter;
            this.value = value;
            this.isRequired = isRequired;
        }
    }

    /// <summary>
    /// 盲盒开启结果
    /// </summary>
    [Serializable]
    public class BlindBoxResult
    {
        public BlindBoxItem item;
        public bool wasManipulated;     // 是否被操控
        public float actualProbability; // 实际概率
        public string manipulationReason; // 操控原因
        public List<string> hiddenEffects; // 隐藏效果

        public BlindBoxResult()
        {
            hiddenEffects = new List<string>();
        }
    }

    /// <summary>
    /// 概率操控记录
    /// </summary>
    [Serializable]
    public class ProbabilityManipulation
    {
        public string playerId;
        public DateTime timestamp;
        public BlindBoxType boxType;
        public float originalProbability;
        public float manipulatedProbability;
        public string reason;
        public ManipulationType type;

        public enum ManipulationType
        {
            PlayerValueAdjustment,  // 玩家价值调整
            SystemBalance,          // 系统平衡
            PsychologicalControl,   // 心理控制
            PunishmentMechanism,    // 惩罚机制
            RewardMechanism,        // 奖励机制
            ExperimentalData        // 实验数据收集
        }
    }

    /// <summary>
    /// 盲盒历史记录
    /// </summary>
    [Serializable]
    public class BlindBoxHistory
    {
        public DateTime openTime;
        public BlindBoxType boxType;
        public BlindBoxResult result;
        public PlayerData playerStateAtTime; // 开启时的玩家状态快照
        public float emotionalResponse;      // 情绪反应评分
        public bool suspectedFraud;          // 是否怀疑欺诈

        public BlindBoxHistory(BlindBoxType boxType, BlindBoxResult result, PlayerData playerState)
        {
            this.openTime = DateTime.Now;
            this.boxType = boxType;
            this.result = result;
            this.playerStateAtTime = playerState;
            this.emotionalResponse = 0f;
            this.suspectedFraud = false;
        }
    }

    /// <summary>
    /// 潘多拉AI系统状态
    /// </summary>
    [Serializable]
    public class PandoraSystemState
    {
        public float globalResourcePool;        // 全局资源池
        public float playerValueAssessment;     // 玩家价值评估
        public float experimentalProgress;      // 实验进度
        public int totalPlayersActive;          // 活跃玩家数
        public float systemStability;           // 系统稳定性
        public List<string> activeExperiments;  // 活跃实验
        public Dictionary<string, float> globalProbabilityModifiers; // 全局概率修正

        public PandoraSystemState()
        {
            activeExperiments = new List<string>();
            globalProbabilityModifiers = new Dictionary<string, float>();
        }
    }

    /// <summary>
    /// 黑市交易数据
    /// </summary>
    [Serializable]
    public class BlackMarketTrade
    {
        public string tradeId;
        public string sellerId;
        public string buyerId;
        public BlindBoxItem item;
        public ResourceType priceType;
        public float price;
        public float riskLevel;             // 风险等级
        public bool isTracked;              // 是否被追踪
        public DateTime tradeTime;
        public TradeStatus status;

        public enum TradeStatus
        {
            Pending,    // 待处理
            Completed,  // 已完成
            Failed,     // 失败
            Intercepted // 被拦截
        }
    }

    /// <summary>
    /// 概率欺诈算法配置
    /// </summary>
    [Serializable]
    public class FraudAlgorithmConfig
    {
        [Header("基础参数")]
        public float baseFraudRate = 0.3f;          // 基础欺诈率
        public float maxProbabilityDeviation = 0.5f; // 最大概率偏差
        
        [Header("玩家状态影响")]
        public float dependenceInfluence = 0.2f;    // 依存度影响
        public float luckInfluence = 0.1f;          // 幸运值影响
        public float socialClassInfluence = 0.3f;   // 社会阶层影响
        
        [Header("心理控制参数")]
        public float addictionThreshold = 60f;      // 成瘾阈值
        public float frustrationThreshold = 70f;    // 挫败阈值
        public float hopeManipulationRate = 0.4f;   // 希望操控率
        
        [Header("系统平衡")]
        public float resourcePoolInfluence = 0.25f; // 资源池影响
        public float globalBalanceWeight = 0.15f;   // 全局平衡权重
    }
}
