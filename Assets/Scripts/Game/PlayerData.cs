using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 玩家属性数据
    /// </summary>
    [Serializable]
    public class PlayerAttributes
    {
        [Header("基础属性")]
        [Range(0, 100)] public float health = 100f;         // 生命值
        [Range(0, 100)] public float energy = 100f;         // 精力值
        [Range(0, 10)] public float luck = 5f;              // 幸运值
        [Range(0, 100)] public float social = 50f;          // 社交值
        
        [Header("心理属性")]
        [Range(0, 100)] public float dependence = 0f;       // 依存度
        [Range(0, 100)] public float cognition = 100f;      // 认知值
        [Range(0, 100)] public float pollution = 0f;        // 污染度
        [Range(0, 100)] public float humanity = 100f;       // 人性值
        [Range(0, 100)] public float morality = 50f;        // 道德值

        /// <summary>
        /// 初始化属性
        /// </summary>
        public void Initialize()
        {
            health = 100f;
            energy = 100f;
            luck = 5f;
            social = 50f;
            dependence = 0f;
            cognition = 100f;
            pollution = 0f;
            humanity = 100f;
            morality = 50f;
        }

        /// <summary>
        /// 修改属性值
        /// </summary>
        public void ModifyAttribute(AttributeType? attributeType, float value)
        {
            Debug.Log($"ModifyAttribute: {attributeType} - {value}");

            switch (attributeType)
            {
                case AttributeType.Health:
                    health = Mathf.Clamp(health + value, 0f, 100f);
                    break;
                case AttributeType.Energy:
                    energy = Mathf.Clamp(energy + value, 0f, 100f);
                    break;
                case AttributeType.Luck:
                    luck = Mathf.Clamp(luck + value, 0f, 10f);
                    break;
                case AttributeType.Social:
                    social = Mathf.Clamp(social + value, 0f, 100f);
                    break;
                case AttributeType.Dependence:
                    dependence = Mathf.Clamp(dependence + value, 0f, 100f);
                    break;
                case AttributeType.Cognition:
                    cognition = Mathf.Clamp(cognition + value, 0f, 100f);
                    break;
                case AttributeType.Pollution:
                    pollution = Mathf.Clamp(pollution + value, 0f, 100f);
                    break;
                case AttributeType.Humanity:
                    humanity = Mathf.Clamp(humanity + value, 0f, 100f);
                    break;
                case AttributeType.Morality:
                    morality = Mathf.Clamp(morality + value, 0f, 100f);
                    break;
            }
        }


        /// <summary>
        /// 获取属性值（推荐使用枚举版本）
        /// </summary>
        public float GetAttributeValue(AttributeType? attributeType)
        {
            return attributeType switch
            {
                AttributeType.Health => health,
                AttributeType.Energy => energy,
                AttributeType.Luck => luck,
                AttributeType.Social => social,
                AttributeType.Dependence => dependence,
                AttributeType.Cognition => cognition,
                AttributeType.Pollution => pollution,
                AttributeType.Humanity => humanity,
                AttributeType.Morality => morality,
                _ => 0f
            };
        }


        /// <summary>
        /// 设置属性值
        /// </summary>
        public void SetAttributeValue(AttributeType attributeType, float value)
        {
            switch (attributeType)
            {
                case AttributeType.Health:
                    health = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Energy:
                    energy = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Luck:
                    luck = Mathf.Clamp(value, 0f, 10f);
                    break;
                case AttributeType.Social:
                    social = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Dependence:
                    dependence = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Cognition:
                    cognition = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Pollution:
                    pollution = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Humanity:
                    humanity = Mathf.Clamp(value, 0f, 100f);
                    break;
                case AttributeType.Morality:
                    morality = Mathf.Clamp(value, 0f, 100f);
                    break;
            }
        }

    }

    /// <summary>
    /// 玩家资源数据
    /// </summary>
    [Serializable]
    public class PlayerResources
    {
        public float credits = 200f;        // 信用点
        public float time = 100f;           // 时间
        public float diamond = 0f;          // 钻石
        public float socialScore = 0f;      // 社会积分

        /// <summary>
        /// 初始化资源
        /// </summary>
        public void Initialize()
        {
            credits = 20000f;
            time = 100f;
            diamond = 100f;
            socialScore = 0f;
        }

        /// <summary>
        /// 消耗资源
        /// </summary>
        public bool ConsumeResource(ResourceType type, float amount)
        {
            switch (type)
            {
                case ResourceType.Credits:
                    if (credits >= amount)
                    {
                        credits -= amount;
                        return true;
                    }
                    break;
                case ResourceType.Time:
                    if (time >= amount)
                    {
                        time -= amount;
                        return true;
                    }
                    break;
                case ResourceType.Diamond:
                    if (diamond >= amount)
                    {
                        diamond -= amount;
                        return true;
                    }
                    break;
                case ResourceType.SocialScore:
                    if (socialScore >= amount)
                    {
                        socialScore -= amount;
                        return true;
                    }
                    break;
            }
            return false;
        }

        /// <summary>
        /// 添加资源
        /// </summary>
        public void AddResource(ResourceType type, float amount)
        {
            switch (type)
            {
                case ResourceType.Credits:
                    credits += amount;
                    break;
                case ResourceType.Time:
                    time += amount;
                    break;
                case ResourceType.Diamond:
                    diamond += amount;
                    break;
                case ResourceType.SocialScore:
                    socialScore += amount;
                    break;
            }
        }

        /// <summary>
        /// 获取资源数量
        /// </summary>
        public float GetResource(ResourceType type)
        {
            switch (type)
            {
                case ResourceType.Credits: return credits;
                case ResourceType.Time: return time;
                case ResourceType.Diamond: return diamond;
                case ResourceType.SocialScore: return socialScore;
                default: return 0f;
            }
        }
    }

    /// <summary>
    /// 玩家数据主类
    /// </summary>
    [Serializable]
    public class PlayerData
    {
        [Header("基础信息")]
        public string playerName = "无名者";
        public SocialClass socialClass = SocialClass.Parasite;
        public JobType currentJob = JobType.None;
        public int daysSurvived = 0;
        public int totalBoxesOpened = 0;

        [Header("属性和资源")]
        public PlayerAttributes attributes;
        public PlayerResources resources;

        [Header("天赋和状态")]
        public List<TalentType> talents;
        public List<StatusEffect> statusEffects;

        [Header("游戏进度")]
        public List<string> unlockedEvents;
        public List<string> completedAchievements;
        public bool hasMetResistance = false;
        public bool knowsTruth = false;

        [Header("特殊能力")]
        public Dictionary<string, bool> specialFlags;

        [Header("计数器")]
        public Dictionary<string, float> counters;

        /// <summary>
        /// 初始化玩家数据
        /// </summary>
        public void Initialize()
        {
            playerName = "无名者";
            socialClass = SocialClass.Parasite;
            currentJob = JobType.None;
            daysSurvived = 0;
            totalBoxesOpened = 0;

            attributes = new PlayerAttributes();
            attributes.Initialize();

            resources = new PlayerResources();
            resources.Initialize();

            talents = new List<TalentType>();
            statusEffects = new List<StatusEffect>();
            unlockedEvents = new List<string>();
            completedAchievements = new List<string>();
            specialFlags = new Dictionary<string, bool>();
            counters = new Dictionary<string, float>();

            hasMetResistance = false;
            knowsTruth = false;
        }

        /// <summary>
        /// 添加天赋
        /// </summary>
        public void AddTalent(TalentType talent)
        {
            if (!talents.Contains(talent))
            {
                talents.Add(talent);
            }
        }

        public void RemoveTalent(TalentType talent)
        {
            if (talents.Contains(talent))
            {
                talents.Remove(talent);
            }
        }

        /// <summary>
        /// 检查是否拥有天赋
        /// </summary>
        public bool HasTalent(TalentType talent)
        {
            return talents.Contains(talent);
        }

        /// <summary>
        /// 添加状态效果
        /// </summary>
        public void AddStatusEffect(StatusEffect effect)
        {
            statusEffects.Add(effect);
        }

        /// <summary>
        /// 移除状态效果
        /// </summary>
        public void RemoveStatusEffect(StatusEffectType type)
        {
            statusEffects.RemoveAll(effect => effect.type == type);
        }

        /// <summary>
        /// 检查是否有特定状态效果
        /// </summary>
        public bool HasStatusEffect(StatusEffectType type)
        {
            return statusEffects.Exists(effect => effect.type == type);
        }

        /// <summary>
        /// 设置特殊标志
        /// </summary>
        public void SetSpecialFlag(string flagName, bool value)
        {
            if (specialFlags == null)
            {
                specialFlags = new Dictionary<string, bool>();
            }
            specialFlags[flagName] = value;
        }

        /// <summary>
        /// 获取特殊标志
        /// </summary>
        public bool GetSpecialFlag(string flagName)
        {
            if (specialFlags == null)
            {
                specialFlags = new Dictionary<string, bool>();
                return false;
            }
            return specialFlags.ContainsKey(flagName) && specialFlags[flagName];
        }

        /// <summary>
        /// 检查是否有特殊标志
        /// </summary>
        public bool HasSpecialFlag(string flagName)
        {
            return specialFlags != null && specialFlags.ContainsKey(flagName);
        }

        /// <summary>
        /// 获取计数器值
        /// </summary>
        public float GetCounter(string counterName)
        {
            if (counters == null)
            {
                counters = new Dictionary<string, float>();
                return 0f;
            }
            return counters.ContainsKey(counterName) ? counters[counterName] : 0f;
        }

        /// <summary>
        /// 设置计数器值
        /// </summary>
        public void SetCounter(string counterName, float value)
        {
            if (counters == null)
            {
                counters = new Dictionary<string, float>();
            }
            counters[counterName] = value;
        }

        /// <summary>
        /// 增加计数器值
        /// </summary>
        public void AddToCounter(string counterName, float value)
        {
            if (counters == null)
            {
                counters = new Dictionary<string, float>();
            }
            if (!counters.ContainsKey(counterName))
            {
                counters[counterName] = 0f;
            }
            counters[counterName] += value;
        }

        /// <summary>
        /// 每日更新
        /// </summary>
        public void DailyUpdate()
        {
            daysSurvived++;
            
            // 每日基础资源获取
            resources.AddResource(ResourceType.Credits, GetDailyCredits());
            
            // 更新状态效果
            UpdateStatusEffects();
            
            // 每日消耗
            DailyConsumption();
        }

        private float GetDailyCredits()
        {
            float baseCredits = 50f;
            
            // 根据社会阶层调整
            switch (socialClass)
            {
                case SocialClass.Parasite:
                    baseCredits = 50f;
                    break;
                case SocialClass.Worker:
                    baseCredits = 100f;
                    break;
                case SocialClass.Chosen:
                    baseCredits = 200f;
                    break;
            }

            // 根据工作调整
            if (currentJob != JobType.None)
            {
                baseCredits *= 1.5f;
            }

            return baseCredits;
        }

        private void UpdateStatusEffects()
        {
            for (int i = statusEffects.Count - 1; i >= 0; i--)
            {
                statusEffects[i].duration--;
                if (statusEffects[i].duration <= 0)
                {
                    statusEffects.RemoveAt(i);
                }
            }
        }

        private void DailyConsumption()
        {
            // 基础生存消耗
            attributes.ModifyAttribute(AttributeType.Energy, -10f);

            // 根据状态效果调整消耗
            if (HasStatusEffect(StatusEffectType.Sickness))
            {
                attributes.ModifyAttribute(AttributeType.Health, -5f);
            }

            if (HasStatusEffect(StatusEffectType.Fatigue))
            {
                attributes.ModifyAttribute(AttributeType.Energy, -5f);
            }
        }
    }

    /// <summary>
    /// 状态效果
    /// </summary>
    [Serializable]
    public class StatusEffect
    {
        public StatusEffectType type;
        public float intensity;     // 强度
        public int duration;        // 持续时间（天）
        public string description;

        public StatusEffect(StatusEffectType type, float intensity, int duration, string description = "")
        {
            this.type = type;
            this.intensity = intensity;
            this.duration = duration;
            this.description = description;
        }
    }
}
