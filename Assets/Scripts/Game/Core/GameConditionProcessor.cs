using System;
using System.Linq;
using UnityEngine;

namespace BoxOfFate.Game.Core
{
    /// <summary>
    /// 游戏条件处理器 - 统一处理所有游戏条件
    /// </summary>
    public static class GameConditionProcessor
    {
        /// <summary>
        /// 检查游戏条件
        /// </summary>
        public static bool CheckCondition(GameCondition condition, PlayerData playerData)
        {
            if (condition == null || playerData == null) return false;

            try
            {
                switch (condition.type)
                {
                    case GameConditionType.AttributeThreshold:
                        return CheckAttributeThreshold(condition, playerData);
                        
                    case GameConditionType.AttributeRange:
                        return CheckAttributeRange(condition, playerData);
                        
                    case GameConditionType.ResourceThreshold:
                        return CheckResourceThreshold(condition, playerData);
                        
                    case GameConditionType.ResourceRange:
                        return CheckResourceRange(condition, playerData);
                        
                    case GameConditionType.HasStatusEffect:
                        return CheckHasStatusEffect(condition, playerData);
                        
                    case GameConditionType.HasTalent:
                        return CheckHasTalent(condition, playerData);
                        
                    case GameConditionType.DaysSurvived:
                        return CheckDaysSurvived(condition, playerData);
                        
                    case GameConditionType.BoxesOpened:
                        return CheckBoxesOpened(condition, playerData);
                        
                    case GameConditionType.HasFlag:
                        return CheckHasFlag(condition, playerData);
                        
                    case GameConditionType.SpecialTask:
                        return CheckSpecialTask(condition, playerData);
                    
                    case GameConditionType.TruthDiscovered:
                        return CheckTruthDiscovered(condition, playerData);
                        
                    case GameConditionType.FeatureUnlocked:
                        return CheckFeatureUnlocked(condition, playerData);
                    case GameConditionType.NoNegativeStatus:
                        return CheckNoNegativeStatus(condition, playerData);
                        
                    default:
                        Debug.LogWarning($"未处理的条件类型: {condition.type}");
                        return false;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"检查条件时发生错误: {condition.type}, {e.Message}");
                return false;
            }
        }

        public static float GetConditionProgress(GameCondition condition, PlayerData playerData)
        {
            if (condition == null || playerData == null) return 0;

            try
            {
                switch (condition.type)
                {
                    // case GameConditionType.ProgressValue:
                    //     if (progressTrackers.ContainsKey(condition.trackerId))
                    //     {
                    //         return Mathf.Clamp01(progressTrackers[condition.trackerId].currentValue / condition.value);
                    //     }
                    //     return 0f;
                    
                    case GameConditionType.AttributeThreshold:
                        if (!condition.attributeType.HasValue) return 0;
                        float currentValue = playerData.attributes.GetAttributeValue(condition.attributeType.Value);
                        return Mathf.Clamp01(currentValue / condition.value);
                    
                    case GameConditionType.ResourceThreshold:
                        if (!condition.resourceType.HasValue) return 0;
            
                        currentValue = playerData.resources.GetResource(condition.resourceType.Value);
                        return Mathf.Clamp01(currentValue / condition.value);
                    
                    case GameConditionType.DaysSurvived:
                        return Mathf.Clamp01(playerData.daysSurvived / condition.value);
                    
                    case GameConditionType.BoxesOpened:
                        return Mathf.Clamp01(playerData.totalBoxesOpened / condition.value);
                    
                    default:
                        return CheckCondition(condition, playerData) ? 1f : 0f;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"检查条件时发生错误: {condition.type}, {e.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取条件的显示文本
        /// </summary>
        public static string GetConditionDisplayText(GameCondition condition)
        {
            if (condition == null) return "";

            if (!string.IsNullOrEmpty(condition.description))
                return condition.description;

            switch (condition.type)
            {
                case GameConditionType.AttributeThreshold:
                    if (condition.attributeType.HasValue)
                    {
                        return $"{GetAttributeDisplayName(condition.attributeType.Value)} ≥ {condition.value:F0}";
                    }
                    break;

                case GameConditionType.AttributeRange:
                    if (condition.attributeType.HasValue)
                    {
                        return $"{GetAttributeDisplayName(condition.attributeType.Value)}: {condition.minValue:F0}-{condition.maxValue:F0}";
                    }
                    break;

                case GameConditionType.ResourceThreshold:
                    if (condition.resourceType.HasValue)
                    {
                        return $"{GetResourceDisplayName(condition.resourceType.Value)} ≥ {condition.value:F0}";
                    }
                    break;

                case GameConditionType.HasTalent:
                    if (condition.talentType.HasValue)
                    {
                        return $"拥有天赋: {GetTalentDisplayName(condition.talentType.Value)}";
                    }
                    break;

                case GameConditionType.HasStatusEffect:
                    if (condition.statusEffectType.HasValue)
                    {
                        return $"拥有状态: {GetStatusEffectDisplayName(condition.statusEffectType.Value)}";
                    }
                    break;

                case GameConditionType.DaysSurvived:
                    return $"生存天数 ≥ {condition.value:F0}";

                case GameConditionType.BoxesOpened:
                    return $"开盒数量 ≥ {condition.value:F0}";

                case GameConditionType.HasFlag:
                    return $"拥有标志: {condition.stringValue}";

                case GameConditionType.SpecialTask:
                    return $"完成任务: {condition.stringValue}";
            }

            return condition.type.ToString();
        }

        #region 私有方法 - 具体条件检查

        private static bool CheckAttributeThreshold(GameCondition condition, PlayerData playerData)
        {
            if (!condition.attributeType.HasValue) return false;
            
            float currentValue = playerData.attributes.GetAttributeValue(condition.attributeType.Value);
            return currentValue >= condition.value;
        }

        private static bool CheckAttributeRange(GameCondition condition, PlayerData playerData)
        {
            if (!condition.attributeType.HasValue) return false;
            
            float currentValue = playerData.attributes.GetAttributeValue(condition.attributeType.Value);
            return currentValue >= condition.minValue && currentValue <= condition.maxValue;
        }

        private static bool CheckResourceThreshold(GameCondition condition, PlayerData playerData)
        {
            if (!condition.resourceType.HasValue) return false;
            
            float currentValue = playerData.resources.GetResource(condition.resourceType.Value);
            return currentValue >= condition.value;
        }

        private static bool CheckResourceRange(GameCondition condition, PlayerData playerData)
        {
            if (!condition.resourceType.HasValue) return false;
            
            float currentValue = playerData.resources.GetResource(condition.resourceType.Value);
            return currentValue >= condition.minValue && currentValue <= condition.maxValue;
        }

        private static bool CheckHasStatusEffect(GameCondition condition, PlayerData playerData)
        {
            if (!condition.statusEffectType.HasValue) return false;
            
            return playerData.HasStatusEffect(condition.statusEffectType.Value);
        }

        private static bool CheckHasTalent(GameCondition condition, PlayerData playerData)
        {
            if (!condition.talentType.HasValue) return false;
            
            return playerData.HasTalent(condition.talentType.Value);
        }


        private static bool CheckDaysSurvived(GameCondition condition, PlayerData playerData)
        {
            return playerData.daysSurvived >= condition.value;
        }

        private static bool CheckBoxesOpened(GameCondition condition, PlayerData playerData)
        {
            return playerData.totalBoxesOpened >= condition.value;
        }

        private static bool CheckHasFlag(GameCondition condition, PlayerData playerData)
        {
            if (string.IsNullOrEmpty(condition.stringValue)) return false;
            
            return playerData.GetSpecialFlag(condition.stringValue) == condition.boolValue;
        }

        private static bool CheckNoFlag(GameCondition condition, PlayerData playerData)
        {
            if (string.IsNullOrEmpty(condition.stringValue)) return true;
            
            return playerData.GetSpecialFlag(condition.stringValue) != condition.boolValue;
        }

        private static bool CheckSpecialTask(GameCondition condition, PlayerData playerData)
        {
            if (string.IsNullOrEmpty(condition.stringValue)) return false;
            
            // 这里可以检查特殊任务的完成状态
            return playerData.GetSpecialFlag($"task_completed_{condition.stringValue}");
        }

        private static bool CheckTruthDiscovered(GameCondition condition, PlayerData playerData)
        {
            if (string.IsNullOrEmpty(condition.stringValue)) return false;
            
            return playerData.GetSpecialFlag($"truth_discovered_{condition.stringValue}");
        }

        private static bool CheckFeatureUnlocked(GameCondition condition, PlayerData playerData)
        {
            if (string.IsNullOrEmpty(condition.stringValue)) return false;
            
            return playerData.GetSpecialFlag($"feature_unlocked_{condition.stringValue}");
        }
        private static bool CheckNoNegativeStatus(GameCondition condition, PlayerData playerData)
        {
            // 这需要创建一个复合条件
            var negativeStatuses = new[]
            {
                StatusEffectType.Sickness,
                StatusEffectType.Depression,
                StatusEffectType.Fatigue,
                StatusEffectType.CognitiveDamage
            };

            foreach (var status in negativeStatuses)
            {
                if (playerData.HasStatusEffect(status))
                    return false;
            }
            
            return true;
        }


        #endregion

        #region 显示名称获取

        private static string GetAttributeDisplayName(AttributeType attributeType)
        {
            return attributeType switch
            {
                AttributeType.Health => "生命值",
                AttributeType.Energy => "精力值",
                AttributeType.Luck => "幸运值",
                AttributeType.Social => "社交值",
                AttributeType.Dependence => "依存度",
                AttributeType.Cognition => "认知值",
                AttributeType.Pollution => "污染度",
                AttributeType.Humanity => "人性值",
                AttributeType.Morality => "道德值",
                _ => attributeType.ToString()
            };
        }

        private static string GetResourceDisplayName(ResourceType resourceType)
        {
            return resourceType switch
            {
                ResourceType.Credits => "信用点",
                ResourceType.Time => "时间",
                ResourceType.Diamond => "钻石",
                ResourceType.SocialScore => "社会积分",
                _ => resourceType.ToString()
            };
        }

        private static string GetStatusEffectDisplayName(StatusEffectType statusEffectType)
        {
            // 这里可以添加状态效果的显示名称映射
            return statusEffectType.ToString();
        }

        private static string GetTalentDisplayName(TalentType talentType)
        {
            // 这里可以添加天赋的显示名称映射
            return talentType.ToString();
        }

        #endregion
    }
}
