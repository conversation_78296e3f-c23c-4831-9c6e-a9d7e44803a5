using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game.Core
{
    /// <summary>
    /// 游戏效果处理器 - 统一处理所有游戏效果
    /// </summary>
    public static class GameEffectProcessor
    {
        /// <summary>
        /// 应用游戏效果
        /// </summary>
        public static void ApplyEffect(GameEffect effect, PlayerData playerData, float intensity = 1f)
        {
            if (effect == null || playerData == null) return;

            try
            {
                switch (effect.type)
                {
                    case GameEffectType.AttributeChange:
                        ApplyAttributeChange(effect, playerData, intensity);
                        break;
                        
                    case GameEffectType.AttributeSet:
                        ApplyAttributeSet(effect, playerData);
                        break;
                        
                    case GameEffectType.ResourceChange:
                        ApplyResourceChange(effect, playerData, intensity);
                        break;
                        
                    case GameEffectType.ResourceSet:
                        ApplyResourceSet(effect, playerData);
                        break;
                        
                    case GameEffectType.AddStatusEffect:
                        ApplyAddStatusEffect(effect, playerData);
                        break;
                        
                    case GameEffectType.RemoveStatusEffect:
                        ApplyRemoveStatusEffect(effect, playerData);
                        break;
                        
                    case GameEffectType.GrantTalent:
                        ApplyGrantTalent(effect, playerData);
                        break;
                        
                    case GameEffectType.RemoveTalent:
                        ApplyRemoveTalent(effect, playerData);
                        break;
                        
                    case GameEffectType.SetFlag:
                        ApplySetFlag(effect, playerData);
                        break;
                        
                    case GameEffectType.TimeChange:
                        ApplyTimeChange(effect, playerData);
                        break;
                        
                    case GameEffectType.TriggerEvent:
                        ApplyTriggerEvent(effect, playerData);
                        break;
                        
                    case GameEffectType.UnlockFeature:
                        ApplyUnlockFeature(effect, playerData);
                        break;
                        
                    case GameEffectType.MetaProgress:
                        ApplyMetaProgress(effect, playerData);
                        break;
                        
                    case GameEffectType.TruthDiscovery:
                        ApplyTruthDiscovery(effect, playerData);
                        break;
                    case GameEffectType.Memory:
                        ApplyMemoryLoss(effect, playerData);
                        break;
                        
                    default:
                        Debug.LogWarning($"未处理的效果类型: {effect.type}");
                        break;
                }

            }
            catch (Exception e)
            {
                Debug.LogError($"应用效果时发生错误: {effect.type}, {e.Message}");
            }
        }

        /// <summary>
        /// 检查效果是否可以应用
        /// </summary>
        public static bool CanApplyEffect(GameEffect effect, PlayerData playerData, float intensity = 1f)
        {
            if (effect == null || playerData == null) return false;

            // 基础检查
            switch (effect.type)
            {
                case GameEffectType.AttributeChange:
                    // 检查属性是否在可修改范围内
                    if (effect.value < 0 && effect.attributeType.HasValue)
                    {
                        return playerData.attributes.GetAttributeValue(effect.attributeType.Value)  >= Math.Abs(effect.value * intensity);
                    }
                    break;
                case GameEffectType.ResourceChange:
                    // 检查资源是否足够（如果是负值）
                    if (effect.value < 0 && effect.resourceType.HasValue)
                    {
                        return playerData.resources.GetResource(effect.resourceType.Value) >= Math.Abs(effect.value * intensity);
                    }
                    break;
                    
                case GameEffectType.RemoveTalent:
                    // 检查是否拥有该天赋
                    if (effect.talentType.HasValue)
                    {
                        return playerData.HasTalent(effect.talentType.Value);
                    }
                    break;
                    
                case GameEffectType.RemoveStatusEffect:
                    // 检查是否有该状态效果
                    if (effect.statusEffectType.HasValue)
                    {
                        return playerData.HasStatusEffect(effect.statusEffectType.Value);
                    }
                    break;
            }

            return true;
        }

        /// <summary>
        /// 获取效果的显示文本
        /// </summary>
        public static string GetEffectDisplayText(GameEffect effect)
        {
            if (effect == null) return "";

            if (!string.IsNullOrEmpty(effect.description))
                return effect.description;

            switch (effect.type)
            {
                case GameEffectType.AttributeChange:
                    if (effect.attributeType.HasValue)
                    {
                        string sign = effect.value >= 0 ? "+" : "";
                        return $"{GetAttributeDisplayName(effect.attributeType.Value)}: {sign}{effect.value:F0}";
                    }
                    break;
                    
                case GameEffectType.ResourceChange:
                    if (effect.resourceType.HasValue)
                    {
                        string sign = effect.value >= 0 ? "+" : "";
                        return $"{GetResourceDisplayName(effect.resourceType.Value)}: {sign}{effect.value:F0}";
                    }
                    break;
                    
                case GameEffectType.AddStatusEffect:
                    if (effect.statusEffectType.HasValue)
                    {
                        string duration = effect.duration > 0 ? $"({effect.duration}回合)" : "";
                        return $"获得状态: {GetStatusEffectDisplayName(effect.statusEffectType.Value)}{duration}";
                    }
                    break;
                    
                case GameEffectType.GrantTalent:
                    if (effect.talentType.HasValue)
                    {
                        return $"获得天赋: {GetTalentDisplayName(effect.talentType.Value)}";
                    }
                    break;
                    
                case GameEffectType.SetFlag:
                    return $"设置标志: {effect.stringValue} = {effect.boolValue}";
                    
                case GameEffectType.TimeChange:
                    string timeSign = effect.value >= 0 ? "+" : "";
                    return $"时间: {timeSign}{effect.value:F0}";
            }

            return effect.type.ToString();
        }

        #region 私有方法 - 具体效果实现

        private static void ApplyAttributeChange(GameEffect effect, PlayerData playerData, float intensity = 1f)
        {
            if (effect.attributeType.HasValue)
            {
                playerData.attributes.ModifyAttribute(effect.attributeType.Value, effect.value * intensity);
            }
        }

        private static void ApplyAttributeSet(GameEffect effect, PlayerData playerData)
        {
            if (effect.attributeType.HasValue)
            {
                playerData.attributes.SetAttributeValue(effect.attributeType.Value, effect.value);
            }
        }

        private static void ApplyResourceChange(GameEffect effect, PlayerData playerData, float intensity = 1f)
        {
            if (effect.resourceType.HasValue)
            {
                playerData.resources.AddResource(effect.resourceType.Value, effect.value * intensity);
            }
        }

        private static void ApplyResourceSet(GameEffect effect, PlayerData playerData)
        {
            if (effect.resourceType.HasValue)
            {
                playerData.resources.AddResource(effect.resourceType.Value, effect.value - playerData.resources.GetResource(effect.resourceType.Value));
            }
        }

        private static void ApplyAddStatusEffect(GameEffect effect, PlayerData playerData)
        {
            if (effect.statusEffectType.HasValue)
            {
                var statusEffect = new StatusEffect(effect.statusEffectType.Value, effect.value, effect.duration, effect.description);
                playerData.AddStatusEffect(statusEffect);
            }
        }

        private static void ApplyRemoveStatusEffect(GameEffect effect, PlayerData playerData)
        {
            if (effect.statusEffectType.HasValue)
            {
                playerData.RemoveStatusEffect(effect.statusEffectType.Value);
            }
        }

        private static void ApplyGrantTalent(GameEffect effect, PlayerData playerData)
        {
            if (effect.talentType.HasValue)
            {
                playerData.AddTalent(effect.talentType.Value);
            }
        }

        private static void ApplyRemoveTalent(GameEffect effect, PlayerData playerData)
        {
            if (effect.talentType.HasValue)
            {
                playerData.RemoveTalent(effect.talentType.Value);
            }
        }

        private static void ApplySetFlag(GameEffect effect, PlayerData playerData)
        {
            if (!string.IsNullOrEmpty(effect.stringValue))
            {
                playerData.SetSpecialFlag(effect.stringValue, effect.boolValue);
            }
        }

        private static void ApplyTimeChange(GameEffect effect, PlayerData playerData)
        {
            playerData.resources.AddResource(ResourceType.Time, effect.value);
        }

        private static void ApplyTriggerEvent(GameEffect effect, PlayerData playerData)
        {
            // 触发事件的逻辑
            if (!string.IsNullOrEmpty(effect.stringValue))
            {
                // 这里可以调用事件系统
                Debug.Log($"触发事件: {effect.stringValue}");
            }
        }

        private static void ApplyUnlockFeature(GameEffect effect, PlayerData playerData)
        {
            // 解锁功能的逻辑
            if (!string.IsNullOrEmpty(effect.stringValue))
            {
                playerData.SetSpecialFlag($"feature_unlocked_{effect.stringValue}", true);
            }
        }

        private static void ApplyMetaProgress(GameEffect effect, PlayerData playerData)
        {
            // 元游戏进度的逻辑
            Debug.Log($"元游戏进度: {effect.stringValue}");
        }

        private static void ApplyTruthDiscovery(GameEffect effect, PlayerData playerData)
        {
            // 真相发现的逻辑
            if (!string.IsNullOrEmpty(effect.stringValue))
            {
                playerData.SetSpecialFlag($"truth_discovered_{effect.stringValue}", true);
            }
        }

        /// <summary>
        /// 应用记忆丢失
        /// </summary>
        private static void ApplyMemoryLoss(GameEffect effect, PlayerData playerData)
        {
            // 添加记忆丢失状态效果
            var memoryLoss = new StatusEffect(StatusEffectType.MemoryLoss, 1f, -1, $"丢失了关于{effect.stringValue}的记忆");
            playerData.AddStatusEffect(memoryLoss);

            // 可能影响认知值
            playerData.attributes.ModifyAttribute(AttributeType.Cognition, -5f);
        }

        #endregion

        #region 显示名称获取

        private static string GetAttributeDisplayName(AttributeType attributeType)
        {
            return attributeType switch
            {
                AttributeType.Health => "生命值",
                AttributeType.Energy => "精力值",
                AttributeType.Luck => "幸运值",
                AttributeType.Social => "社交值",
                AttributeType.Dependence => "依存度",
                AttributeType.Cognition => "认知值",
                AttributeType.Pollution => "污染度",
                AttributeType.Humanity => "人性值",
                AttributeType.Morality => "道德值",
                _ => attributeType.ToString()
            };
        }

        private static string GetResourceDisplayName(ResourceType resourceType)
        {
            return resourceType switch
            {
                ResourceType.Credits => "信用点",
                ResourceType.Time => "时间",
                ResourceType.Diamond => "钻石",
                ResourceType.SocialScore => "社会积分",
                _ => resourceType.ToString()
            };
        }

        private static string GetStatusEffectDisplayName(StatusEffectType statusEffectType)
        {
            // 这里可以添加状态效果的显示名称映射
            return statusEffectType.ToString();
        }

        private static string GetTalentDisplayName(TalentType talentType)
        {
            // 这里可以添加天赋的显示名称映射
            return talentType.ToString();
        }

        #endregion
    }
}
