using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game.Core
{
    /// <summary>
    /// 游戏效果类型 - 统一的效果分类
    /// </summary>
    public enum GameEffectType
    {
        // 属性相关
        AttributeChange,        // 属性修改
        AttributeSet,          // 属性设置

        // 资源相关
        ResourceChange,        // 资源修改
        ResourceSet,          // 资源设置

        // 状态效果
        AddStatusEffect,      // 添加状态效果
        RemoveStatusEffect,   // 移除状态效果

        // 天赋相关
        GrantTalent,         // 授予天赋
        RemoveTalent,        // 移除天赋

        // 特殊效果
        TriggerEvent,        // 触发事件
        UnlockFeature,       // 解锁功能
        SetFlag,             // 设置标志

        // 时间相关
        TimeChange,          // 时间修改

        // 元游戏
        MetaProgress,        // 元游戏进度
        TruthDiscovery,      // 真相发现

        //其他
        Memory, // 记忆代价
        AddTalent,
        UnlockEvent,
        StoryProgress,
        Talent, //天赋
        Title, //称号
        StatusImmunity,//状态免疫
        SpecialAbility, //特殊能力
        ProbabilityModifier,//概率修正
    }

    /// <summary>
    /// 游戏条件类型 - 统一的条件分类
    /// </summary>
    public enum GameConditionType
    {
        // 属性条件
        AttributeThreshold,   // 属性阈值
        AttributeRange,      // 属性范围

        // 资源条件
        ResourceThreshold,   // 资源阈值
        ResourceRange,      // 资源范围

        // 状态条件
        HasStatusEffect,    // 拥有状态效果

        // 天赋条件
        HasTalent,         // 拥有天赋

        // 进度条件
        DaysSurvived,      // 生存天数
        BoxesOpened,       // 开盒数量

        // 特殊条件
        HasFlag,           // 拥有标志
        SpecialTask,       // 特殊任务

        // 元游戏条件
        TruthDiscovered,   // 真相已发现
        FeatureUnlocked,   // 功能已解锁

        //其他
        NoNegativeStatus, //没有负面状态
        SpecialEvent,//特殊事件
        
    }

    /// <summary>
    /// 统一的游戏效果类 - 替代所有 **Effect 类
    /// </summary>
    [Serializable]
    public class GameEffect
    {
        [Header("基础信息")]
        public GameEffectType type;
        public string description;
        
        [Header("目标")]
        public AttributeType? attributeType;
        public ResourceType? resourceType;
        public StatusEffectType? statusEffectType;
        public TalentType? talentType;
        
        [Header("数值")]
        public float value;
        public float minValue;
        public float maxValue;
        public int duration = -1;  // -1 表示永久
        
        [Header("特殊参数")]
        public string stringValue;  // 用于事件ID、标志名等
        public bool boolValue;      // 用于标志值等
    
        // 构造函数重载
        public GameEffect() { }

        public GameEffect(GameEffectType type, AttributeType? attributeType, float value, string description = "")
        {
            this.type = type;
            this.attributeType = attributeType;
            this.value = value;
            this.description = description;
        }

        public GameEffect(GameEffectType type, ResourceType? resourceType, float value, string description = "")
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
            this.description = description;
        }

        public GameEffect(GameEffectType type, StatusEffectType? statusEffectType, float intensity = 0f, int duration = -1, string description = "")
        {
            this.type = type;
            this.statusEffectType = statusEffectType;
            this.value = intensity;
            this.duration = duration;
            this.description = description;
        }

        public GameEffect(GameEffectType type, TalentType? talentType, string description = "")
        {
            this.type = type;
            this.talentType = talentType;
            this.description = description;
        }

        public GameEffect(GameEffectType type, string stringValue, float value = 0f, string description = "")
        {
            this.type = type;
            this.stringValue = stringValue;
            this.value = value;
            this.description = description;
        }

        /// <summary>
        /// 应用效果到玩家数据
        /// </summary>
        public void Apply(PlayerData playerData, float intensity = 1f)
        {
            GameEffectProcessor.ApplyEffect(this, playerData, intensity);
        }

        /// <summary>
        /// 检查效果是否可以应用
        /// </summary>
        public bool CanApply(PlayerData playerData, float intensity = 1f)
        {
            return GameEffectProcessor.CanApplyEffect(this, playerData, intensity);
        }

        /// <summary>
        /// 获取效果的显示文本
        /// </summary>
        public string GetDisplayText()
        {
            return GameEffectProcessor.GetEffectDisplayText(this);
        }
    }

    /// <summary>
    /// 统一的游戏条件类 - 替代所有 **Condition 类
    /// </summary>
    [Serializable]
    public class GameCondition
    {
        [Header("基础信息")]
        public GameConditionType type;
        public string description;
        
        [Header("目标")]
        public AttributeType? attributeType;
        public ResourceType? resourceType;
        public StatusEffectType? statusEffectType;
        public TalentType? talentType;
        
        [Header("数值")]
        public float value;
        public float minValue;
        public float maxValue;
        
        [Header("特殊参数")]
        public string stringValue;  // 用于标志名、任务ID等
        public bool boolValue;      // 用于标志值等
        

        // 构造函数重载
        public GameCondition() { }

        public GameCondition(GameConditionType type, AttributeType attributeType, float value, string description = "")
        {
            this.type = type;
            this.attributeType = attributeType;
            this.value = value;
            this.description = description;
        }

        public GameCondition(GameConditionType type, ResourceType resourceType, float value, string description = "")
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
            this.description = description;
        }

        public GameCondition(GameConditionType type, TalentType talentType, string description = "")
        {
            this.type = type;
            this.talentType = talentType;
            this.description = description;
        }

        public GameCondition(GameConditionType type, string stringValue, float value = 0f, string description = "")
        {
            this.type = type;
            this.stringValue = stringValue;
            this.value = value;
            this.description = description;
        }

        public GameCondition(GameConditionType type, float value = 0f, string description = "")
        {
            this.type = type;
            this.value = value;
            this.description = description;
        }

        /// <summary>
        /// 检查条件是否满足
        /// </summary>
        public bool Check(PlayerData playerData)
        {
            return GameConditionProcessor.CheckCondition(this, playerData);
        }

        public float GetConditionProgress(PlayerData playerData)
        {
            return GameConditionProcessor.GetConditionProgress(this, playerData);
        }

        /// <summary>
        /// 获取条件的显示文本
        /// </summary>
        public string GetDisplayText()
        {
            return GameConditionProcessor.GetConditionDisplayText(this);
        }
    }
}
