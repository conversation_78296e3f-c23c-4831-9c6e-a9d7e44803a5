using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 元游戏数据加载器 - 从CSV文件加载所有元游戏相关数据
    /// </summary>
    public static class MetaGameDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有元游戏数据
        /// </summary>
        public static bool LoadAllData(Dictionary<string, MetaUpgrade> metaUpgrades, MetaGameConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadMetaUpgrades(metaUpgrades);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("元游戏数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载元游戏数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载元游戏升级配置
        /// </summary>
        private static void LoadMetaUpgrades(Dictionary<string, MetaUpgrade> metaUpgrades)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "MetaUpgrades.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"元游戏升级配置文件不存在: {filePath}");
                LoadDefaultMetaUpgrades(metaUpgrades);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var metaUpgrade = new MetaUpgrade
                    {
                        id = values[0],
                        name = values[1],
                        description = values[2],
                        category = ParseEnum<MetaUpgradeCategory>(values[3]),
                        baseCost = float.Parse(values[4]),
                        maxLevel = int.Parse(values[5]),
                        effectPerLevel = float.Parse(values[6])
                    };

                    // 解析可选字段
                    if (values.Length > 7 && !string.IsNullOrEmpty(values[7]))
                        metaUpgrade.unlockCondition = values[7];
                    if (values.Length > 8 && !string.IsNullOrEmpty(values[8]))
                        metaUpgrade.costMultiplier = float.Parse(values[8]);

                    metaUpgrades[metaUpgrade.id] = metaUpgrade;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析元游戏升级配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载元游戏系统配置
        /// </summary>
        private static void LoadConfig(MetaGameConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "MetaGameConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"元游戏系统配置文件不存在: {filePath}");
                var defaultConfig = MetaGameConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "baseMetaCurrencyRate":
                            config.baseMetaCurrencyRate = float.Parse(value);
                            break;
                        case "maxMetaCurrencyPerRun":
                            config.maxMetaCurrencyPerRun = float.Parse(value);
                            break;
                        case "metaCurrencyDecayRate":
                            config.metaCurrencyDecayRate = float.Parse(value);
                            break;
                        case "baseUpgradeCost":
                            config.baseUpgradeCost = float.Parse(value);
                            break;
                        case "upgradeCostMultiplier":
                            config.upgradeCostMultiplier = float.Parse(value);
                            break;
                        case "maxUpgradeLevel":
                            config.maxUpgradeLevel = int.Parse(value);
                            break;
                        case "completionBonusRate":
                            config.completionBonusRate = float.Parse(value);
                            break;
                        case "achievementBonusRate":
                            config.achievementBonusRate = float.Parse(value);
                            break;
                        case "survivalBonusRate":
                            config.survivalBonusRate = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析元游戏系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载默认元游戏升级
        /// </summary>
        private static void LoadDefaultMetaUpgrades(Dictionary<string, MetaUpgrade> metaUpgrades)
        {
            // 起始资源提升
            metaUpgrades["starting_credits"] = new MetaUpgrade
            {
                id = "starting_credits",
                name = "起始资金",
                description = "增加每次游戏开始时的初始信用点",
                category = MetaUpgradeCategory.StartingBonus,
                baseCost = 100f,
                maxLevel = 10,
                effectPerLevel = 50f,
                costMultiplier = 1.5f
            };

            // 幸运值提升
            metaUpgrades["luck_boost"] = new MetaUpgrade
            {
                id = "luck_boost",
                name = "幸运加成",
                description = "永久提升幸运值",
                category = MetaUpgradeCategory.AttributeBonus,
                baseCost = 200f,
                maxLevel = 5,
                effectPerLevel = 5f,
                costMultiplier = 2f
            };

            // 盲盒折扣
            metaUpgrades["box_discount"] = new MetaUpgrade
            {
                id = "box_discount",
                name = "盲盒折扣",
                description = "降低所有盲盒的价格",
                category = MetaUpgradeCategory.GameplayBonus,
                baseCost = 150f,
                maxLevel = 8,
                effectPerLevel = 0.05f,
                costMultiplier = 1.3f
            };
        }

        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(MetaGameConfig source, MetaGameConfig target)
        {
            target.baseMetaCurrencyRate = source.baseMetaCurrencyRate;
            target.maxMetaCurrencyPerRun = source.maxMetaCurrencyPerRun;
            target.metaCurrencyDecayRate = source.metaCurrencyDecayRate;
            target.baseUpgradeCost = source.baseUpgradeCost;
            target.upgradeCostMultiplier = source.upgradeCostMultiplier;
            target.maxUpgradeLevel = source.maxUpgradeLevel;
            target.completionBonusRate = source.completionBonusRate;
            target.achievementBonusRate = source.achievementBonusRate;
            target.survivalBonusRate = source.survivalBonusRate;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }

    /// <summary>
    /// 元游戏升级类别
    /// </summary>
    public enum MetaUpgradeCategory
    {
        StartingBonus,      // 起始奖励
        AttributeBonus,     // 属性奖励
        GameplayBonus,      // 游戏玩法奖励
        SpecialAbility      // 特殊能力
    }

    /// <summary>
    /// 元游戏升级配置
    /// </summary>
    [Serializable]
    public class MetaUpgrade
    {
        public string id;
        public string name;
        public string description;
        public MetaUpgradeCategory category;
        public float baseCost;
        public int maxLevel;
        public float effectPerLevel;
        public string unlockCondition;
        public float costMultiplier = 1.5f;
    }
}
