using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 元游戏数据加载器 - 从CSV文件加载所有元游戏相关数据
    /// </summary>
    public static class MetaGameDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有元游戏数据
        /// </summary>
        public static bool LoadAllData(Dictionary<string, MetaFeature> metaFeatures, MetaGameConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadMetaFeatures(metaFeatures);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("元游戏数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载元游戏数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载元游戏升级配置
        /// </summary>
        private static void LoadMetaFeatures(Dictionary<string, MetaFeature> metaFeatures)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "MetaFeatureConfigs.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"元游戏升级配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var metaFeature = new MetaFeature
                    {
                        id = values[0],
                        name = values[1],
                        description = values[2],
                        baseCost = float.Parse(values[3]),
                        maxLevel = int.Parse(values[4]),
                        effectPerLevel = float.Parse(values[5]),
                        costMultiplier = float.Parse(values[6])
                    };

                    // 解析可选字段
                    if (values.Length > 7 && !string.IsNullOrEmpty(values[7]))
                        metaFeature.unlockConditions = CVSUtils.ParseContions(values[7]);

                    metaFeatures[metaFeature.id] = metaFeature;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析元游戏升级配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载元游戏系统配置
        /// </summary>
        private static void LoadConfig(MetaGameConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "MetaGameConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"元游戏系统配置文件不存在: {filePath}");
                var defaultConfig = MetaGameConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "baseMetaCurrencyRate":
                            config.baseMetaCurrencyRate = float.Parse(value);
                            break;
                        case "maxMetaCurrencyPerRun":
                            config.maxMetaCurrencyPerRun = float.Parse(value);
                            break;
                        case "metaCurrencyDecayRate":
                            config.metaCurrencyDecayRate = float.Parse(value);
                            break;
                        case "baseUpgradeCost":
                            config.baseUpgradeCost = float.Parse(value);
                            break;
                        case "upgradeCostMultiplier":
                            config.upgradeCostMultiplier = float.Parse(value);
                            break;
                        case "maxUpgradeLevel":
                            config.maxUpgradeLevel = int.Parse(value);
                            break;
                        case "completionBonusRate":
                            config.completionBonusRate = float.Parse(value);
                            break;
                        case "achievementBonusRate":
                            config.achievementBonusRate = float.Parse(value);
                            break;
                        case "survivalBonusRate":
                            config.survivalBonusRate = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析元游戏系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }


        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(MetaGameConfig source, MetaGameConfig target)
        {
            target.baseMetaCurrencyRate = source.baseMetaCurrencyRate;
            target.maxMetaCurrencyPerRun = source.maxMetaCurrencyPerRun;
            target.metaCurrencyDecayRate = source.metaCurrencyDecayRate;
            target.baseUpgradeCost = source.baseUpgradeCost;
            target.upgradeCostMultiplier = source.upgradeCostMultiplier;
            target.maxUpgradeLevel = source.maxUpgradeLevel;
            target.completionBonusRate = source.completionBonusRate;
            target.achievementBonusRate = source.achievementBonusRate;
            target.survivalBonusRate = source.survivalBonusRate;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }


}
