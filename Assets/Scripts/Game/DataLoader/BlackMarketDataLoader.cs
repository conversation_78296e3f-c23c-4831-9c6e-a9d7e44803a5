using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 黑市数据加载器 - 从CSV文件加载所有黑市相关数据
    /// </summary>
    public static class BlackMarketDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有黑市数据
        /// </summary>
        public static bool LoadAllData(Dictionary<string, BlackMarketVendor> vendors, 
                                      Dictionary<string, BlackMarketListing> listings, 
                                      BlackMarketConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadVendors(vendors);
                LoadListings(listings);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("黑市数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载黑市数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载商贩配置
        /// </summary>
        private static void LoadVendors(Dictionary<string, BlackMarketVendor> vendors)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlackMarketVendors.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"黑市商贩配置文件不存在: {filePath}");
                LoadDefaultVendors(vendors);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 5) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var vendor = new BlackMarketVendor
                    {
                        id = values[0],
                        name = values[1],
                        description = values[2],
                        trustLevel = float.Parse(values[3]),
                        specialties = ParseSpecialties(values[4])
                    };

                    vendors[vendor.id] = vendor;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析黑市商贩失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载交易列表
        /// </summary>
        private static void LoadListings(Dictionary<string, BlackMarketListing> listings)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlackMarketListings.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"黑市交易列表配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 8) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var listing = new BlackMarketListing
                    {
                        id = values[0],
                        vendorId = values[1],
                        item = CreateItemFromData(values[2], values[3], values[4]),
                        price = float.Parse(values[5]),
                        baseRisk = float.Parse(values[6]),
                        minimumSocialClass = ParseEnum<SocialClass>(values[7])
                    };

                    // 解析可选字段
                    if (values.Length > 8 && !string.IsNullOrEmpty(values[8]))
                        listing.minimumReputation = float.Parse(values[8]);
                    if (values.Length > 9 && !string.IsNullOrEmpty(values[9]))
                        listing.reputationReward = float.Parse(values[9]);

                    listings[listing.id] = listing;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析黑市交易列表失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载黑市配置
        /// </summary>
        private static void LoadConfig(BlackMarketConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlackMarketConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"黑市配置文件不存在: {filePath}");
                var defaultConfig = BlackMarketConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "baseRiskMultiplier":
                            config.baseRiskMultiplier = float.Parse(value);
                            break;
                        case "suspicionDecayRate":
                            config.suspicionDecayRate = float.Parse(value);
                            break;
                        case "maxSuspicionLevel":
                            config.maxSuspicionLevel = float.Parse(value);
                            break;
                        case "minimumCognition":
                            config.minimumCognition = float.Parse(value);
                            break;
                        case "requireSpecialFlag":
                            config.requireSpecialFlag = bool.Parse(value);
                            break;
                        case "accessFlagName":
                            config.accessFlagName = value;
                            break;
                        case "priceFluctuationRange":
                            config.priceFluctuationRange = float.Parse(value);
                            break;
                        case "maxDailyTransactions":
                            config.maxDailyTransactions = int.Parse(value);
                            break;
                        case "reputationDecayRate":
                            config.reputationDecayRate = float.Parse(value);
                            break;
                        case "detectionThreshold":
                            config.detectionThreshold = float.Parse(value);
                            break;
                        case "raidProbability":
                            config.raidProbability = float.Parse(value);
                            break;
                        case "cooldownHours":
                            config.cooldownHours = int.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析黑市配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载默认商贩
        /// </summary>
        private static void LoadDefaultVendors(Dictionary<string, BlackMarketVendor> vendors)
        {
            vendors["shadow_dealer"] = new BlackMarketVendor
            {
                id = "shadow_dealer",
                name = "影子商人",
                description = "神秘的商人，专门交易禁忌物品",
                trustLevel = 0.7f,
                specialties = new List<BlindBoxContentType> 
                { 
                    BlindBoxContentType.Technology, 
                    BlindBoxContentType.Memory 
                }
            };

            vendors["data_broker"] = new BlackMarketVendor
            {
                id = "data_broker",
                name = "数据掮客",
                description = "买卖各种机密信息",
                trustLevel = 0.5f,
                specialties = new List<BlindBoxContentType> 
                { 
                    BlindBoxContentType.Technology,
                    BlindBoxContentType.Talent 
                }
            };
        }


        /// <summary>
        /// 解析专长列表
        /// </summary>
        private static List<BlindBoxContentType> ParseSpecialties(string specialtiesStr)
        {
            var specialties = new List<BlindBoxContentType>();
            if (string.IsNullOrEmpty(specialtiesStr)) return specialties;

            var parts = specialtiesStr.Split(';');
            foreach (var part in parts)
            {
                if (Enum.TryParse<BlindBoxContentType>(part.Trim(), true, out var specialty))
                {
                    specialties.Add(specialty);
                }
            }
            return specialties;
        }

        /// <summary>
        /// 从数据创建物品
        /// </summary>
        private static BlindBoxItem CreateItemFromData(string id, string name, string description)
        {
            return CreateSpecialItem(id, name, description);
        }

        /// <summary>
        /// 创建特殊物品
        /// </summary>
        private static BlindBoxItem CreateSpecialItem(string id, string name, string description)
        {
            return new BlindBoxItem(id, name, BlindBoxContentType.Technology, 0f, false)
            {
                description = description
            };
        }

        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(BlackMarketConfig source, BlackMarketConfig target)
        {
            target.baseRiskMultiplier = source.baseRiskMultiplier;
            target.suspicionDecayRate = source.suspicionDecayRate;
            target.maxSuspicionLevel = source.maxSuspicionLevel;
            target.minimumCognition = source.minimumCognition;
            target.requireSpecialFlag = source.requireSpecialFlag;
            target.accessFlagName = source.accessFlagName;
            target.priceFluctuationRange = source.priceFluctuationRange;
            target.maxDailyTransactions = source.maxDailyTransactions;
            target.reputationDecayRate = source.reputationDecayRate;
            target.detectionThreshold = source.detectionThreshold;
            target.raidProbability = source.raidProbability;
            target.cooldownHours = source.cooldownHours;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }
}
