using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 特权数据加载器 - 从CSV文件加载所有特权相关数据
    /// </summary>
    public static class PrivilegeDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有特权数据
        /// </summary>
        public static bool LoadAllData(Dictionary<string, PrivilegeConfig> privileges, PrivilegeSystemConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadPrivileges(privileges);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("特权数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载特权数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载特权配置
        /// </summary>
        private static void LoadPrivileges(Dictionary<string, PrivilegeConfig> privileges)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "PrivilegeConfigs.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"特权配置文件不存在: {filePath}");
                LoadDefaultPrivileges(privileges);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 6) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var privilege = new PrivilegeConfig
                    {
                        id = values[0],
                        name = values[1],
                        description = values[2],
                        type = ParseEnum<PrivilegeType>(values[3]),
                        requiredSocialClass = ParseEnum<SocialClass>(values[4]),
                        effectValue = float.Parse(values[5])
                    };

                    // 解析可选字段
                    if (values.Length > 6 && !string.IsNullOrEmpty(values[6]))
                        privilege.duration = float.Parse(values[6]);
                    if (values.Length > 7 && !string.IsNullOrEmpty(values[7]))
                        privilege.cost = float.Parse(values[7]);
                    if (values.Length > 8 && !string.IsNullOrEmpty(values[8]))
                        privilege.conditions = ParseConditions(values[8]);

                    privileges[privilege.id] = privilege;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析特权配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载特权系统配置
        /// </summary>
        private static void LoadConfig(PrivilegeSystemConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "PrivilegeSystemConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"特权系统配置文件不存在: {filePath}");
                var defaultConfig = PrivilegeSystemConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "basePrivilegeDecayRate":
                            config.basePrivilegeDecayRate = float.Parse(value);
                            break;
                        case "maxPrivilegeLevel":
                            config.maxPrivilegeLevel = float.Parse(value);
                            break;
                        case "privilegeGainMultiplier":
                            config.privilegeGainMultiplier = float.Parse(value);
                            break;
                        case "parasitePrivilegeMultiplier":
                            config.parasitePrivilegeMultiplier = float.Parse(value);
                            break;
                        case "workerPrivilegeMultiplier":
                            config.workerPrivilegeMultiplier = float.Parse(value);
                            break;
                        case "chosenPrivilegeMultiplier":
                            config.chosenPrivilegeMultiplier = float.Parse(value);
                            break;
                        case "resourceBonusRate":
                            config.resourceBonusRate = float.Parse(value);
                            break;
                        case "accessBonusRate":
                            config.accessBonusRate = float.Parse(value);
                            break;
                        case "protectionBonusRate":
                            config.protectionBonusRate = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析特权系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载默认特权
        /// </summary>
        private static void LoadDefaultPrivileges(Dictionary<string, PrivilegeConfig> privileges)
        {
            // 资源加成特权
            privileges["resource_bonus"] = new PrivilegeConfig
            {
                id = "resource_bonus",
                name = "资源加成",
                description = "获得额外的资源收益",
                type = PrivilegeType.ResourceBonus,
                requiredSocialClass = SocialClass.Worker,
                effectValue = 0.2f,
                duration = 3600f,
                cost = 100f
            };

            // 系统保护特权
            privileges["system_protection"] = new PrivilegeConfig
            {
                id = "system_protection",
                name = "系统保护",
                description = "减少系统惩罚的影响",
                type = PrivilegeType.Protection,
                requiredSocialClass = SocialClass.Chosen,
                effectValue = 0.5f,
                duration = 7200f,
                cost = 500f
            };

            // 特殊访问权限
            privileges["special_access"] = new PrivilegeConfig
            {
                id = "special_access",
                name = "特殊访问",
                description = "获得特殊区域的访问权限",
                type = PrivilegeType.Access,
                requiredSocialClass = SocialClass.Chosen,
                effectValue = 1f,
                duration = -1f, // 永久
                cost = 1000f
            };
        }

        /// <summary>
        /// 解析条件
        /// </summary>
        private static List<string> ParseConditions(string conditionsStr)
        {
            var conditions = new List<string>();
            if (string.IsNullOrEmpty(conditionsStr)) return conditions;

            var parts = conditionsStr.Split(';');
            foreach (var part in parts)
            {
                if (!string.IsNullOrWhiteSpace(part))
                {
                    conditions.Add(part.Trim());
                }
            }
            return conditions;
        }

        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(PrivilegeSystemConfig source, PrivilegeSystemConfig target)
        {
            target.basePrivilegeDecayRate = source.basePrivilegeDecayRate;
            target.maxPrivilegeLevel = source.maxPrivilegeLevel;
            target.privilegeGainMultiplier = source.privilegeGainMultiplier;
            target.parasitePrivilegeMultiplier = source.parasitePrivilegeMultiplier;
            target.workerPrivilegeMultiplier = source.workerPrivilegeMultiplier;
            target.chosenPrivilegeMultiplier = source.chosenPrivilegeMultiplier;
            target.resourceBonusRate = source.resourceBonusRate;
            target.accessBonusRate = source.accessBonusRate;
            target.protectionBonusRate = source.protectionBonusRate;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }

    /// <summary>
    /// 特权类型
    /// </summary>
    public enum PrivilegeType
    {
        ResourceBonus,      // 资源奖励
        Protection,         // 保护
        Access,            // 访问权限
        Immunity,          // 免疫
        Enhancement        // 增强
    }

    /// <summary>
    /// 特权配置
    /// </summary>
    [Serializable]
    public class PrivilegeConfig
    {
        public string id;
        public string name;
        public string description;
        public PrivilegeType type;
        public SocialClass requiredSocialClass;
        public float effectValue;
        public float duration = -1f; // -1表示永久
        public float cost;
        public List<string> conditions;
    }
}
