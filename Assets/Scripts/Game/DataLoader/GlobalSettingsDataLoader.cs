using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒数据加载器 - 从CSV文件加载所有盲盒相关数据
    /// </summary>
    public static class GlobalSettingsDataLoader
    {
        private static GlobalSettings globalSettings;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有盲盒数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            try
            {
                LoadGlobalSettings();

                isLoaded = true;
                Debug.Log("全局数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载盲盒数据失败: {e.Message}");
            }
        }

        /// <summary>
        /// 获取全局设置
        /// </summary>
        public static GlobalSettings GetGlobalSettings()
        {
            if (!isLoaded) LoadAllData();
            return globalSettings;
        }

        #region 私有加载方法


        /// <summary>
        /// 加载全局设置
        /// </summary>
        private static void LoadGlobalSettings()
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlindBoxGlobalSettings.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"全局设置文件不存在: {filePath}");
                globalSettings = new GlobalSettings(); // 使用默认值
                return;
            }

            globalSettings = new GlobalSettings();
            var lines = File.ReadAllLines(filePath);
            
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "maxHistoryRecords":
                            globalSettings.maxHistoryRecords = int.Parse(value);
                            break;
                        case "enableDetailedLogging":
                            globalSettings.enableDetailedLogging = bool.Parse(value);
                            break;
                        case "showRealProbabilities":
                            globalSettings.showRealProbabilities = bool.Parse(value);
                            break;
                        case "enableProbabilityHints":
                            globalSettings.enableProbabilityHints = bool.Parse(value);
                            break;
                        case "openAnimationDuration":
                            globalSettings.openAnimationDuration = float.Parse(value);
                            break;
                        case "enableParticleEffects":
                            globalSettings.enableParticleEffects = bool.Parse(value);
                            break;
                        case "enableSoundEffects":
                            globalSettings.enableSoundEffects = bool.Parse(value);
                            break;
                        case "soundVolume":
                            globalSettings.soundVolume = float.Parse(value);
                            break;
                        case "enableDebugMode":
                            globalSettings.enableDebugMode = bool.Parse(value);
                            break;
                        case "forceShowAllProbabilities":
                            globalSettings.forceShowAllProbabilities = bool.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析全局设置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out var result))
            {
                return result;
            }
            throw new ArgumentException($"无法解析枚举值: {value} -> {typeof(T).Name}");
        }


        #endregion
    }

}
