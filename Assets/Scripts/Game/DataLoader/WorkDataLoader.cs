using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 工作数据加载器 - 从CSV文件加载所有工作相关数据
    /// </summary>
    public static class WorkDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有工作数据
        /// </summary>
        public static bool LoadAllData(Dictionary<JobType, JobConfig> jobConfigs, WorkSystemConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadJobConfigs(jobConfigs);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("工作数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载工作数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载工作配置
        /// </summary>
        private static void LoadJobConfigs(Dictionary<JobType, JobConfig> jobConfigs)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "JobConfigs.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"工作配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 8) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var jobConfig = new JobConfig
                    {
                        jobType = ParseEnum<JobType>(values[0]),
                        name = values[1],
                        description = values[2],
                        baseHourlyPay = float.Parse(values[3]),
                        requiredSocialClass = ParseEnum<SocialClass>(values[4]),
                        requirements = ParseRequiredAttributes(values[5]),
                        energyCostPerHour = float.Parse(values[6]),
                        socialScoreReward = float.Parse(values[7])
                    };

                    jobConfigs[jobConfig.jobType] = jobConfig;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析工作配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载工作系统配置
        /// </summary>
        private static void LoadConfig(WorkSystemConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "WorkSystemConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"工作系统配置文件不存在: {filePath}");
                var defaultConfig = WorkSystemConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "baseWorkHours":
                            config.baseWorkHours = float.Parse(value);
                            break;
                        case "maxWorkHours":
                            config.maxWorkHours = float.Parse(value);
                            break;
                        case "minEnergyRequired":
                            config.minEnergyRequired = float.Parse(value);
                            break;
                        case "baseEfficiency":
                            config.baseEfficiency = float.Parse(value);
                            break;
                        case "maxEfficiency":
                            config.maxEfficiency = float.Parse(value);
                            break;
                        case "fatigueThreshold":
                            config.fatigueThreshold = float.Parse(value);
                            break;
                        case "baseSalaryMultiplier":
                            config.baseSalaryMultiplier = float.Parse(value);
                            break;
                        case "overtimeMultiplier":
                            config.overtimeMultiplier = float.Parse(value);
                            break;
                        case "performanceBonusRate":
                            config.performanceBonusRate = float.Parse(value);
                            break;
                        case "accidentProbability":
                            config.accidentProbability = float.Parse(value);
                            break;
                        case "stressProbability":
                            config.stressProbability = float.Parse(value);
                            break;
                        case "burnoutThreshold":
                            config.burnoutThreshold = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析工作系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 解析必需属性
        /// </summary>
        private static List<GameCondition> ParseRequiredAttributes(string attributesStr)
        {
            List<GameCondition> attributes = new List<GameCondition>();
            if (string.IsNullOrEmpty(attributesStr)) return attributes;

            var parts = attributesStr.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length == 2)
                {
                    if (Enum.TryParse<AttributeType>(keyValue[0], true, out var attrType))
                    {
                        if (float.TryParse(keyValue[1], out var value))
                        {
                            attributes.Add(new GameCondition(GameConditionType.AttributeThreshold, attrType, value));
                        }
                    }
                }
            }
            return attributes;
        }

        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(WorkSystemConfig source, WorkSystemConfig target)
        {
            target.baseWorkHours = source.baseWorkHours;
            target.maxWorkHours = source.maxWorkHours;
            target.minEnergyRequired = source.minEnergyRequired;
            target.baseEfficiency = source.baseEfficiency;
            target.maxEfficiency = source.maxEfficiency;
            target.fatigueThreshold = source.fatigueThreshold;
            target.baseSalaryMultiplier = source.baseSalaryMultiplier;
            target.overtimeMultiplier = source.overtimeMultiplier;
            target.performanceBonusRate = source.performanceBonusRate;
            target.accidentProbability = source.accidentProbability;
            target.stressProbability = source.stressProbability;
            target.burnoutThreshold = source.burnoutThreshold;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }
}
