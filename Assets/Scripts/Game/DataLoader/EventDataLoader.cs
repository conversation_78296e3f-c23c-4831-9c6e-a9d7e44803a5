using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 事件数据加载器 - 从CSV文件加载所有事件相关数据
    /// </summary>
    public static class EventDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有事件数据
        /// </summary>
        public static bool LoadAllData(Dictionary<string, GameEvent> events, EventSystemConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadEvents(events);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("事件数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载事件数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载事件配置
        /// </summary>
        private static void LoadEvents(Dictionary<string, GameEvent> events)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "GameEvents.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"事件配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var gameEvent = new GameEvent
                    {
                        id = values[0],
                        title = values[1],
                        description = values[2],
                        type = ParseEnum<EventType>(values[3]),
                        baseWeight = float.Parse(values[4]),
                        cooldownHours = float.Parse(values[5]),
                        storyStage = values[6]
                    };

                    // 解析可选字段
                    if (values.Length > 7 && !string.IsNullOrEmpty(values[7]))
                        gameEvent.triggerConditions = ParseConditions(values[7]);
                    if (values.Length > 8 && !string.IsNullOrEmpty(values[8]))
                        gameEvent.immediateEffects = ParseEffects(values[8]);
                    if (values.Length > 9 && !string.IsNullOrEmpty(values[9]))
                        gameEvent.choices = ParseChoices(values[9]);

                    events[gameEvent.id] = gameEvent;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析事件配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载事件系统配置
        /// </summary>
        private static void LoadConfig(EventSystemConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "EventSystemConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"事件系统配置文件不存在: {filePath}");
                var defaultConfig = EventSystemConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "baseEventInterval":
                            config.baseEventInterval = float.Parse(value);
                            break;
                        case "minEventInterval":
                            config.minEventInterval = float.Parse(value);
                            break;
                        case "maxEventInterval":
                            config.maxEventInterval = float.Parse(value);
                            break;
                        case "baseEventProbability":
                            config.baseEventProbability = float.Parse(value);
                            break;
                        case "storyEventProbability":
                            config.storyEventProbability = float.Parse(value);
                            break;
                        case "randomEventProbability":
                            config.randomEventProbability = float.Parse(value);
                            break;
                        case "positiveEventWeight":
                            config.positiveEventWeight = float.Parse(value);
                            break;
                        case "neutralEventWeight":
                            config.neutralEventWeight = float.Parse(value);
                            break;
                        case "negativeEventWeight":
                            config.negativeEventWeight = float.Parse(value);
                            break;
                        case "enableConditionCheck":
                            config.enableConditionCheck = bool.Parse(value);
                            break;
                        case "enableCooldownCheck":
                            config.enableCooldownCheck = bool.Parse(value);
                            break;
                        case "globalCooldown":
                            config.globalCooldown = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析事件系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 解析条件
        /// </summary>
        private static List<GameCondition> ParseConditions(string conditionsStr)
        {
            var conditions = new List<GameCondition>();
            if (string.IsNullOrEmpty(conditionsStr)) return conditions;

            var parts = conditionsStr.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length >= 2)
                {
                    var conditionType = keyValue[0];
                    var value = keyValue[1];
                    
                    conditions.Add(new GameCondition(ParseConditionType(conditionType), float.Parse(value)));
                }
            }
            return conditions;
        }

        /// <summary>
        /// 解析选择
        /// </summary>
        private static List<EventChoice> ParseChoices(string choicesStr)
        {
            var choices = new List<EventChoice>();
            if (string.IsNullOrEmpty(choicesStr)) return choices;

            var parts = choicesStr.Split(';');
            foreach (var part in parts)
            {
                var choiceParts = part.Split('|');
                if (choiceParts.Length >= 2)
                {
                    var choice = new EventChoice
                    {
                        id = choiceParts[0],
                        text = choiceParts[1]
                    };
                    
                    if (choiceParts.Length > 2)
                    {
                        choice.effects = ParseEffects(choiceParts[2]);
                    }
                    
                    choices.Add(choice);
                }
            }
            return choices;
        }

        /// <summary>
        /// 解析效果
        /// </summary>
        private static List<GameEffect> ParseEffects(string effectsStr)
        {
            var effects = new List<GameEffect>();
            if (string.IsNullOrEmpty(effectsStr)) return effects;

            var parts = effectsStr.Split(',');
            foreach (var part in parts)
            {
                var effectParts = part.Split(':');
                if (effectParts.Length >= 3)
                {
                    var effectType = effectParts[0];
                    var target = effectParts[1];
                    var value = effectParts[2];
                    
                    effects.Add(new GameEffect(ParseEffectType(effectType), target, float.Parse(value)));
                }
            }
            return effects;
        }

        /// <summary>
        /// 解析条件类型
        /// </summary>
        private static GameConditionType ParseConditionType(string conditionType)
        {
            switch (conditionType)
            {
                case "DaysSurvived": return GameConditionType.DaysSurvived;
                case "AttributeValue": return GameConditionType.AttributeThreshold;
                case "ResourceValue": return GameConditionType.ResourceThreshold;
                case "SocialClass": return GameConditionType.SocialClass;
                default: return GameConditionType.DaysSurvived;
            }
        }

        /// <summary>
        /// 解析效果类型
        /// </summary>
        private static GameEffectType ParseEffectType(string effectType)
        {
            switch (effectType)
            {
                case "Resource": return GameEffectType.ResourceChange;
                case "Attribute": return GameEffectType.AttributeChange;
                case "Flag": return GameEffectType.SetFlag;
                case "Event": return GameEffectType.TriggerEvent;
                default: return GameEffectType.ResourceChange;
            }
        }

        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(EventSystemConfig source, EventSystemConfig target)
        {
            target.baseEventInterval = source.baseEventInterval;
            target.minEventInterval = source.minEventInterval;
            target.maxEventInterval = source.maxEventInterval;
            target.baseEventProbability = source.baseEventProbability;
            target.storyEventProbability = source.storyEventProbability;
            target.randomEventProbability = source.randomEventProbability;
            target.positiveEventWeight = source.positiveEventWeight;
            target.neutralEventWeight = source.neutralEventWeight;
            target.negativeEventWeight = source.negativeEventWeight;
            target.enableConditionCheck = source.enableConditionCheck;
            target.enableCooldownCheck = source.enableCooldownCheck;
            target.globalCooldown = source.globalCooldown;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }
}
