using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 成就数据加载器 - 从CSV文件加载所有成就相关数据
    /// </summary>
    public static class AchievementDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有成就数据
        /// </summary>
        public static bool LoadAllData(Dictionary<string, Achievement> achievements, Dictionary<string, ProgressTracker> progressTrackers)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadAchievements(achievements);
                LoadProgressTrackers(progressTrackers);

                isLoaded = true;
                Debug.Log("成就数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载成就数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载成就配置
        /// </summary>
        private static void LoadAchievements(Dictionary<string, Achievement> achievements)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "Achievements.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"成就配置文件不存在: {filePath}");
                LoadDefaultAchievements(achievements);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 9) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var achievement = new Achievement
                    {
                        id = values[0],
                        name = values[1],
                        description = values[2],
                        category = ParseEnum<AchievementCategory>(values[3]),
                        rarity = ParseEnum<AchievementRarity>(values[4]),
                        isHidden = bool.Parse(values[5]),
                        iconPath = values[6],
                        isUnlocked = false // 默认未解锁
                    };

                    // 解析条件
                    if (!string.IsNullOrEmpty(values[7]))
                    {
                        achievement.conditions = ParseConditions(values[7]);
                    }

                    // 解析奖励
                    if (!string.IsNullOrEmpty(values[8]))
                    {
                        achievement.rewards = ParseRewards(values[8]);
                    }

                    achievements[achievement.id] = achievement;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析成就失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载进度追踪器
        /// </summary>
        private static void LoadProgressTrackers(Dictionary<string, ProgressTracker> progressTrackers)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "ProgressTrackers.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"进度追踪器配置文件不存在: {filePath}");
                LoadDefaultProgressTrackers(progressTrackers);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 4) continue;

                try
                {
                    var tracker = new ProgressTracker
                    {
                        id = values[0],
                        name = values[1],
                        description = values[2],
                        currentValue = float.Parse(values[3])
                    };

                    progressTrackers[tracker.id] = tracker;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析进度追踪器失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载默认成就
        /// </summary>
        private static void LoadDefaultAchievements(Dictionary<string, Achievement> achievements)
        {
            // 第一周生存
            achievements["first_week_survivor"] = new Achievement
            {
                id = "first_week_survivor",
                name = "初来乍到",
                description = "在这个世界生存7天",
                rarity = AchievementRarity.Common,
                isUnlocked = false
            };

            // 第一个月生存
            achievements["first_month_survivor"] = new Achievement
            {
                id = "first_month_survivor",
                name = "月度幸存者",
                description = "在这个世界生存30天",
                rarity = AchievementRarity.Uncommon,
                isUnlocked = false
            };

            // 第一次开盒
            achievements["first_box_opener"] = new Achievement
            {
                id = "first_box_opener",
                name = "初次尝试",
                description = "开启第一个盲盒",
                rarity = AchievementRarity.Common,
                isUnlocked = false
            };

            // 盲盒大师
            achievements["box_master"] = new Achievement
            {
                id = "box_master",
                name = "盲盒大师",
                description = "开启100个盲盒",
                rarity = AchievementRarity.Rare,
                isUnlocked = false
            };

            // 觉醒者
            achievements["awakened_one"] = new Achievement
            {
                id = "awakened_one",
                name = "觉醒者",
                description = "发现系统的真相",
                rarity = AchievementRarity.Epic,
                isUnlocked = false
            };
        }

        /// <summary>
        /// 加载默认进度追踪器
        /// </summary>
        private static void LoadDefaultProgressTrackers(Dictionary<string, ProgressTracker> progressTrackers)
        {
            // 生存天数追踪器
            progressTrackers["days_survived"] = new ProgressTracker
            {
                id = "days_survived",
                name = "生存天数",
                description = "记录玩家生存的天数",
                currentValue = 0f
            };

            // 开盒次数追踪器
            progressTrackers["boxes_opened"] = new ProgressTracker
            {
                id = "boxes_opened",
                name = "开盒次数",
                description = "记录玩家开启盲盒的次数",
                currentValue = 0f
            };

            // 获得信用点追踪器
            progressTrackers["credits_earned"] = new ProgressTracker
            {
                id = "credits_earned",
                name = "获得信用点",
                description = "记录玩家累计获得的信用点",
                currentValue = 0f
            };
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析条件
        /// </summary>
        private static List<GameCondition> ParseConditions(string conditionsStr)
        {
            var conditions = new List<GameCondition>();
            var conditionParts = conditionsStr.Split(';');

            foreach (var part in conditionParts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length >= 2)
                {
                    var conditionType = keyValue[0];
                    var value = keyValue.Length > 2 ? keyValue[2] : keyValue[1];

                    // 简化的条件解析，可以根据需要扩展
                    conditions.Add(new GameCondition(ParseConditionType(conditionType), float.Parse(value)));
                }
            }

            return conditions;
        }

        /// <summary>
        /// 解析奖励
        /// </summary>
        private static List<GameEffect> ParseRewards(string rewardsStr)
        {
            var rewards = new List<GameEffect>();
            var rewardParts = rewardsStr.Split(';');

            foreach (var part in rewardParts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length >= 2)
                {
                    var rewardType = keyValue[0];
                    var target = keyValue.Length > 2 ? keyValue[1] : null;
                    var value = keyValue.Length > 2 ? keyValue[2] : keyValue[1];

                    // 简化的奖励解析，可以根据需要扩展
                    rewards.Add(new GameEffect(ParseEffectType(rewardType), target, float.Parse(value)));
                }
            }

            return rewards;
        }

        /// <summary>
        /// 解析条件类型
        /// </summary>
        private static GameConditionType ParseConditionType(string conditionType)
        {
            switch (conditionType)
            {
                case "DaysSurvived": return GameConditionType.DaysSurvived;
                case "BoxesOpened": return GameConditionType.BoxesOpened;
                case "SocialClass": return GameConditionType.SocialClass;
                case "AttributeValue": return GameConditionType.AttributeThreshold;
                case "ResourceValue": return GameConditionType.ResourceThreshold;
                default: return GameConditionType.DaysSurvived;
            }
        }

        /// <summary>
        /// 解析效果类型
        /// </summary>
        private static GameEffectType ParseEffectType(string effectType)
        {
            switch (effectType)
            {
                case "Resource": return GameEffectType.ResourceChange;
                case "Attribute": return GameEffectType.AttributeChange;
                case "Title": return GameEffectType.Title;
                case "UnlockEvent": return GameEffectType.UnlockEvent;
                default: return GameEffectType.ResourceChange;
            }
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }
}
