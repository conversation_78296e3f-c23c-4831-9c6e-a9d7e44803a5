using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒数据加载器 - 从CSV文件加载所有盲盒相关数据
    /// </summary>
    public static class BlindBoxDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有盲盒数据
        /// </summary>
        public static bool LoadAllData(Dictionary<BlindBoxType, BlindBoxConfig> boxConfigs, Dictionary<string, BlindBoxItem> itemDatabase, FraudAlgorithmConfig fraudConfig)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadBlindBoxConfigs(boxConfigs);
                LoadBlindBoxItems(itemDatabase);
                LoadItemEffects(itemDatabase);
                LoadLootTables(boxConfigs, itemDatabase);
                LoadFraudConfig(fraudConfig);

                isLoaded = true;
                Debug.Log("盲盒数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载盲盒数据失败: {e.Message}");
                return false;
            }

            return true;
        }


        #region 私有加载方法

        /// <summary>
        /// 加载盲盒配置
        /// </summary>
        private static void LoadBlindBoxConfigs(Dictionary<BlindBoxType, BlindBoxConfig> boxConfigs)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlindBoxConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"盲盒配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 8) continue;

                try
                {
                    var boxType = ParseEnum<BlindBoxType>(values[0]);
                    var config = new BlindBoxConfig
                    {
                        type = boxType,
                        name = values[1],
                        description = values[2],
                        basePrice = float.Parse(values[3]),
                        priceType = ParseEnum<ResourceType>(values[4])
                    };

                    boxConfigs[boxType] = config;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析盲盒配置失败 (行 {i}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载盲盒物品
        /// </summary>
        private static void LoadBlindBoxItems(Dictionary<string, BlindBoxItem> itemDatabase)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlindBoxItems.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"盲盒物品文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 8) continue;

                try
                {
                    var itemId = values[0];
                    var contentType = ParseEnum<BlindBoxContentType>(values[3]);
                    var value = float.Parse(values[4]);
                    var isConsumable = bool.Parse(values[5]);

                    var item = new BlindBoxItem(itemId, values[1], contentType, value, isConsumable)
                    {
                        description = values[2]
                    };

                    itemDatabase[itemId] = item;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析盲盒物品失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载物品效果
        /// </summary>
        private static void LoadItemEffects(Dictionary<string, BlindBoxItem> itemDatabase)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlindBoxItemEffects.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"物品效果文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;

                try
                {
                    var itemId = values[0];
                    if (!itemDatabase.ContainsKey(itemId)) continue;

                    var effect = CreateGameEffect(values);
                    if (effect != null)
                    {
                        itemDatabase[itemId].effects.Add(effect);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析物品效果失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载掉落表
        /// </summary>
        private static void LoadLootTables(Dictionary<BlindBoxType, BlindBoxConfig> boxConfigs, Dictionary<string, BlindBoxItem> itemDatabase)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "BlindBoxLootTable.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"掉落表文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 6) continue;

                try
                {
                    var boxType = ParseEnum<BlindBoxType>(values[0]);
                    if (!boxConfigs.ContainsKey(boxType)) continue;

                    var lootTable = new BlindBoxLootTable
                    {
                        itemId = values[1],
                        baseWeight = float.Parse(values[2]),
                        displayProbability = float.Parse(values[3]),
                        realProbability = float.Parse(values[4])
                    };

                    // 解析条件
                    if (!string.IsNullOrEmpty(values[5]))
                    {
                        // 简单的条件解析，格式: "luck:<50"
                        var conditionParts = values[5].Split(':');
                        if (conditionParts.Length == 2)
                        {
                            // 这里可以添加更复杂的条件解析逻辑
                        }
                    }

                    boxConfigs[boxType].lootTables.Add(lootTable);
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析掉落表失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载欺诈算法配置
        /// </summary>
        private static void LoadFraudConfig(FraudAlgorithmConfig fraudConfig)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "FraudAlgorithmConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"欺诈算法配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var parameter = values[0];
                    var value = float.Parse(values[1]);

                    switch (parameter)
                    {
                        case "baseFraudRate":
                            fraudConfig.baseFraudRate = value;
                            break;
                        case "maxProbabilityDeviation":
                            fraudConfig.maxProbabilityDeviation = value;
                            break;
                        case "dependenceInfluence":
                            fraudConfig.dependenceInfluence = value;
                            break;
                        case "luckInfluence":
                            fraudConfig.luckInfluence = value;
                            break;
                        case "socialClassInfluence":
                            fraudConfig.socialClassInfluence = value;
                            break;
                        case "addictionThreshold":
                            fraudConfig.addictionThreshold = value;
                            break;
                        case "frustrationThreshold":
                            fraudConfig.frustrationThreshold = value;
                            break;
                        case "hopeManipulationRate":
                            fraudConfig.hopeManipulationRate = value;
                            break;
                        case "resourcePoolInfluence":
                            fraudConfig.resourcePoolInfluence = value;
                            break;
                        case "globalBalanceWeight":
                            fraudConfig.globalBalanceWeight = value;
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析欺诈算法配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out var result))
            {
                return result;
            }
            throw new ArgumentException($"无法解析枚举值: {value} -> {typeof(T).Name}");
        }

        /// <summary>
        /// 创建游戏效果
        /// </summary>
        private static GameEffect CreateGameEffect(string[] values)
        {
            var effectType = ParseEnum<GameEffectType>(values[1]);
            var description = values[6];

            switch (effectType)
            {
                case GameEffectType.AttributeChange:
                    var attributeType = ParseEnum<AttributeType>(values[2]);
                    var attributeValue = float.Parse(values[3]);
                    return new GameEffect(effectType, attributeType, attributeValue, description);

                case GameEffectType.ResourceChange:
                    var resourceType = ParseEnum<ResourceType>(values[2]);
                    var resourceValue = float.Parse(values[3]);
                    return new GameEffect(effectType, resourceType, resourceValue, description);

                case GameEffectType.AddStatusEffect:
                case GameEffectType.RemoveStatusEffect:
                    var statusType = ParseEnum<StatusEffectType>(values[2]);
                    var duration = int.Parse(values[4]);
                    return new GameEffect(effectType, statusType, 0f, duration, description);

                case GameEffectType.SpecialAbility:
                case GameEffectType.StatusImmunity:
                    var abilityId = values[5];
                    return new GameEffect(effectType, abilityId, 0f, description);

                default:
                    return null;
            }
        }

        #endregion
    }

}
