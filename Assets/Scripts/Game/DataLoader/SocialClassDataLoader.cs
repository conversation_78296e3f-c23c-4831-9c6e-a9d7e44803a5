using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 社会阶层数据加载器 - 从CSV文件加载所有社会阶层相关数据
    /// </summary>
    public static class SocialClassDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有社会阶层数据
        /// </summary>
        public static bool LoadAllData(Dictionary<SocialClass, SocialClassConfig> socialClasses, SocialClassSystemConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadSocialClasses(socialClasses);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("社会阶层数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载社会阶层数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载社会阶层配置
        /// </summary>
        private static void LoadSocialClasses(Dictionary<SocialClass, SocialClassConfig> socialClasses)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "SocialClassConfigs.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"社会阶层配置文件不存在: {filePath}");
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var socialClass = ParseEnum<SocialClass>(values[0]);
                    var config = new SocialClassConfig
                    {
                        className = values[1],
                        description = values[2],
                        privileges = ParsePrivileges(values[3]),
                    };

                    // 解析可选字段
                    if (values.Length > 4 && !string.IsNullOrEmpty(values[4]))
                        config.promotionRequirements = ParseConditions(values[4]);
                    if (values.Length > 5 && !string.IsNullOrEmpty(values[5]))
                        config.promotionCosts = ParseEffects(values[5]);




                    socialClasses[socialClass] = config;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析社会阶层配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载社会阶层系统配置
        /// </summary>
        private static void LoadConfig(SocialClassSystemConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "SocialClassSystemConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"社会阶层系统配置文件不存在: {filePath}");
                var defaultConfig = SocialClassSystemConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "basePromotionThreshold":
                            config.basePromotionThreshold = float.Parse(value);
                            break;
                        case "baseDemotionThreshold":
                            config.baseDemotionThreshold = float.Parse(value);
                            break;
                        case "socialMobilityRate":
                            config.socialMobilityRate = float.Parse(value);
                            break;
                        case "parasiteResourceMultiplier":
                            config.parasiteResourceMultiplier = float.Parse(value);
                            break;
                        case "workerResourceMultiplier":
                            config.workerResourceMultiplier = float.Parse(value);
                            break;
                        case "chosenResourceMultiplier":
                            config.chosenResourceMultiplier = float.Parse(value);
                            break;
                        case "enableClassWarfare":
                            config.enableClassWarfare = bool.Parse(value);
                            break;
                        case "enableSocialRevolution":
                            config.enableSocialRevolution = bool.Parse(value);
                            break;
                        case "revolutionThreshold":
                            config.revolutionThreshold = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析社会阶层系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }


        /// <summary>
        /// 解析特殊能力
        /// </summary>
        private static List<string> ParsePrivileges(string abilitiesStr)
        {
            var abilities = new List<string>();
            if (string.IsNullOrEmpty(abilitiesStr)) return abilities;

            var parts = abilitiesStr.Split(';');
            foreach (var part in parts)
            {
                if (!string.IsNullOrWhiteSpace(part))
                {
                    abilities.Add(part.Trim());
                }
            }
            return abilities;
        }

        
        /// <summary>
        /// 解析效果类型
        /// </summary>
        private static GameEffectType ParseEffectType(string effectType)
        {
            switch (effectType)
            {
                case "Resource": return GameEffectType.ResourceChange;
                case "Attribute": return GameEffectType.AttributeChange;
                case "Flag": return GameEffectType.SetFlag;
                case "Event": return GameEffectType.TriggerEvent;
                default: return GameEffectType.ResourceChange;
            }
        }
        /// <summary>
        /// 解析必需属性
        /// </summary>
        private static List<GameCondition> ParseConditions(string attributesStr)
        {
            List<GameCondition> attributes = new List<GameCondition>();
            if (string.IsNullOrEmpty(attributesStr)) return attributes;

            var parts = attributesStr.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length == 2)
                {
                    if (Enum.TryParse<AttributeType>(keyValue[0], true, out var attrType))
                    {
                        if (float.TryParse(keyValue[1], out var value))
                        {
                            attributes.Add(new GameCondition(GameConditionType.AttributeThreshold, attrType, value));
                        }
                    }
                }
            }
            return attributes;
        }


        /// <summary>
        /// 解析效果
        /// </summary>
        private static List<GameEffect> ParseEffects(string effectsStr)
        {
            var effects = new List<GameEffect>();
            if (string.IsNullOrEmpty(effectsStr)) return effects;

            var parts = effectsStr.Split(',');
            foreach (var part in parts)
            {
                var effectParts = part.Split(':');
                if (effectParts.Length >= 3)
                {
                    var effectType = effectParts[0];
                    var target = effectParts[1];
                    var value = effectParts[2];
                    
                    effects.Add(new GameEffect(ParseEffectType(effectType), target, float.Parse(value)));
                }
            }
            return effects;
        }


        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(SocialClassSystemConfig source, SocialClassSystemConfig target)
        {
            target.basePromotionThreshold = source.basePromotionThreshold;
            target.baseDemotionThreshold = source.baseDemotionThreshold;
            target.socialMobilityRate = source.socialMobilityRate;
            target.parasiteResourceMultiplier = source.parasiteResourceMultiplier;
            target.workerResourceMultiplier = source.workerResourceMultiplier;
            target.chosenResourceMultiplier = source.chosenResourceMultiplier;
            target.enableClassWarfare = source.enableClassWarfare;
            target.enableSocialRevolution = source.enableSocialRevolution;
            target.revolutionThreshold = source.revolutionThreshold;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }

}
