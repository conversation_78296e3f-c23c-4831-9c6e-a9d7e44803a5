using System;
using System.Collections.Generic;
using System.IO;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 社会阶层数据加载器 - 从CSV文件加载所有社会阶层相关数据
    /// </summary>
    public static class SocialClassDataLoader
    {
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有社会阶层数据
        /// </summary>
        public static bool LoadAllData(Dictionary<SocialClass, SocialClassConfig> socialClasses, SocialClassSystemConfig config)
        {
            if (isLoaded)
                return true;

            try
            {
                LoadSocialClasses(socialClasses);
                LoadConfig(config);

                isLoaded = true;
                Debug.Log("社会阶层数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载社会阶层数据失败: {e.Message}");
                return false;
            }

            return true;
        }

        #region 私有加载方法

        /// <summary>
        /// 加载社会阶层配置
        /// </summary>
        private static void LoadSocialClasses(Dictionary<SocialClass, SocialClassConfig> socialClasses)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "SocialClassConfigs.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"社会阶层配置文件不存在: {filePath}");
                LoadDefaultSocialClasses(socialClasses);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;
                if (string.IsNullOrWhiteSpace(values[0])) continue; // 跳过空行

                try
                {
                    var socialClass = ParseEnum<SocialClass>(values[0]);
                    var config = new SocialClassConfig
                    {
                        socialClass = socialClass,
                        name = values[1],
                        description = values[2],
                        promotionThreshold = float.Parse(values[3]),
                        demotionThreshold = float.Parse(values[4]),
                        resourceMultiplier = float.Parse(values[5]),
                        privilegeLevel = float.Parse(values[6])
                    };

                    // 解析可选字段
                    if (values.Length > 7 && !string.IsNullOrEmpty(values[7]))
                        config.specialAbilities = ParseAbilities(values[7]);
                    if (values.Length > 8 && !string.IsNullOrEmpty(values[8]))
                        config.restrictions = ParseRestrictions(values[8]);

                    socialClasses[socialClass] = config;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析社会阶层配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载社会阶层系统配置
        /// </summary>
        private static void LoadConfig(SocialClassSystemConfig config)
        {
            var filePath = Path.Combine(Application.streamingAssetsPath, "Data", "SocialClassSystemConfig.csv");
            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"社会阶层系统配置文件不存在: {filePath}");
                var defaultConfig = SocialClassSystemConfig.GetDefault();
                CopyConfigValues(defaultConfig, config);
                return;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 2) continue;

                try
                {
                    var setting = values[0];
                    var value = values[1];

                    switch (setting)
                    {
                        case "basePromotionThreshold":
                            config.basePromotionThreshold = float.Parse(value);
                            break;
                        case "baseDemotionThreshold":
                            config.baseDemotionThreshold = float.Parse(value);
                            break;
                        case "socialMobilityRate":
                            config.socialMobilityRate = float.Parse(value);
                            break;
                        case "parasiteResourceMultiplier":
                            config.parasiteResourceMultiplier = float.Parse(value);
                            break;
                        case "workerResourceMultiplier":
                            config.workerResourceMultiplier = float.Parse(value);
                            break;
                        case "chosenResourceMultiplier":
                            config.chosenResourceMultiplier = float.Parse(value);
                            break;
                        case "enableClassWarfare":
                            config.enableClassWarfare = bool.Parse(value);
                            break;
                        case "enableSocialRevolution":
                            config.enableSocialRevolution = bool.Parse(value);
                            break;
                        case "revolutionThreshold":
                            config.revolutionThreshold = float.Parse(value);
                            break;
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析社会阶层系统配置失败 (行 {i + 1}): {e.Message}");
                }
            }
        }

        /// <summary>
        /// 加载默认社会阶层
        /// </summary>
        private static void LoadDefaultSocialClasses(Dictionary<SocialClass, SocialClassConfig> socialClasses)
        {
            // 蛀虫阶层
            socialClasses[SocialClass.Parasite] = new SocialClassConfig
            {
                socialClass = SocialClass.Parasite,
                name = "蛀虫",
                description = "社会底层，依赖系统生存",
                promotionThreshold = 100f,
                demotionThreshold = 0f,
                resourceMultiplier = 0.8f,
                privilegeLevel = 10f,
                specialAbilities = new List<string> { "survival_instinct" },
                restrictions = new List<string> { "limited_access", "low_priority" }
            };

            // 工蜂阶层
            socialClasses[SocialClass.Worker] = new SocialClassConfig
            {
                socialClass = SocialClass.Worker,
                name = "工蜂",
                description = "社会中层，系统的主要劳动力",
                promotionThreshold = 200f,
                demotionThreshold = 50f,
                resourceMultiplier = 1f,
                privilegeLevel = 50f,
                specialAbilities = new List<string> { "work_efficiency", "social_networking" },
                restrictions = new List<string> { "moderate_access" }
            };

            // 神选者阶层
            socialClasses[SocialClass.Chosen] = new SocialClassConfig
            {
                socialClass = SocialClass.Chosen,
                name = "神选者",
                description = "社会顶层，享有最高特权",
                promotionThreshold = float.MaxValue,
                demotionThreshold = 150f,
                resourceMultiplier = 1.5f,
                privilegeLevel = 100f,
                specialAbilities = new List<string> { "system_control", "privilege_access", "immunity" },
                restrictions = new List<string>()
            };
        }

        /// <summary>
        /// 解析特殊能力
        /// </summary>
        private static List<string> ParseAbilities(string abilitiesStr)
        {
            var abilities = new List<string>();
            if (string.IsNullOrEmpty(abilitiesStr)) return abilities;

            var parts = abilitiesStr.Split(';');
            foreach (var part in parts)
            {
                if (!string.IsNullOrWhiteSpace(part))
                {
                    abilities.Add(part.Trim());
                }
            }
            return abilities;
        }

        /// <summary>
        /// 解析限制条件
        /// </summary>
        private static List<string> ParseRestrictions(string restrictionsStr)
        {
            var restrictions = new List<string>();
            if (string.IsNullOrEmpty(restrictionsStr)) return restrictions;

            var parts = restrictionsStr.Split(';');
            foreach (var part in parts)
            {
                if (!string.IsNullOrWhiteSpace(part))
                {
                    restrictions.Add(part.Trim());
                }
            }
            return restrictions;
        }

        /// <summary>
        /// 复制配置值
        /// </summary>
        private static void CopyConfigValues(SocialClassSystemConfig source, SocialClassSystemConfig target)
        {
            target.basePromotionThreshold = source.basePromotionThreshold;
            target.baseDemotionThreshold = source.baseDemotionThreshold;
            target.socialMobilityRate = source.socialMobilityRate;
            target.parasiteResourceMultiplier = source.parasiteResourceMultiplier;
            target.workerResourceMultiplier = source.workerResourceMultiplier;
            target.chosenResourceMultiplier = source.chosenResourceMultiplier;
            target.enableClassWarfare = source.enableClassWarfare;
            target.enableSocialRevolution = source.enableSocialRevolution;
            target.revolutionThreshold = source.revolutionThreshold;
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out T result))
                return result;
            return default(T);
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        #endregion
    }

    /// <summary>
    /// 社会阶层配置
    /// </summary>
    [Serializable]
    public class SocialClassConfig
    {
        public SocialClass socialClass;
        public string name;
        public string description;
        public float promotionThreshold;
        public float demotionThreshold;
        public float resourceMultiplier;
        public float privilegeLevel;
        public List<string> specialAbilities;
        public List<string> restrictions;
    }
}
