using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 天赋数据加载器 - 从CSV文件加载天赋和状态效果数据
    /// </summary>
    public static class TalentDataLoader
    {
        private static Dictionary<TalentType, TalentConfig> talentConfigs;
        private static Dictionary<StatusEffectType, StatusEffectConfig> statusEffectConfigs;
        private static bool isLoaded = false;

        /// <summary>
        /// 加载所有天赋数据
        /// </summary>
        public static void LoadAllData()
        {
            if (isLoaded) return;

            try
            {
                talentConfigs = new Dictionary<TalentType, TalentConfig>();
                statusEffectConfigs = new Dictionary<StatusEffectType, StatusEffectConfig>();

                LoadTalentData();
                LoadStatusEffectData();

                isLoaded = true;
                Debug.Log("天赋数据加载完成");
            }
            catch (Exception e)
            {
                Debug.LogError($"加载天赋数据失败: {e.Message}");
            }
        }

        /// <summary>
        /// 获取天赋配置
        /// </summary>
        public static Dictionary<TalentType, TalentConfig> GetTalentConfigs()
        {
            if (!isLoaded) LoadAllData();
            return talentConfigs;
        }

        /// <summary>
        /// 获取状态效果配置
        /// </summary>
        public static Dictionary<StatusEffectType, StatusEffectConfig> GetStatusEffectConfigs()
        {
            if (!isLoaded) LoadAllData();
            return statusEffectConfigs;
        }

        /// <summary>
        /// 获取特定天赋配置
        /// </summary>
        public static TalentConfig GetTalentConfig(TalentType talentType)
        {
            if (!isLoaded) LoadAllData();
            return talentConfigs.TryGetValue(talentType, out var config) ? config : null;
        }

        /// <summary>
        /// 获取特定状态效果配置
        /// </summary>
        public static StatusEffectConfig GetStatusEffectConfig(StatusEffectType statusEffectType)
        {
            if (!isLoaded) LoadAllData();
            return statusEffectConfigs.TryGetValue(statusEffectType, out var config) ? config : null;
        }

        #region 私有方法

        /// <summary>
        /// 加载天赋数据
        /// </summary>
        private static void LoadTalentData()
        {
            // 加载基础天赋数据
            var talentDataPath = Path.Combine(Application.streamingAssetsPath, "Data", "TalentData.csv");
            var talentData = LoadTalentBasicData(talentDataPath);

            // 加载天赋需求条件
            var requirementsPath = Path.Combine(Application.streamingAssetsPath, "Data", "TalentRequirements.csv");
            var requirements = LoadTalentRequirements(requirementsPath);

            // 加载天赋效果
            var effectsPath = Path.Combine(Application.streamingAssetsPath, "Data", "TalentEffects.csv");
            var effects = LoadTalentEffects(effectsPath);

            // 组合数据
            foreach (var kvp in talentData)
            {
                var talentType = kvp.Key;
                var config = kvp.Value;

                // 添加需求条件
                if (requirements.TryGetValue(talentType, out var talentRequirements))
                {
                    config.requirements = talentRequirements;
                }

                // 添加效果
                if (effects.TryGetValue(talentType, out var talentEffects))
                {
                    config.effects = talentEffects;
                }

                talentConfigs[talentType] = config;
            }
        }

        /// <summary>
        /// 加载天赋基础数据
        /// </summary>
        private static Dictionary<TalentType, TalentConfig> LoadTalentBasicData(string filePath)
        {
            var result = new Dictionary<TalentType, TalentConfig>();

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"天赋数据文件不存在: {filePath}");
                return result;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;

                try
                {
                    var talentType = ParseEnum<TalentType>(values[0]);
                    var config = new TalentConfig
                    {
                        name = values[1],
                        description = values[2],
                        rarity = ParseEnum<TalentRarity>(values[3]),
                        iconPath = values[4],
                        unlockLevel = int.Parse(values[5]),
                        isStartingTalent = bool.Parse(values[6])
                    };

                    result[talentType] = config;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析天赋数据失败 (行 {i + 1}): {e.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 加载天赋需求条件
        /// </summary>
        private static Dictionary<TalentType, List<GameCondition>> LoadTalentRequirements(string filePath)
        {
            var result = new Dictionary<TalentType, List<GameCondition>>();

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"天赋需求文件不存在: {filePath}");
                return result;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 8) continue;

                try
                {
                    var talentType = ParseEnum<TalentType>(values[0]);
                    
                    // 跳过无条件的天赋
                    if (string.IsNullOrEmpty(values[1])) continue;

                    var condition = CreateGameCondition(values);
                    if (condition != null)
                    {
                        if (!result.ContainsKey(talentType))
                        {
                            result[talentType] = new List<GameCondition>();
                        }
                        result[talentType].Add(condition);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析天赋需求失败 (行 {i + 1}): {e.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 加载天赋效果
        /// </summary>
        private static Dictionary<TalentType, List<GameEffect>> LoadTalentEffects(string filePath)
        {
            var result = new Dictionary<TalentType, List<GameEffect>>();

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"天赋效果文件不存在: {filePath}");
                return result;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;

                try
                {
                    var talentType = ParseEnum<TalentType>(values[0]);
                    var effect = CreateGameEffect(values);
                    
                    if (effect != null)
                    {
                        if (!result.ContainsKey(talentType))
                        {
                            result[talentType] = new List<GameEffect>();
                        }
                        result[talentType].Add(effect);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析天赋效果失败 (行 {i + 1}): {e.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 加载状态效果数据
        /// </summary>
        private static void LoadStatusEffectData()
        {
            // 加载基础状态效果数据
            var statusDataPath = Path.Combine(Application.streamingAssetsPath, "Data", "StatusEffectData.csv");
            var statusData = LoadStatusEffectBasicData(statusDataPath);

            // 加载状态效果影响
            var effectsPath = Path.Combine(Application.streamingAssetsPath, "Data", "StatusEffectEffects.csv");
            var effects = LoadStatusEffectEffects(effectsPath);

            // 组合数据
            foreach (var kvp in statusData)
            {
                var statusType = kvp.Key;
                var config = kvp.Value;

                // 添加效果
                if (effects.TryGetValue(statusType, out var statusEffects))
                {
                    config.effects = statusEffects;
                }

                statusEffectConfigs[statusType] = config;
            }
        }

        /// <summary>
        /// 加载状态效果基础数据
        /// </summary>
        private static Dictionary<StatusEffectType, StatusEffectConfig> LoadStatusEffectBasicData(string filePath)
        {
            var result = new Dictionary<StatusEffectType, StatusEffectConfig>();

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"状态效果数据文件不存在: {filePath}");
                return result;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 7) continue;

                try
                {
                    var statusType = ParseEnum<StatusEffectType>(values[0]);
                    var config = new StatusEffectConfig
                    {
                        name = values[1],
                        description = values[2],
                        isPositive = bool.Parse(values[3]),
                        iconPath = values[4],
                        stackable = bool.Parse(values[5]),
                        maxStacks = int.Parse(values[6])
                    };

                    result[statusType] = config;
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析状态效果数据失败 (行 {i + 1}): {e.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 加载状态效果影响
        /// </summary>
        private static Dictionary<StatusEffectType, List<GameEffect>> LoadStatusEffectEffects(string filePath)
        {
            var result = new Dictionary<StatusEffectType, List<GameEffect>>();

            if (!File.Exists(filePath))
            {
                Debug.LogWarning($"状态效果影响文件不存在: {filePath}");
                return result;
            }

            var lines = File.ReadAllLines(filePath);
            for (int i = 1; i < lines.Length; i++) // 跳过标题行
            {
                var values = ParseCSVLine(lines[i]);
                if (values.Length < 6) continue;

                try
                {
                    var statusType = ParseEnum<StatusEffectType>(values[0]);
                    var effect = CreateStatusGameEffect(values);
                    
                    if (effect != null)
                    {
                        if (!result.ContainsKey(statusType))
                        {
                            result[statusType] = new List<GameEffect>();
                        }
                        result[statusType].Add(effect);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"解析状态效果影响失败 (行 {i + 1}): {e.Message}");
                }
            }

            return result;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private static string[] ParseCSVLine(string line)
        {
            return line.Split(',');
        }

        /// <summary>
        /// 解析枚举
        /// </summary>
        private static T ParseEnum<T>(string value) where T : struct
        {
            if (Enum.TryParse<T>(value, true, out var result))
            {
                return result;
            }
            throw new ArgumentException($"无法解析枚举值: {value} -> {typeof(T).Name}");
        }

        /// <summary>
        /// 创建游戏条件
        /// </summary>
        private static GameCondition CreateGameCondition(string[] values)
        {
            var conditionType = ParseEnum<GameConditionType>(values[1]);
            var description = values[7];

            switch (conditionType)
            {
                case GameConditionType.AttributeThreshold:
                    var attributeType = ParseEnum<AttributeType>(values[2]);
                    var attributeValue = float.Parse(values[3]);
                    return new GameCondition(conditionType, attributeType, attributeValue, description);

                case GameConditionType.ResourceThreshold:
                    var resourceType = ParseEnum<ResourceType>(values[2]);
                    var resourceValue = float.Parse(values[3]);
                    return new GameCondition(conditionType, resourceType, resourceValue, description);

                case GameConditionType.DaysSurvived:
                case GameConditionType.BoxesOpened:
                    var numericValue = float.Parse(values[3]);
                    return new GameCondition(conditionType, numericValue, description);

                case GameConditionType.SpecialEvent:
                    var eventId = values[6];
                    return new GameCondition(conditionType, eventId, 0f, description);

                default:
                    return null;
            }
        }

        /// <summary>
        /// 创建游戏效果
        /// </summary>
        private static GameEffect CreateGameEffect(string[] values)
        {
            var effectType = ParseEnum<GameEffectType>(values[1]);
            var description = values[6];

            switch (effectType)
            {
                case GameEffectType.AttributeChange:
                    var attributeType = ParseEnum<AttributeType>(values[2]);
                    var attributeValue = float.Parse(values[3]);
                    return new GameEffect(effectType, attributeType, attributeValue, description);

                case GameEffectType.ResourceChange:
                    var resourceType = ParseEnum<ResourceType>(values[2]);
                    var resourceValue = float.Parse(values[3]);
                    return new GameEffect(effectType, resourceType, resourceValue, description);

                case GameEffectType.StatusImmunity:
                    var statusType = ParseEnum<StatusEffectType>(values[2]);
                    return new GameEffect(effectType, statusType, 0f, 0, description);

                case GameEffectType.SpecialAbility:
                case GameEffectType.ProbabilityModifier:
                    var abilityId = values[5];
                    var value = string.IsNullOrEmpty(values[3]) ? 0f : float.Parse(values[3]);
                    return new GameEffect(effectType, abilityId, value, description);

                default:
                    return null;
            }
        }

        /// <summary>
        /// 创建状态效果的游戏效果
        /// </summary>
        private static GameEffect CreateStatusGameEffect(string[] values)
        {
            var effectType = ParseEnum<GameEffectType>(values[1]);
            var description = values[5];

            switch (effectType)
            {
                case GameEffectType.AttributeChange:
                    var attributeType = ParseEnum<AttributeType>(values[2]);
                    var attributeValue = float.Parse(values[3]);
                    var effect = new GameEffect(effectType, attributeType, attributeValue, description);
                    effect.boolValue = bool.Parse(values[4]); // perStack
                    return effect;

                case GameEffectType.SpecialAbility:
                    var abilityId = values[5];
                    var specialEffect = new GameEffect(effectType, abilityId, 0f, description);
                    specialEffect.boolValue = bool.Parse(values[4]); // perStack
                    return specialEffect;

                default:
                    return null;
            }
        }

        #endregion
    }
}
