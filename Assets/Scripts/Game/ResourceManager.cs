using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 资源管理器 - 管理游戏中的各种资源
    /// </summary>
    public class ResourceManager : IDisposable
    {
        private Dictionary<ResourceType, ResourceConfig> resourceConfigs;
        private List<ResourceTransaction> transactionHistory;
        private float dailyInflationRate = 0.01f; // 每日通胀率
        private float timeDebtInterestRate = 0.05f; // 时间债务利率

        public event Action<ResourceType, float, float> OnResourceChanged; // 资源类型, 变化量, 当前值
        public event Action<ResourceTransaction> OnTransactionCompleted;
        public event Action<string> OnResourceWarning; // 资源警告

        public ResourceManager()
        {
            Initialize();
        }

        private void Initialize()
        {
            resourceConfigs = new Dictionary<ResourceType, ResourceConfig>();
            transactionHistory = new List<ResourceTransaction>();
            
            InitializeResourceConfigs();
        }

        /// <summary>
        /// 更新资源系统（每帧调用）
        /// </summary>
        public void UpdateResources(float deltaTime)
        {
            UpdateDailyEffects(deltaTime);
            UpdateTimeDebt(deltaTime);
            CheckResourceWarnings();
        }

        /// <summary>
        /// 获取资源数量
        /// </summary>
        public float GetResourceAmount(PlayerData playerData, ResourceType resourceType)
        {
            return playerData.resources.GetResource(resourceType);
        }

        /// <summary>
        /// 添加资源
        /// </summary>
        public bool AddResource(PlayerData playerData, ResourceType resourceType, float amount, string reason = "")
        {
            if (amount <= 0) return false;

            float oldValue = playerData.resources.GetResource(resourceType);
            playerData.resources.AddResource(resourceType, amount);
            float newValue = playerData.resources.GetResource(resourceType);

            // 记录交易
            RecordTransaction(playerData, resourceType, amount, TransactionType.Gain, reason);

            // 触发事件
            OnResourceChanged?.Invoke(resourceType, amount, newValue);

            return true;
        }

        /// <summary>
        /// 消耗资源
        /// </summary>
        public bool ConsumeResource(PlayerData playerData, ResourceType resourceType, float amount, string reason = "")
        {
            if (amount <= 0) return false;

            float currentAmount = playerData.resources.GetResource(resourceType);
            if (currentAmount < amount)
            {
                OnResourceWarning?.Invoke($"资源不足: {resourceType}, 需要: {amount}, 拥有: {currentAmount}");
                return false;
            }

            float oldValue = currentAmount;
            playerData.resources.ConsumeResource(resourceType, amount);
            float newValue = playerData.resources.GetResource(resourceType);

            // 记录交易
            RecordTransaction(playerData, resourceType, -amount, TransactionType.Consume, reason);

            // 触发事件
            OnResourceChanged?.Invoke(resourceType, -amount, newValue);

            return true;
        }

        /// <summary>
        /// 资源转换
        /// </summary>
        public bool ConvertResource(PlayerData playerData, ResourceType fromType, ResourceType toType, 
            float fromAmount, float conversionRate, string reason = "")
        {
            if (!ConsumeResource(playerData, fromType, fromAmount, $"转换为{toType}: {reason}"))
            {
                return false;
            }

            float toAmount = fromAmount * conversionRate;
            AddResource(playerData, toType, toAmount, $"从{fromType}转换: {reason}");

            return true;
        }

        /// <summary>
        /// 时间债务系统
        /// </summary>
        public bool BorrowTime(PlayerData playerData, float timeAmount, int repaymentDays)
        {
            // 立即获得时间
            AddResource(playerData, ResourceType.Time, timeAmount, "时间债务借贷");

            // 创建债务记录
            var debt = new TimeDebt
            {
                principalAmount = timeAmount,
                interestRate = timeDebtInterestRate,
                repaymentDays = repaymentDays,
                daysRemaining = repaymentDays,
                borrowDate = DateTime.Now
            };

            // 添加到玩家状态效果
            var debtEffect = new StatusEffect(StatusEffectType.TimeDebt, timeAmount, repaymentDays, 
                $"时间债务: {timeAmount}单位，{repaymentDays}天内偿还");
            playerData.AddStatusEffect(debtEffect);

            return true;
        }

        /// <summary>
        /// 偿还时间债务
        /// </summary>
        public bool RepayTimeDebt(PlayerData playerData, float amount)
        {
            var timeDebtEffects = playerData.statusEffects.FindAll(e => e.type == StatusEffectType.TimeDebt);
            
            if (timeDebtEffects.Count == 0)
            {
                OnResourceWarning?.Invoke("没有时间债务需要偿还");
                return false;
            }

            float totalDebt = 0f;
            foreach (var debt in timeDebtEffects)
            {
                totalDebt += debt.intensity;
            }

            if (amount > totalDebt)
            {
                amount = totalDebt;
            }

            if (!ConsumeResource(playerData, ResourceType.Time, amount, "偿还时间债务"))
            {
                return false;
            }

            // 减少债务
            float remainingAmount = amount;
            for (int i = timeDebtEffects.Count - 1; i >= 0 && remainingAmount > 0; i--)
            {
                var debt = timeDebtEffects[i];
                if (debt.intensity <= remainingAmount)
                {
                    remainingAmount -= debt.intensity;
                    playerData.RemoveStatusEffect(StatusEffectType.TimeDebt);
                }
                else
                {
                    debt.intensity -= remainingAmount;
                    remainingAmount = 0;
                }
            }

            return true;
        }

        /// <summary>
        /// 计算每日资源变化
        /// </summary>
        public void ProcessDailyResourceChanges(PlayerData playerData)
        {
            // 每日基础收入
            float dailyCredits = CalculateDailyCredits(playerData);
            AddResource(playerData, ResourceType.Credits, dailyCredits, "每日基础收入");

            // 通胀效应 - 信用点贬值
            float currentCredits = GetResourceAmount(playerData, ResourceType.Credits);
            float inflationLoss = currentCredits * dailyInflationRate;
            ConsumeResource(playerData, ResourceType.Credits, inflationLoss, "通胀贬值");

            // 社会积分衰减
            float socialScore = GetResourceAmount(playerData, ResourceType.SocialScore);
            if (socialScore > 0)
            {
                float decay = socialScore * 0.02f; // 每日衰减2%
                ConsumeResource(playerData, ResourceType.SocialScore, decay, "社会积分自然衰减");
            }

            // 人性值和道德值的自然恢复/衰减
            ProcessMoralityChanges(playerData);
        }

        /// <summary>
        /// 计算每日信用点收入
        /// </summary>
        private float CalculateDailyCredits(PlayerData playerData)
        {
            float baseIncome = 50f; // 基础救济金

            // 根据社会阶层调整
            switch (playerData.socialClass)
            {
                case SocialClass.Parasite:
                    baseIncome = 50f;
                    break;
                case SocialClass.Worker:
                    baseIncome = 150f;
                    break;
                case SocialClass.Chosen:
                    baseIncome = 500f;
                    break;
            }

            // 工作收入
            if (playerData.currentJob != JobType.None)
            {
                baseIncome += GetJobIncome(playerData.currentJob);
            }

            // 天赋影响
            if (playerData.HasTalent(TalentType.RichKid))
            {
                baseIncome *= 2f;
            }

            if (playerData.HasTalent(TalentType.Workaholic))
            {
                baseIncome *= 1.5f;
            }

            // 状态效果影响
            if (playerData.HasStatusEffect(StatusEffectType.Sickness))
            {
                baseIncome *= 0.5f;
            }

            return baseIncome;
        }

        /// <summary>
        /// 获取工作收入
        /// </summary>
        private float GetJobIncome(JobType jobType)
        {
            switch (jobType)
            {
                case JobType.DataEntry: return 100f;
                case JobType.BoxSorting: return 80f;
                case JobType.SystemMaintenance: return 200f;
                case JobType.Advertising: return 150f;
                case JobType.Security: return 300f;
                case JobType.Management: return 500f;
                case JobType.Research: return 400f;
                case JobType.Enforcement: return 600f;
                default: return 0f;
            }
        }

        /// <summary>
        /// 处理道德和人性变化
        /// </summary>
        private void ProcessMoralityChanges(PlayerData playerData)
        {
            // 人性值自然恢复（如果没有负面影响）
            if (!playerData.HasStatusEffect(StatusEffectType.CognitiveDamage) && 
                playerData.attributes.humanity < 100f)
            {
                playerData.attributes.ModifyAttribute(AttributeType.Humanity, 1f);
            }

            // 道德值根据行为调整
            if (playerData.attributes.morality < 50f && !playerData.HasStatusEffect(StatusEffectType.DataCorruption))
            {
                playerData.attributes.ModifyAttribute(AttributeType.Morality, 0.5f);
            }

            // 污染度的影响
            if (playerData.attributes.pollution > 50f)
            {
                playerData.attributes.ModifyAttribute(AttributeType.Humanity, -0.5f);
                playerData.attributes.ModifyAttribute(AttributeType.Morality, -0.3f);
            }
        }

        /// <summary>
        /// 更新每日效果
        /// </summary>
        private void UpdateDailyEffects(float deltaTime)
        {
            // 这里可以添加实时的资源变化效果
            // 比如每小时的微小变化等
        }

        /// <summary>
        /// 更新时间债务
        /// </summary>
        private void UpdateTimeDebt(float deltaTime)
        {
            // 实时计算时间债务利息
            // 这里可以添加更复杂的债务计算逻辑
        }

        /// <summary>
        /// 检查资源警告
        /// </summary>
        private void CheckResourceWarnings()
        {
            // 这里可以添加资源不足的警告逻辑
        }

        /// <summary>
        /// 记录资源交易
        /// </summary>
        private void RecordTransaction(PlayerData playerData, ResourceType resourceType, float amount, 
            TransactionType type, string reason)
        {
            var transaction = new ResourceTransaction
            {
                playerId = playerData.playerName,
                resourceType = resourceType,
                amount = amount,
                transactionType = type,
                reason = reason,
                timestamp = DateTime.Now,
                playerStateSnapshot = JsonUtility.ToJson(playerData.resources)
            };

            transactionHistory.Add(transaction);

            // 限制历史记录数量
            if (transactionHistory.Count > 1000)
            {
                transactionHistory.RemoveAt(0);
            }

            OnTransactionCompleted?.Invoke(transaction);
        }

        /// <summary>
        /// 初始化资源配置
        /// </summary>
        private void InitializeResourceConfigs()
        {
            resourceConfigs[ResourceType.Credits] = new ResourceConfig
            {
                name = "信用点",
                description = "基础货币，用于购买盲盒和基础服务",
                maxValue = float.MaxValue,
                minValue = 0f,
                defaultValue = 200f,
                inflationRate = 0.01f
            };

            resourceConfigs[ResourceType.Time] = new ResourceConfig
            {
                name = "时间",
                description = "珍贵的时间资源，可用于特殊交易",
                maxValue = 1000f,
                minValue = 0f,
                defaultValue = 100f,
                inflationRate = 0f
            };

            resourceConfigs[ResourceType.Diamond] = new ResourceConfig
            {
                name = "钻石",
                description = "稀有货币，用于高级交易",
                maxValue = float.MaxValue,
                minValue = 0f,
                defaultValue = 0f,
                inflationRate = -0.005f // 钻石升值
            };

            resourceConfigs[ResourceType.SocialScore] = new ResourceConfig
            {
                name = "社会积分",
                description = "社会地位的体现，影响各种机会",
                maxValue = 1000f,
                minValue = 0f,
                defaultValue = 0f,
                inflationRate = 0f
            };
        }

        public void Dispose()
        {
            resourceConfigs?.Clear();
            transactionHistory?.Clear();
        }
    }

    /// <summary>
    /// 资源配置
    /// </summary>
    [Serializable]
    public class ResourceConfig
    {
        public string name;
        public string description;
        public float maxValue;
        public float minValue;
        public float defaultValue;
        public float inflationRate;
    }

    /// <summary>
    /// 资源交易记录
    /// </summary>
    [Serializable]
    public class ResourceTransaction
    {
        public string playerId;
        public ResourceType resourceType;
        public float amount;
        public TransactionType transactionType;
        public string reason;
        public DateTime timestamp;
        public string playerStateSnapshot;
    }

    /// <summary>
    /// 交易类型
    /// </summary>
    public enum TransactionType
    {
        Gain,       // 获得
        Consume,    // 消耗
        Convert,    // 转换
        Transfer    // 转移
    }

    /// <summary>
    /// 时间债务
    /// </summary>
    [Serializable]
    public class TimeDebt
    {
        public float principalAmount;   // 本金
        public float interestRate;      // 利率
        public int repaymentDays;       // 还款天数
        public int daysRemaining;       // 剩余天数
        public DateTime borrowDate;     // 借贷日期
        public float totalInterest;     // 总利息

        public float GetTotalDebt()
        {
            return principalAmount + (principalAmount * interestRate * (repaymentDays - daysRemaining));
        }
    }
}
