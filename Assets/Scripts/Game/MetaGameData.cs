using System;
using System.Collections.Generic;
using BoxOfFate.Game.Core;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 元游戏进度数据 - 跨游戏局持久化的数据
    /// </summary>
    [Serializable]
    public class MetaGameProgress
    {
        [Header("基础统计")]
        public int totalGamesPlayed = 0;                    // 总游戏次数
        public int totalLifetimeBoxes = 0;                  // 总开盒数
        public float totalPlayTimeHours = 0f;               // 总游戏时间（小时）
        public int totalDeaths = 0;                         // 总死亡次数
        public int maxDaysSurvived = 0;                     // 最长生存天数

        [Header("觉醒系统")]
        public float awakeningLevel = 0f;                   // 觉醒程度 (0-100)
        public float fraudAwarenessLevel = 0f;              // 欺诈认知程度 (0-100)
        public float systemUnderstandingLevel = 0f;         // 系统理解程度 (0-100)
        public int manipulationDetections = 0;              // 检测到的操控次数
        public int truthFragmentsFound = 0;                 // 发现的真相碎片数

        [Header("解锁功能")]
        public List<string> unlockedFeatures = new List<string>();     // 已解锁的功能
        public List<string> discoveredTruths = new List<string>();     // 已发现的真相
        public List<string> unlockedEndingTypes = new List<string>();  // 已解锁的结局类型

        [Header("结局记录")]
        public Dictionary<string, int> endingCounts = new Dictionary<string, int>();
        public List<GameEndingRecord> endingHistory = new List<GameEndingRecord>();

        [Header("特殊成就")]
        public List<string> specialAchievements = new List<string>();
        public Dictionary<string, float> specialCounters = new Dictionary<string, float>();

        [Header("系统状态")]
        public DateTime firstPlayTime;                      // 首次游戏时间
        public DateTime lastPlayTime;                       // 最后游戏时间
        public string currentPhase = "Innocent";            // 当前阶段
        public bool hasReachedTrueEnding = false;           // 是否达到真结局

        /// <summary>
        /// 检查是否拥有特定功能
        /// </summary>
        public bool HasFeature(string featureName)
        {
            return unlockedFeatures.Contains(featureName);
        }

        /// <summary>
        /// 解锁功能
        /// </summary>
        public void UnlockFeature(string featureName)
        {
            if (!HasFeature(featureName))
            {
                unlockedFeatures.Add(featureName);
                Debug.Log($"[MetaGame] 解锁功能: {featureName}");
            }
        }

        /// <summary>
        /// 添加真相发现
        /// </summary>
        public void DiscoverTruth(string truthId)
        {
            if (!discoveredTruths.Contains(truthId))
            {
                discoveredTruths.Add(truthId);
                truthFragmentsFound++;
                Debug.Log($"[MetaGame] 发现真相: {truthId}");
            }
        }

        /// <summary>
        /// 记录结局
        /// </summary>
        public void RecordEnding(GameEndType endingType, PlayerData finalPlayerData)
        {
            string endingKey = endingType.ToString();
            
            if (!endingCounts.ContainsKey(endingKey))
            {
                endingCounts[endingKey] = 0;
            }
            endingCounts[endingKey]++;

            var record = new GameEndingRecord
            {
                endingType = endingType,
                timestamp = DateTime.Now,
                daysSurvived = finalPlayerData.daysSurvived,
                finalAwakeningLevel = awakeningLevel,
                gameNumber = totalGamesPlayed + 1
            };
            endingHistory.Add(record);

            if (!unlockedEndingTypes.Contains(endingKey))
            {
                unlockedEndingTypes.Add(endingKey);
            }
        }

        /// <summary>
        /// 获取当前游戏阶段
        /// </summary>
        public MetaGamePhase GetCurrentPhase()
        {
            if (awakeningLevel < 20) return MetaGamePhase.Innocent;
            if (awakeningLevel < 40) return MetaGamePhase.Suspicious;
            if (awakeningLevel < 60) return MetaGamePhase.Aware;
            if (awakeningLevel < 80) return MetaGamePhase.Rebellious;
            return MetaGamePhase.Enlightened;
        }

        /// <summary>
        /// 获取特殊计数器值
        /// </summary>
        public float GetCounter(string counterName)
        {
            return specialCounters.ContainsKey(counterName) ? specialCounters[counterName] : 0f;
        }

        /// <summary>
        /// 设置特殊计数器值
        /// </summary>
        public void SetCounter(string counterName, float value)
        {
            specialCounters[counterName] = value;
        }

        /// <summary>
        /// 增加特殊计数器值
        /// </summary>
        public void AddToCounter(string counterName, float value)
        {
            if (!specialCounters.ContainsKey(counterName))
            {
                specialCounters[counterName] = 0f;
            }
            specialCounters[counterName] += value;
        }
    }

    /// <summary>
    /// 游戏结局记录
    /// </summary>
    [Serializable]
    public class GameEndingRecord
    {
        public GameEndType endingType;
        public DateTime timestamp;
        public int daysSurvived;
        public float finalAwakeningLevel;
        public int gameNumber;
    }

    /// <summary>
    /// 元游戏阶段
    /// </summary>
    public enum MetaGamePhase
    {
        Innocent,       // 天真期 (0-20)
        Suspicious,     // 怀疑期 (20-40)
        Aware,          // 觉醒期 (40-60)
        Rebellious,     // 反抗期 (60-80)
        Enlightened     // 超越期 (80-100)
    }

    /// <summary>
    /// 功能解锁配置
    /// </summary>
    [Serializable]
    public class MetaFeature
    {
        public string id;
        public string name;
        public string description;
        public float baseCost;
        public int maxLevel;
        public float effectPerLevel;
        public List<GameCondition> unlockConditions;
        public float costMultiplier = 1.5f;
    }

    /// <summary>
    /// 真相碎片配置
    /// </summary>
    [Serializable]
    public class TruthFragmentConfig
    {
        public string truthId;
        public string title;
        public string content;
        public string triggerCondition;
        public float awakeningValue;
        public bool isKeyTruth;
    }
}
