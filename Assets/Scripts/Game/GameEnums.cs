using System;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 游戏状态枚举
    /// </summary>
    public enum GameState
    {
        MainMenu,       // 主菜单
        Playing,        // 游戏中
        Paused,         // 暂停
        GameEnd,        // 游戏结束
        Settings        // 设置
    }

    /// <summary>
    /// 游戏结束类型
    /// </summary>
    public enum GameEndType
    {
        Death,                  // 死亡
        SystemAssimilation,     // 系统同化
        DataCorruption,         // 数据损坏
        Rebellion,              // 反抗成功
        Transcendence,          // 超越结局
        Sacrifice,              // 牺牲结局
        TrumanShow,             // 楚门结局
        UltimateSupport        // 终极幸存者
    }

    /// <summary>
    /// 玩家属性类型
    /// </summary>
    public enum AttributeType
    {
        None,
        Health,         // 生命值
        Energy,         // 精力值
        Luck,           // 幸运值
        Social,         // 社交值
        Dependence,     // 依存度
        Cognition,      // 认知值
        Pollution,      // 污染度
        Humanity,       // 人性值
        Morality        // 道德值
    }

    /// <summary>
    /// 社会阶层
    /// </summary>
    public enum SocialClass
    {
        Parasite = 0,   // 蛀虫（底层）
        Worker = 1,     // 工蜂（中层）
        Chosen = 2      // 神选者（顶层）
    }

    /// <summary>
    /// 盲盒类型
    /// </summary>
    public enum BlindBoxType
    {
        Basic,          // 基础/生存盲盒
        Life,           // 生活/时间盲盒
        Premium,        // 高级/神谕盲盒
        Mystery,        // 神秘盲盒
        BlackMarket,     // 黑市盲盒
        Emergency,  //紧急救援盒
        System, //系统奖励盒
    }

    /// <summary>
    /// 盲盒内容类型
    /// </summary>
    public enum BlindBoxContentType
    {
        Empty,              // 空盒
        Food,               // 食物
        Water,              // 水
        Medicine,           // 医疗用品
        Job,                // 工作机会
        Housing,            // 住房
        Clothing,           // 服装
        Technology,         // 科技产品
        Identity,           // 身份提升
        Talent,             // 天赋/技能
        Currency,           // 货币
        Time,               // 时间
        Memory,             // 记忆
        Trap,               // 陷阱
        Virus,              // 病毒
        Tracker,            // 追踪器
        Debt,               // 债务
        Punishment          // 惩罚
    }

    /// <summary>
    /// 资源类型
    /// </summary>
    public enum ResourceType
    {
        Credits,        // 信用点
        Time,           // 时间
        Humanity,       // 人性值
        Morality,       // 道德值
        Diamond,        // 钻石
        SocialScore     // 社会积分
    }

    /// <summary>
    /// 天赋类型
    /// </summary>
    public enum TalentType
    {
        GamblerGene,//	赌徒基因
        SocialMaster,//	社交达人
        RichKid,//	富二代
        HackerGenius,//	黑客天才
        UnluckyOne,//	倒霉蛋
        Workaholic,//	天生劳模
        MadGenius,//	天才疯子
        ImmuneVariant,//	免疫异变
        BlindBoxIntuition,//	盲盒直觉
        SystemRebel,//	系统叛逆者
        LuckyCharm,//	幸运符
        DataMiner,//	数据挖掘者
        SurvivalInstinct,//	生存本能
        MemoryKeeper,//	记忆守护者
        ResourceMaster,//	资源大师
    }

    /// <summary>
    /// 事件类型
    /// </summary>
    public enum EventType
    {
        Random,         // 随机事件
        Story,          // 剧情事件
        Work,           // 工作事件
        Social,         // 社交事件
        Personal,       // 个人事件
        Crisis,         // 危机事件
        Opportunity,    // 机会事件
        BlackMarket,    // 黑市事件
        Resistance,     // 反抗事件
        System,         // 系统事件
        Punishment      // 惩罚事件
    }

    /// <summary>
    /// 状态效果类型
    /// </summary>
    public enum StatusEffectType
    {
        // 正面效果
        LuckyBoost,         // 幸运提升
        EnergyBoost,        // 精力提升
        SocialBoost,        // 社交提升
        WorkEfficiency,     // 工作效率
        Euphoria,           // 兴奋状态

        // 负面效果
        Sickness,           // 疾病
        Fatigue,            // 疲劳
        Depression,         // 抑郁
        Addiction,          // 成瘾
        Paranoia,           // 偏执
        MemoryLoss,         // 记忆丢失

        // 特殊效果
        SystemTracking,     // 系统追踪
        CognitiveDamage,    // 认知损伤
        TimeDebt,           // 时间债务
        DataCorruption      // 数据损坏
    }

    /// <summary>
    /// 工作类型
    /// </summary>
    public enum JobType
    {
        None,               // 无工作
        DataEntry,          // 数据录入
        BoxSorting,         // 盲盒分拣
        SystemMaintenance,  // 系统维护
        Advertising,        // 广告推广
        Security,           // 安保
        Management,         // 管理
        Research,           // 研究
        Enforcement         // 执法
    }

    /// <summary>
    /// 难度等级
    /// </summary>
    public enum DifficultyLevel
    {
        Easy,       // 简单
        Normal,     // 普通
        Hard,       // 困难
        Nightmare   // 噩梦
    }

    /// <summary>
    /// 物品效果类型
    /// </summary>
    public enum ItemEffectType
    {
        AttributeChange,    // 属性变化
        ResourceChange,     // 资源变化
        StatusEffect,       // 状态效果
        TalentGrant,        // 天赋授予
        EventTrigger        // 事件触发
    }



    /// <summary>
    /// 黑市商贩类型
    /// </summary>
    public enum BlackMarketMerchantType
    {
        ShadowDealer,       // 影子商人
        DataBroker,         // 数据掮客
        MemoryTrader,       // 记忆商人
        SystemHacker,       // 系统黑客
        TimeThief           // 时间窃贼
    }

    /// <summary>
    /// 成就类别
    /// </summary>
    public enum AchievementCategory
    {
        Survival,           // 生存类
        BlindBox,           // 盲盒类
        Social,             // 社会类
        Story,              // 剧情类
        Special,            // 特殊类
        Hidden              // 隐藏类
    }

    /// <summary>
    /// 成就稀有度
    /// </summary>
    public enum AchievementRarity
    {
        Common,             // 普通
        Uncommon,           // 不常见
        Rare,               // 稀有
        Epic,               // 史诗
        Legendary           // 传说
    }

}
