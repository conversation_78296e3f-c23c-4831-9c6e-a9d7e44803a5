using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 盲盒纪元 - 游戏核心逻辑管理器
    /// </summary>
    public class GameLogic : MonoBehaviour
    {
        private static GameLogic _instance;
        public static GameLogic Instance => _instance;

        [Header("游戏状态")]
        public GameState currentGameState = GameState.MainMenu;

        [Header("玩家数据")]
        public PlayerData playerData;

        [Header("系统管理器")]
        public BlindBoxSystem blindBoxManager;
        public ResourceManager resourceManager;
        public SocialClassSystem socialClassManager;
        public TalentSystem talentManager;
        public EventSystem eventManager;
        public WorkSystem workSystem;
        public BlackMarketSystem blackMarketSystem;
        public PrivilegeSystem privilegeSystem;
        public GameFlowController gameFlowController;
        public AchievementSystem achievementSystem;

        [Header("游戏设置")]
        public float gameSpeed = 1f;
        public bool debugMode = false;
        public bool enableTestMode = false;

        void Awake()
        {
            if (_instance != null)
            {
                Destroy(gameObject);
                return;
            }

            _instance = this;
            DontDestroyOnLoad(gameObject);

            InitializeSystems();
        }

        void Start()
        {
            // 初始化游戏数据
            InitializeGameData();
        }

        void Update()
        {
            // 游戏主循环
            UpdateGameLoop();
        }

        void OnDestroy()
        {
            Cleanup();
        }

        private void InitializeSystems()
        {
            // 初始化核心管理器
            blindBoxManager = new BlindBoxSystem();
            resourceManager = new ResourceManager();
            socialClassManager = new SocialClassSystem();
            talentManager = new TalentSystem();
            eventManager = new EventSystem();

            // 初始化扩展系统
            workSystem = new WorkSystem();
            blackMarketSystem = new BlackMarketSystem();
            privilegeSystem = new PrivilegeSystem();
            gameFlowController = new GameFlowController();
            achievementSystem = new AchievementSystem();

            

            // 设置事件监听
            SetupEventListeners();

            Debug.Log("游戏系统初始化完成");
        }

        private void InitializeGameData()
        {
            if (playerData == null)
            {
                playerData = new PlayerData();
                playerData.Initialize();
            }
        }

        /// <summary>
        /// 设置事件监听
        /// </summary>
        private void SetupEventListeners()
        {
            // 盲盒事件
            if (blindBoxManager != null)
            {
                blindBoxManager.OnBoxOpened += OnBlindBoxOpened;
            }

            // 成就事件
            if (achievementSystem != null)
            {
                achievementSystem.OnAchievementUnlocked += OnAchievementUnlocked;
                achievementSystem.OnProgressUpdated += OnProgressUpdated;
            }

            // 游戏流程事件
            if (gameFlowController != null)
            {
                gameFlowController.OnNewDay += OnNewDay;
                gameFlowController.OnGameEnded += OnGameEnded;
            }
        }

        private void UpdateGameLoop()
        {
            if (currentGameState == GameState.Playing)
            {
                // 更新各个系统
                resourceManager?.UpdateResources(Time.deltaTime);
                eventManager?.UpdateEvents(Time.deltaTime);
                gameFlowController?.UpdateGameFlow(Time.deltaTime, playerData);

                // 定期更新成就进度
                UpdateAchievementProgress();

                // 检查游戏结束条件
                CheckGameEndConditions();
            }
        }

        /// <summary>
        /// 更新成就进度
        /// </summary>
        private void UpdateAchievementProgress()
        {
            if (achievementSystem == null || playerData == null) return;

            // 更新基础进度
            achievementSystem.UpdateProgress("days_survived", playerData.daysSurvived, playerData);
            achievementSystem.UpdateProgress("boxes_opened", playerData.totalBoxesOpened, playerData);
            achievementSystem.UpdateProgress("credits_earned", playerData.resources.credits, playerData);
        }

        private void CheckGameEndConditions()
        {
            if (playerData.attributes.health <= 0)
            {
                TriggerGameEnd(GameEndType.Death);
            }
            else if (playerData.attributes.dependence >= 100)
            {
                TriggerGameEnd(GameEndType.SystemAssimilation);
            }
            else if (playerData.attributes.pollution >= 100)
            {
                TriggerGameEnd(GameEndType.DataCorruption);
            }
        }

        private void TriggerGameEnd(GameEndType endType)
        {
            currentGameState = GameState.GameEnd;
            eventManager.TriggerGameEndEvent(endType);
        }

        private void Cleanup()
        {
            // 清理系统资源
            blindBoxManager?.Dispose();
            resourceManager?.Dispose();
            socialClassManager?.Dispose();
            talentManager?.Dispose();
            eventManager?.Dispose();
            workSystem?.Dispose();
            blackMarketSystem?.Dispose();
            privilegeSystem?.Dispose();
            gameFlowController?.Dispose();
            achievementSystem?.Dispose();
        }

        /// <summary>
        /// 盲盒开启事件处理
        /// </summary>
        private void OnBlindBoxOpened(BlindBoxResult result)
        {
            if (achievementSystem != null && playerData != null)
            {
                achievementSystem.IncrementProgress("boxes_opened", 1f, playerData);
            }
        }

        /// <summary>
        /// 成就解锁事件处理
        /// </summary>
        private void OnAchievementUnlocked(Achievement achievement)
        {
            Debug.Log($"成就解锁: {achievement.name} - {achievement.description}");
        }

        /// <summary>
        /// 进度更新事件处理
        /// </summary>
        private void OnProgressUpdated(string trackerId, float value)
        {
            if (debugMode)
            {
                Debug.Log($"进度更新: {trackerId} = {value}");
            }
        }

        /// <summary>
        /// 新的一天事件处理
        /// </summary>
        private void OnNewDay(int day)
        {
            if (achievementSystem != null && playerData != null)
            {
                achievementSystem.UpdateProgress("days_survived", day, playerData);
            }
            Debug.Log($"新的一天: 第{day}天");
        }

        /// <summary>
        /// 游戏结束事件处理
        /// </summary>
        private void OnGameEnded(GameEndType endType, string message)
        {
            currentGameState = GameState.GameEnd;
            Debug.Log($"游戏结束: {endType} - {message}");
        }

        /// <summary>
        /// 开始新游戏
        /// </summary>
        public void StartNewGame()
        {
            playerData = new PlayerData();
            playerData.Initialize();

            // 随机分配初始天赋
            AssignStartingTalent();

            // 启动游戏流程
            gameFlowController?.StartGameFlow(playerData);

            currentGameState = GameState.Playing;

            Debug.Log("新游戏开始！");
        }

        /// <summary>
        /// 分配初始天赋
        /// </summary>
        private void AssignStartingTalent()
        {
            var startingTalents = new TalentType[]
            {
                TalentType.RichKid,
                TalentType.UnluckyOne,
                TalentType.Workaholic
            };

            // 随机选择一个初始天赋
            var randomTalent = startingTalents[UnityEngine.Random.Range(0, startingTalents.Length)];
            talentManager.AcquireTalent(playerData, randomTalent);
        }

        /// <summary>
        /// 开启盲盒
        /// </summary>
        public BlindBoxResult OpenBlindBox(BlindBoxType boxType)
        {
            if (currentGameState != GameState.Playing)
            {
                Debug.LogWarning("游戏未在进行中，无法开启盲盒");
                return null;
            }

            return blindBoxManager.OpenBlindBox(boxType, playerData);
        }

        /// <summary>
        /// 尝试晋升社会阶层
        /// </summary>
        public PromotionResult AttemptPromotion(SocialClass targetClass)
        {
            if (currentGameState != GameState.Playing)
            {
                Debug.LogWarning("游戏未在进行中，无法尝试晋升");
                return null;
            }

            return socialClassManager.AttemptPromotion(playerData, targetClass);
        }

        /// <summary>
        /// 开始工作
        /// </summary>
        public WorkResult StartWork(JobType jobType, float workHours = 8f)
        {
            if (workSystem == null || playerData == null)
            {
                return new WorkResult { success = false, message = "工作系统未初始化" };
            }

            return workSystem.StartWork(playerData, jobType, workHours);
        }

        /// <summary>
        /// 访问黑市
        /// </summary>
        public BlackMarketAccess AccessBlackMarket()
        {
            if (blackMarketSystem == null || playerData == null)
            {
                return new BlackMarketAccess { canAccess = false, reason = "黑市系统未初始化" };
            }

            return blackMarketSystem.AccessBlackMarket(playerData);
        }

        /// <summary>
        /// 使用特权
        /// </summary>
        public PrivilegeResult UsePrivilege(string privilegeId, Dictionary<string, object> parameters = null)
        {
            if (privilegeSystem == null || playerData == null)
            {
                return new PrivilegeResult { success = false, errorMessage = "特权系统未初始化" };
            }

            return privilegeSystem.UsePrivilege(playerData, privilegeId, parameters);
        }

        /// <summary>
        /// 获取成就列表
        /// </summary>
        public List<Achievement> GetAchievements()
        {
            return achievementSystem?.GetAllAchievements() ?? new List<Achievement>();
        }

        /// <summary>
        /// 获取游戏统计
        /// </summary>
        public GameStatistics GetGameStatistics()
        {
            if (playerData == null) return new GameStatistics();

            var stats = new GameStatistics();

            // 基础统计
            stats.daysSurvived = playerData.daysSurvived;
            stats.totalBoxesOpened = playerData.totalBoxesOpened;
            stats.currentSocialClass = playerData.socialClass;
            stats.totalCreditsEarned = playerData.resources.credits;
            stats.achievementsUnlocked = playerData.completedAchievements.Count;
            stats.talentsAcquired = playerData.talents.Count;

            // 其他统计数据可以从各个系统获取
            // 这里简化处理，实际项目中应该从各个管理器收集数据

            return stats;
        }

        /// <summary>
        /// 重置游戏
        /// </summary>
        public void ResetGame()
        {
            currentGameState = GameState.MainMenu;

            // 重置玩家数据
            if (playerData != null)
            {
                playerData.Initialize();
            }

            // 重置各个系统
            blindBoxManager?.Dispose();
            socialClassManager?.Dispose();
            eventManager?.Dispose();
            achievementSystem?.Dispose();
            gameFlowController?.Dispose();

            // 重新初始化
            InitializeSystems();

            Debug.Log("游戏已重置");
        }

        /// <summary>
        /// 处理每日更新
        /// </summary>
        public void ProcessDailyUpdate()
        {
            if (currentGameState != GameState.Playing) return;

            // 更新玩家数据
            playerData.DailyUpdate();

            // 处理资源变化
            resourceManager.ProcessDailyResourceChanges(playerData);

            // 更新状态效果
            talentManager.UpdateStatusEffects(playerData);

            Debug.Log($"第{playerData.daysSurvived}天结束");
        }

        /// <summary>
        /// 暂停游戏
        /// </summary>
        public void PauseGame()
        {
            if (currentGameState == GameState.Playing)
            {
                currentGameState = GameState.Paused;
                Time.timeScale = 0f;
            }
        }

        /// <summary>
        /// 恢复游戏
        /// </summary>
        public void ResumeGame()
        {
            if (currentGameState == GameState.Paused)
            {
                currentGameState = GameState.Playing;
                Time.timeScale = gameSpeed;
            }
        }

        /// <summary>
        /// 设置游戏速度
        /// </summary>
        public void SetGameSpeed(float speed)
        {
            gameSpeed = Mathf.Clamp(speed, 0.1f, 5f);
            if (currentGameState == GameState.Playing)
            {
                Time.timeScale = gameSpeed;
            }
        }



        /// <summary>
        /// 保存游戏
        /// </summary>
        public void SaveGame()
        {
            try
            {
                var saveData = new GameSaveData
                {
                    playerData = playerData,
                    gameState = currentGameState,
                    saveTime = DateTime.Now
                };

                string jsonData = JsonUtility.ToJson(saveData, true);
                PlayerPrefs.SetString("SaveData", jsonData);
                PlayerPrefs.Save();

                Debug.Log("游戏保存成功");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"保存游戏失败: {e.Message}");
            }
        }

        /// <summary>
        /// 加载游戏
        /// </summary>
        public bool LoadGame()
        {
            try
            {
                if (PlayerPrefs.HasKey("SaveData"))
                {
                    string jsonData = PlayerPrefs.GetString("SaveData");
                    var saveData = JsonUtility.FromJson<GameSaveData>(jsonData);

                    playerData = saveData.playerData;
                    currentGameState = saveData.gameState;

                    Debug.Log("游戏加载成功");
                    return true;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加载游戏失败: {e.Message}");
            }

            return false;
        }



        /// <summary>
        /// 退出游戏
        /// </summary>
        public void QuitGame()
        {
            SaveGame();

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
    }



    /// <summary>
    /// 游戏存档数据
    /// </summary>
    [System.Serializable]
    public class GameSaveData
    {
        public PlayerData playerData;
        public GameState gameState;
        public DateTime saveTime;
    }
}