using System;
using System.Collections.Generic;
using UnityEngine;

namespace BoxOfFate.Game
{
    /// <summary>
    /// 游戏流程控制器 - 管理游戏的核心循环和阶段推进
    /// </summary>
    public class GameFlowController : IDisposable
    {
        private GamePhase currentPhase;
        private bool nextDay = false;
        private int currentDay = 0;
        private bool isGameActive = false;

        private Dictionary<GamePhase, PhaseConfig> phaseConfigs;
        private List<GameMilestone> milestones;
        private List<SurvivalCheck> survivalChecks;

        public event Action<int> OnNewDay;
        public event Action<GamePhase, GamePhase> OnPhaseChanged;
        public event Action<GameMilestone> OnMilestoneReached;
        public event Action<GameEndType, string> OnGameEnded;
        public event Action<SurvivalThreat> OnSurvivalThreat;

        public GameFlowController()
        {
            Initialize();
        }

        private void Initialize()
        {
            currentPhase = GamePhase.Tutorial;
            phaseConfigs = new Dictionary<GamePhase, PhaseConfig>();
            milestones = new List<GameMilestone>();
            survivalChecks = new List<SurvivalCheck>();
            
            InitializePhaseConfigs();
            InitializeMilestones();
            InitializeSurvivalChecks();
        }

        /// <summary>
        /// 开始游戏流程
        /// </summary>
        public void StartGameFlow(PlayerData playerData)
        {
            // 通知元游戏管理器
            if (MetaGameSystem.Instance != null)
            {
                MetaGameSystem.Instance.StartNewGame();
            }

            isGameActive = true;
            currentDay = playerData.daysSurvived;
            nextDay = false;
            
            // 根据玩家进度确定当前阶段
            DetermineCurrentPhase(playerData);
            
            Debug.Log($"游戏流程开始，当前阶段：{currentPhase}，第{currentDay}天");
        }

        /// <summary>
        /// 更新游戏流程
        /// </summary>
        public void UpdateGameFlow(float deltaTime, PlayerData playerData)
        {
            if (!isGameActive) return;
            
            // 检查是否进入新的一天
            if (nextDay)
            {
                ProcessNewDay(playerData);
                nextDay = false;
            }

            // 检查阶段推进
            CheckPhaseProgression(playerData);
            
            // 检查里程碑
            CheckMilestones(playerData);
            
            // 检查生存威胁
            CheckSurvivalThreats(playerData);
            
            // 检查游戏结束条件
            CheckGameEndConditions(playerData);
        }

        public void NextDay()
        {
            nextDay = true;
        }

        /// <summary>
        /// 处理新的一天
        /// </summary>
        private void ProcessNewDay(PlayerData playerData)
        {
            currentDay++;
            playerData.daysSurvived = currentDay;

            // 每日更新
            ProcessDailyUpdates(playerData);

            // 触发每日事件
            TriggerDailyEvents(playerData);

            OnNewDay?.Invoke(currentDay);
            Debug.Log($"新的一天开始：第{currentDay}天");
        }

        /// <summary>
        /// 处理每日更新
        /// </summary>
        private void ProcessDailyUpdates(PlayerData playerData)
        {
            // 基础生存消耗
            ApplyDailySurvivalCosts(playerData);
            
            // 状态效果更新
            UpdateStatusEffects(playerData);
            
            // 资源自然变化
            ApplyResourceChanges(playerData);
            
            // 阶段特殊效果
            ApplyPhaseEffects(playerData);
        }

        /// <summary>
        /// 应用每日生存消耗
        /// </summary>
        private void ApplyDailySurvivalCosts(PlayerData playerData)
        {
            var phaseConfig = phaseConfigs[currentPhase];
            
            // 基础消耗
            playerData.attributes.ModifyAttribute(AttributeType.Energy, -phaseConfig.dailyEnergyCost);
            playerData.attributes.ModifyAttribute(AttributeType.Health, -phaseConfig.dailyHealthCost);

            // 依存度增长
            float dependenceIncrease = phaseConfig.dailyDependenceIncrease;
            if (playerData.totalBoxesOpened > 50)
            {
                dependenceIncrease *= 1.5f; // 开盒越多，依存度增长越快
            }
            playerData.attributes.ModifyAttribute(AttributeType.Dependence, dependenceIncrease);

            // 认知值衰减
            if (playerData.attributes.dependence > 60f)
            {
                playerData.attributes.ModifyAttribute(AttributeType.Cognition, -1f);
            }
        }

        /// <summary>
        /// 更新状态效果
        /// </summary>
        private void UpdateStatusEffects(PlayerData playerData)
        {
            // 这里应该调用TalentManager的UpdateStatusEffects方法
            // 为了简化，我们直接在这里处理一些基础逻辑
            
            for (int i = playerData.statusEffects.Count - 1; i >= 0; i--)
            {
                var effect = playerData.statusEffects[i];
                effect.duration--;
                
                if (effect.duration <= 0)
                {
                    playerData.statusEffects.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 应用资源变化
        /// </summary>
        private void ApplyResourceChanges(PlayerData playerData)
        {
            // 通胀效应
            float inflationRate = 0.01f * (currentDay / 30f); // 随时间增加通胀率
            float currentCredits = playerData.resources.credits;
            float inflationLoss = currentCredits * inflationRate;
            playerData.resources.ConsumeResource(ResourceType.Credits, inflationLoss);
            
            // 社会积分衰减
            if (playerData.resources.socialScore > 0)
            {
                float decay = playerData.resources.socialScore * 0.02f;
                playerData.resources.ConsumeResource(ResourceType.SocialScore, decay);
            }
        }

        /// <summary>
        /// 应用阶段效果
        /// </summary>
        private void ApplyPhaseEffects(PlayerData playerData)
        {
            var phaseConfig = phaseConfigs[currentPhase];
            
            foreach (var effect in phaseConfig.dailyEffects)
            {
                ApplyPhaseEffect(playerData, effect);
            }
        }

        /// <summary>
        /// 应用单个阶段效果
        /// </summary>
        private void ApplyPhaseEffect(PlayerData playerData, PhaseEffect effect)
        {
            switch (effect.type)
            {
                case PhaseEffectType.AttributeChange:
                    playerData.attributes.ModifyAttribute(effect.target, effect.value);
                    break;
                
                case PhaseEffectType.ResourceChange:
                    if (effect.value > 0)
                    {
                        playerData.resources.AddResource(effect.resourceType.Value, effect.value);
                    }
                    else
                    {
                        playerData.resources.ConsumeResource(effect.resourceType.Value, -effect.value);
                    }
                    break;
                
                case PhaseEffectType.RandomEvent:
                    if (UnityEngine.Random.Range(0f, 1f) < effect.value)
                    {
                        TriggerRandomEvent(playerData, effect.target);
                    }
                    break;
            }
        }

        /// <summary>
        /// 触发每日事件
        /// </summary>
        private void TriggerDailyEvents(PlayerData playerData)
        {
            var phaseConfig = phaseConfigs[currentPhase];
            
            // 根据阶段配置触发相应的事件
            if (UnityEngine.Random.Range(0f, 1f) < phaseConfig.dailyEventChance)
            {
                TriggerPhaseEvent(playerData);
            }
        }

        /// <summary>
        /// 触发阶段事件
        /// </summary>
        private void TriggerPhaseEvent(PlayerData playerData)
        {
            switch (currentPhase)
            {
                case GamePhase.EarlyStruggle:
                    TriggerEarlyStruggleEvent(playerData);
                    break;
                
                case GamePhase.SystemIntegration:
                    TriggerSystemIntegrationEvent(playerData);
                    break;
                
                case GamePhase.Awakening:
                    TriggerAwakeningEvent(playerData);
                    break;
                
                case GamePhase.FinalChoice:
                    TriggerFinalChoiceEvent(playerData);
                    break;
            }
        }

        /// <summary>
        /// 检查阶段推进
        /// </summary>
        private void CheckPhaseProgression(PlayerData playerData)
        {
            var nextPhase = DetermineNextPhase(playerData);
            
            if (nextPhase != currentPhase)
            {
                var oldPhase = currentPhase;
                currentPhase = nextPhase;
                OnPhaseChanged?.Invoke(oldPhase, nextPhase);
                
                Debug.Log($"游戏阶段推进：{oldPhase} -> {nextPhase}");
            }
        }

        /// <summary>
        /// 确定下一个阶段
        /// </summary>
        private GamePhase DetermineNextPhase(PlayerData playerData)
        {
            switch (currentPhase)
            {
                case GamePhase.Tutorial:
                    if (playerData.totalBoxesOpened >= 5)
                        return GamePhase.EarlyStruggle;
                    break;
                
                case GamePhase.EarlyStruggle:
                    if (playerData.daysSurvived >= 20 && playerData.attributes.dependence >= 40f)
                        return GamePhase.SystemIntegration;
                    break;
                
                case GamePhase.SystemIntegration:
                    if (playerData.attributes.cognition >= 70f || playerData.hasMetResistance)
                        return GamePhase.Awakening;
                    break;
                
                case GamePhase.Awakening:
                    if (playerData.knowsTruth || playerData.daysSurvived >= 100)
                        return GamePhase.FinalChoice;
                    break;
            }
            
            return currentPhase;
        }

        /// <summary>
        /// 检查里程碑
        /// </summary>
        private void CheckMilestones(PlayerData playerData)
        {
            foreach (var milestone in milestones)
            {
                if (!milestone.achieved && CheckMilestoneCondition(playerData, milestone))
                {
                    milestone.achieved = true;
                    milestone.achievedDay = currentDay;
                    OnMilestoneReached?.Invoke(milestone);
                    
                    // 应用里程碑奖励
                    ApplyMilestoneReward(playerData, milestone);
                }
            }
        }

        /// <summary>
        /// 检查生存威胁
        /// </summary>
        private void CheckSurvivalThreats(PlayerData playerData)
        {
            foreach (var check in survivalChecks)
            {
                if (CheckSurvivalCondition(playerData, check))
                {
                    var threat = new SurvivalThreat
                    {
                        type = check.threatType,
                        severity = check.severity,
                        description = check.description,
                        timeLimit = check.timeLimit
                    };
                    
                    OnSurvivalThreat?.Invoke(threat);
                }
            }
        }

        /// <summary>
        /// 检查游戏结束条件
        /// </summary>
        private void CheckGameEndConditions(PlayerData playerData)
        {
            // 死亡条件
            if (playerData.attributes.health <= 0)
            {
                EndGame(GameEndType.Death, "生命值耗尽，游戏结束", playerData);
                return;
            }

            // 系统同化条件
            if (playerData.attributes.dependence >= 100f)
            {
                EndGame(GameEndType.SystemAssimilation, "完全依赖系统，失去自我意识", playerData);
                return;
            }

            // 数据损坏条件
            if (playerData.attributes.pollution >= 100f)
            {
                EndGame(GameEndType.DataCorruption, "数据污染过度，意识崩溃", playerData);
                return;
            }
            
            // 特殊结局条件
            // 楚门结局：连续30天不开盒
            if (HasNotOpenedBoxesForDays(playerData, 30))
            {
                EndGame(GameEndType.Transcendence, "你拒绝了系统的诱惑，发现了真相的一角", playerData);
                return;
            }

            // 终极幸存者：生存200天且保持相对平衡
            if (playerData.daysSurvived >= 200 &&
                playerData.attributes.humanity > 50f &&
                playerData.attributes.dependence < 70f)
            {
                EndGame(GameEndType.Sacrifice, "在极端环境中保持了人性，成为真正的幸存者", playerData);
                return;
            }

            // 反抗结局：收集足够的系统代码片段
            if (HasCollectedSystemCodes(playerData, 30))
            {
                EndGame(GameEndType.Rebellion, "成功破解了潘多拉系统，引发了觉醒革命", playerData);
                return;
            }
        }


        /// <summary>
        /// 结束游戏
        /// </summary>
        private void EndGame(GameEndType endType, string message, PlayerData playerData = null)
        {
            isGameActive = false;

            // 通知元游戏管理器
            if (MetaGameSystem.Instance != null && playerData != null)
            {
                MetaGameSystem.Instance.EndGame(endType, playerData);
            }

            OnGameEnded?.Invoke(endType, message);
            Debug.Log($"游戏结束：{endType} - {message}");
        }

        /// <summary>
        /// 确定当前阶段
        /// </summary>
        private void DetermineCurrentPhase(PlayerData playerData)
        {
            if (playerData.daysSurvived == 0)
            {
                currentPhase = GamePhase.Tutorial;
            }
            else if (playerData.daysSurvived < 20)
            {
                currentPhase = GamePhase.EarlyStruggle;
            }
            else if (!playerData.hasMetResistance && playerData.attributes.cognition < 70f)
            {
                currentPhase = GamePhase.SystemIntegration;
            }
            else if (!playerData.knowsTruth)
            {
                currentPhase = GamePhase.Awakening;
            }
            else
            {
                currentPhase = GamePhase.FinalChoice;
            }
        }

        // 辅助方法
        private void TriggerRandomEvent(PlayerData playerData, string eventId)
        {
            Debug.Log($"触发随机事件：{eventId}");

            // 根据事件ID触发不同的随机事件
            switch (eventId)
            {
                case "system_glitch":
                    TriggerSystemGlitchEvent(playerData);
                    break;
                case "resource_shortage":
                    TriggerResourceShortageEvent(playerData);
                    break;
                case "social_encounter":
                    TriggerSocialEncounterEvent(playerData);
                    break;
                case "mysterious_package":
                    TriggerMysteriousPackageEvent(playerData);
                    break;
                case "surveillance_notice":
                    TriggerSurveillanceNoticeEvent(playerData);
                    break;
                default:
                    TriggerGenericRandomEvent(playerData, eventId);
                    break;
            }
        }

        private void TriggerEarlyStruggleEvent(PlayerData playerData)
        {
            Debug.Log("触发早期挣扎阶段事件");

            // 早期阶段的困难事件
            var events = new string[]
            {
                "food_shortage", "shelter_problem", "health_crisis", "credit_debt"
            };

            var randomEvent = events[UnityEngine.Random.Range(0, events.Length)];

            switch (randomEvent)
            {
                case "food_shortage":
                    playerData.attributes.ModifyAttribute(AttributeType.Health, -10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Energy, -15f);
                    UIManager.Instance.ShowEventDialog("食物短缺", "你的食物储备不足，健康和精力下降。");
                    break;

                case "shelter_problem":
                    playerData.attributes.ModifyAttribute(AttributeType.Health, -5f);
                    playerData.attributes.ModifyAttribute(AttributeType.Morality, -5f);
                    UIManager.Instance.ShowEventDialog("住所问题", "居住环境恶化，影响了你的身心健康。");
                    break;

                case "health_crisis":
                    playerData.attributes.ModifyAttribute(AttributeType.Health, -20f);
                    playerData.resources.AddResource(ResourceType.Credits, -50f);
                    UIManager.Instance.ShowEventDialog("健康危机", "突发疾病让你花费了大量医疗费用。");
                    break;

                case "credit_debt":
                    playerData.resources.AddResource(ResourceType.Credits, -100f);
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, 10f);
                    UIManager.Instance.ShowEventDialog("信用债务", "债务压力迫使你更加依赖系统。");
                    break;
            }
        }

        private void TriggerSystemIntegrationEvent(PlayerData playerData)
        {
            Debug.Log("触发系统整合阶段事件");

            // 系统整合阶段的事件
            var events = new string[]
            {
                "system_integration", "data_merge", "consciousness_sync", "identity_blur"
            };

            var randomEvent = events[UnityEngine.Random.Range(0, events.Length)];

            switch (randomEvent)
            {
                case "system_integration":
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, 20f);
                    playerData.attributes.ModifyAttribute(AttributeType.Humanity, -15f);
                    UIManager.Instance.ShowEventDialog("系统整合", "你的意识开始与系统融合。");
                    break;

                case "data_merge":
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, -10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Pollution, 15f);
                    UIManager.Instance.ShowEventDialog("数据融合", "你的记忆与系统数据开始混合。");
                    break;

                case "consciousness_sync":
                    playerData.attributes.ModifyAttribute(AttributeType.Morality, -20f);
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, 25f);
                    UIManager.Instance.ShowEventDialog("意识同步", "你的思维模式正在被系统重塑。");
                    break;

                case "identity_blur":
                    playerData.attributes.ModifyAttribute(AttributeType.Humanity, -25f);
                    playerData.SetSpecialFlag("identity_crisis", true);
                    UIManager.Instance.ShowEventDialog("身份模糊", "你开始分不清自己和系统的界限。");
                    break;
            }
        }

        private void TriggerAwakeningEvent(PlayerData playerData)
        {
            Debug.Log("触发觉醒阶段事件");

            // 觉醒阶段的关键事件
            var events = new string[]
            {
                "system_revelation", "resistance_contact", "truth_fragment", "moral_dilemma"
            };

            var randomEvent = events[UnityEngine.Random.Range(0, events.Length)];

            switch (randomEvent)
            {
                case "system_revelation":
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, 15f);
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, -10f);
                    UIManager.Instance.ShowEventDialog("系统真相", "你发现了系统操控的证据，认知大幅提升。");

                    // 触发元游戏真相发现
                    if (MetaGameSystem.Instance != null)
                    {
                        MetaGameSystem.Instance.DiscoverTruth("system_manipulation_exists");
                    }
                    break;

                case "resistance_contact":
                    playerData.attributes.ModifyAttribute(AttributeType.Social, 10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Humanity, 10f);
                    UIManager.Instance.ShowEventDialog("抵抗接触", "你遇到了其他觉醒者，获得了宝贵的支持。");
                    break;

                case "truth_fragment":
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, 10f);
                    playerData.SetSpecialFlag("truth_seeker", true);
                    UIManager.Instance.ShowEventDialog("真相碎片", "你找到了一个重要的真相碎片。");
                    break;

                case "moral_dilemma":
                    // 这里可以触发选择对话
                    UIManager.Instance.ShowEventDialog("道德困境", "你面临一个艰难的道德选择...");
                    break;
            }
        }

        private void TriggerFinalChoiceEvent(PlayerData playerData)
        {
            Debug.Log("触发最终选择阶段事件");

            // 最终阶段的关键选择事件
            var events = new string[]
            {
                "system_offer", "rebellion_call", "transcendence_path", "sacrifice_choice"
            };

            var randomEvent = events[UnityEngine.Random.Range(0, events.Length)];

            switch (randomEvent)
            {
                case "system_offer":
                    UIManager.Instance.ShowEventDialog("系统提议",
                        "系统向你提供了一个诱人的交易：放弃抵抗，获得特权地位。");
                    // 可以设置选择标志，影响结局
                    playerData.SetSpecialFlag("received_system_offer", true);
                    break;

                case "rebellion_call":
                    UIManager.Instance.ShowEventDialog("反抗召唤",
                        "抵抗组织需要你的帮助来推翻系统。这是你的机会。");
                    playerData.SetSpecialFlag("rebellion_opportunity", true);
                    break;

                case "transcendence_path":
                    UIManager.Instance.ShowEventDialog("超越之路",
                        "你发现了一条超越系统控制的道路，但代价巨大。");
                    playerData.SetSpecialFlag("transcendence_available", true);
                    break;

                case "sacrifice_choice":
                    UIManager.Instance.ShowEventDialog("牺牲选择",
                        "你可以牺牲自己来拯救其他人，这将是最终的觉醒。");
                    playerData.SetSpecialFlag("sacrifice_option", true);
                    break;
            }
        }

        private bool CheckMilestoneCondition(PlayerData playerData, GameMilestone milestone)
        {
            // 简化的条件检查，基于现有的condition字符串
            if (string.IsNullOrEmpty(milestone.condition))
                return false;

            // 解析条件字符串
            if (milestone.condition.Contains("daysSurvived"))
            {
                var parts = milestone.condition.Split(' ');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float value))
                {
                    return playerData.daysSurvived >= value;
                }
            }
            else if (milestone.condition.Contains("totalBoxesOpened"))
            {
                var parts = milestone.condition.Split(' ');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float value))
                {
                    return playerData.totalBoxesOpened >= value;
                }
            }
            else if (milestone.condition.Contains("credits"))
            {
                var parts = milestone.condition.Split(' ');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float value))
                {
                    return playerData.resources.credits >= value;
                }
            }
            else if (milestone.condition.Contains("socialClass"))
            {
                return playerData.socialClass >= SocialClass.Worker;
            }

            return false;
        }

        private void ApplyMilestoneReward(PlayerData playerData, GameMilestone milestone)
        {
            Debug.Log($"获得里程碑奖励：{milestone.name}");

            // 应用奖励
            if (milestone.rewards != null)
            {
                foreach (var reward in milestone.rewards)
                {
                    switch (reward.type)
                    {
                        case "resource":
                            if (reward.target == "credits")
                            {
                                playerData.resources.AddResource(ResourceType.Credits, reward.value);
                            }
                            else if (reward.target == "diamond")
                            {
                                playerData.resources.AddResource(ResourceType.Diamond, reward.value);
                            }
                            break;

                        case "attribute":
                            playerData.attributes.ModifyAttribute(reward.target, reward.value);
                            break;

                        case "unlock":
                            playerData.SetSpecialFlag(reward.target, true);
                            break;
                    }
                }
            }

            // 显示奖励UI
            UIManager.Instance.ShowEventDialog($"里程碑达成",
                $"恭喜！你达成了里程碑：{milestone.name}\n{milestone.description}");
        }

        private bool CheckSurvivalCondition(PlayerData playerData, SurvivalCheck check)
        {
            // 基于现有的condition字符串进行检查
            if (string.IsNullOrEmpty(check.condition))
                return false;

            // 解析条件字符串
            if (check.condition.Contains("health < "))
            {
                var parts = check.condition.Split(' ');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float value))
                {
                    return playerData.attributes.health < value;
                }
            }
            else if (check.condition.Contains("credits < "))
            {
                var parts = check.condition.Split(' ');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float value))
                {
                    return playerData.resources.credits < value;
                }
            }
            else if (check.condition.Contains("dependence > "))
            {
                var parts = check.condition.Split(' ');
                if (parts.Length >= 3 && float.TryParse(parts[2], out float value))
                {
                    return playerData.attributes.dependence > value;
                }
            }

            return false;
        }



        private bool HasNotOpenedBoxesForDays(PlayerData playerData, int days)
        {
            // 检查连续不开盒天数
            // 这里需要在PlayerData中添加lastBoxOpenDay字段来追踪
            float lastBoxOpenDay = playerData.GetCounter("last_box_open_day");
            int daysSinceLastBox = playerData.daysSurvived - (int)lastBoxOpenDay;
            return daysSinceLastBox >= days;
        }

        /// <summary>
        /// 系统故障事件
        /// </summary>
        private void TriggerSystemGlitchEvent(PlayerData playerData)
        {
            playerData.attributes.ModifyAttribute(AttributeType.Cognition, 5f);
            playerData.attributes.ModifyAttribute(AttributeType.Dependence, -5f);
            playerData.AddToCounter("system_glitches_witnessed", 1f);

            UIManager.Instance.ShowEventDialog("系统故障",
                "你目睹了一次系统故障，这让你对系统的完美性产生了怀疑。");

            // 可能触发真相发现
            if (playerData.GetCounter("system_glitches_witnessed") >= 3)
            {
                if (MetaGameSystem.Instance != null)
                {
                    MetaGameSystem.Instance.DiscoverTruth("system_vulnerability");
                }
            }
        }

        /// <summary>
        /// 资源短缺事件
        /// </summary>
        private void TriggerResourceShortageEvent(PlayerData playerData)
        {
            var shortageType = UnityEngine.Random.Range(0, 3);

            switch (shortageType)
            {
                case 0: // 食物短缺
                    playerData.attributes.ModifyAttribute(AttributeType.Health, -15f);
                    playerData.attributes.ModifyAttribute(AttributeType.Energy, -10f);
                    UIManager.Instance.ShowEventDialog("食物短缺", "食物供应中断，你的健康状况受到影响。");
                    break;

                case 1: // 水源污染
                    playerData.attributes.ModifyAttribute(AttributeType.Health, -10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Pollution, 10f);
                    UIManager.Instance.ShowEventDialog("水源污染", "水源被污染，你被迫饮用不洁净的水。");
                    break;

                case 2: // 能源危机
                    playerData.attributes.ModifyAttribute(AttributeType.Energy, -20f);
                    playerData.attributes.ModifyAttribute(AttributeType.Social, -5f);
                    UIManager.Instance.ShowEventDialog("能源危机", "能源短缺影响了整个社区的正常运转。");
                    break;
            }
        }

        /// <summary>
        /// 社交遭遇事件
        /// </summary>
        private void TriggerSocialEncounterEvent(PlayerData playerData)
        {
            var encounterType = UnityEngine.Random.Range(0, 4);

            switch (encounterType)
            {
                case 0: // 遇到觉醒者
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, 10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Humanity, 5f);
                    playerData.SetSpecialFlag("met_awakened_person", true);
                    UIManager.Instance.ShowEventDialog("觉醒者", "你遇到了一个已经觉醒的人，他分享了一些重要信息。");
                    break;

                case 1: // 遇到系统代理人
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, 10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Social, 5f);
                    UIManager.Instance.ShowEventDialog("系统代理人", "一个系统代理人试图说服你更好地融入系统。");
                    break;

                case 2: // 遇到反抗者
                    playerData.attributes.ModifyAttribute(AttributeType.Morality, 10f);
                    playerData.attributes.ModifyAttribute(AttributeType.Humanity, 10f);
                    playerData.SetSpecialFlag("met_rebel", true);
                    UIManager.Instance.ShowEventDialog("反抗者", "你遇到了一个反抗系统的人，他的话让你深思。");
                    break;

                case 3: // 遇到普通人
                    playerData.attributes.ModifyAttribute(AttributeType.Social, 5f);
                    UIManager.Instance.ShowEventDialog("普通遭遇", "你与一个普通人进行了愉快的交谈。");
                    break;
            }
        }

        /// <summary>
        /// 神秘包裹事件
        /// </summary>
        private void TriggerMysteriousPackageEvent(PlayerData playerData)
        {
            var packageType = UnityEngine.Random.Range(0, 3);

            switch (packageType)
            {
                case 0: // 系统代码片段
                    playerData.AddToCounter("system_codes_collected", 1f);
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, 8f);
                    UIManager.Instance.ShowEventDialog("神秘包裹", "包裹中包含一个系统代码片段，这可能很重要。");
                    break;

                case 1: // 追踪设备
                    playerData.attributes.ModifyAttribute(AttributeType.Dependence, 15f);
                    playerData.SetSpecialFlag("being_tracked", true);
                    UIManager.Instance.ShowEventDialog("神秘包裹", "包裹中的设备似乎在追踪你的行为。");
                    break;

                case 2: // 真相文档
                    playerData.attributes.ModifyAttribute(AttributeType.Cognition, 15f);
                    playerData.attributes.ModifyAttribute(AttributeType.Humanity, 5f);
                    UIManager.Instance.ShowEventDialog("神秘包裹", "包裹中的文档揭示了一些令人震惊的真相。");
                    if (MetaGameSystem.Instance != null)
                    {
                        MetaGameSystem.Instance.DiscoverTruth("systematic_fraud");
                    }
                    break;
            }
        }

        /// <summary>
        /// 监控通知事件
        /// </summary>
        private void TriggerSurveillanceNoticeEvent(PlayerData playerData)
        {
            playerData.attributes.ModifyAttribute(AttributeType.Dependence, 5f);
            playerData.attributes.ModifyAttribute(AttributeType.Morality, -5f);
            playerData.SetSpecialFlag("under_surveillance", true);

            UIManager.Instance.ShowEventDialog("监控通知",
                "你收到通知：你的行为正在被系统监控。请保持合规。");

            // 增加监控检测计数
            playerData.AddToCounter("surveillance_notices", 1f);
        }

        /// <summary>
        /// 通用随机事件
        /// </summary>
        private void TriggerGenericRandomEvent(PlayerData playerData, string eventId)
        {
            // 处理其他未定义的事件
            Debug.Log($"触发通用事件: {eventId}");

            // 随机小幅度属性变化
            var attributes = new AttributeType[] { AttributeType.Health, AttributeType.Energy, AttributeType.Social, AttributeType.Luck };
            var randomAttr = attributes[UnityEngine.Random.Range(0, attributes.Length)];
            var randomValue = UnityEngine.Random.Range(-5f, 10f);

            playerData.attributes.ModifyAttribute(randomAttr, randomValue);

            // 获取属性显示名称
            string attrDisplayName = randomAttr switch
            {
                AttributeType.Health => "生命值",
                AttributeType.Energy => "精力值",
                AttributeType.Social => "社交值",
                AttributeType.Luck => "幸运值",
                _ => randomAttr.ToString()
            };

            UIManager.Instance.ShowEventDialog("随机事件",
                $"发生了一个意外事件，影响了你的{attrDisplayName}。");
        }

        private bool HasCollectedSystemCodes(PlayerData playerData, int requiredCount)
        {
            // 检查是否收集了足够的系统代码片段
            return playerData.unlockedEvents.FindAll(e => e.Contains("system_code")).Count >= requiredCount;
        }

        /// <summary>
        /// 初始化阶段配置
        /// </summary>
        private void InitializePhaseConfigs()
        {
            // 教程阶段
            phaseConfigs[GamePhase.Tutorial] = new PhaseConfig
            {
                name = "初入盲盒世界",
                dailyEnergyCost = 5f,
                dailyHealthCost = 1f,
                dailyDependenceIncrease = 1f,
                dailyEventChance = 0.1f,
                dailyEffects = new List<PhaseEffect>()
            };

            // 早期挣扎阶段
            phaseConfigs[GamePhase.EarlyStruggle] = new PhaseConfig
            {
                name = "生存挣扎",
                dailyEnergyCost = 8f,
                dailyHealthCost = 2f,
                dailyDependenceIncrease = 2f,
                dailyEventChance = 0.2f,
                dailyEffects = new List<PhaseEffect>
                {
                    new PhaseEffect(PhaseEffectType.AttributeChange, "luck", -0.1f)
                }
            };

            // 系统整合阶段
            phaseConfigs[GamePhase.SystemIntegration] = new PhaseConfig
            {
                name = "系统整合",
                dailyEnergyCost = 6f,
                dailyHealthCost = 1f,
                dailyDependenceIncrease = 3f,
                dailyEventChance = 0.15f,
                dailyEffects = new List<PhaseEffect>
                {
                    new PhaseEffect(PhaseEffectType.AttributeChange, "pollution", 0.5f)
                }
            };

            // 觉醒阶段
            phaseConfigs[GamePhase.Awakening] = new PhaseConfig
            {
                name = "觉醒时刻",
                dailyEnergyCost = 10f,
                dailyHealthCost = 3f,
                dailyDependenceIncrease = 1f,
                dailyEventChance = 0.3f,
                dailyEffects = new List<PhaseEffect>
                {
                    new PhaseEffect(PhaseEffectType.AttributeChange, "cognition", 0.5f)
                }
            };

            // 最终选择阶段
            phaseConfigs[GamePhase.FinalChoice] = new PhaseConfig
            {
                name = "最终抉择",
                dailyEnergyCost = 15f,
                dailyHealthCost = 5f,
                dailyDependenceIncrease = 0f,
                dailyEventChance = 0.4f,
                dailyEffects = new List<PhaseEffect>()
            };
        }

        /// <summary>
        /// 初始化里程碑
        /// </summary>
        private void InitializeMilestones()
        {
            milestones.Add(new GameMilestone
            {
                id = "first_week",
                name = "初来乍到",
                description = "生存第一周",
                condition = "daysSurvived >= 7"
            });

            milestones.Add(new GameMilestone
            {
                id = "box_addict",
                name = "盲盒成瘾者",
                description = "开启100个盲盒",
                condition = "totalBoxesOpened >= 100"
            });

            milestones.Add(new GameMilestone
            {
                id = "social_climber",
                name = "社会攀登者",
                description = "晋升到工蜂阶层",
                condition = "socialClass >= Worker"
            });
        }

        /// <summary>
        /// 初始化生存检查
        /// </summary>
        private void InitializeSurvivalChecks()
        {
            survivalChecks.Add(new SurvivalCheck
            {
                threatType = SurvivalThreatType.HealthCrisis,
                condition = "health < 20",
                severity = ThreatSeverity.High,
                description = "健康状况危急",
                timeLimit = 3
            });

            survivalChecks.Add(new SurvivalCheck
            {
                threatType = SurvivalThreatType.ResourceDepletion,
                condition = "credits < 10",
                severity = ThreatSeverity.Medium,
                description = "资源即将耗尽",
                timeLimit = 5
            });
        }

        public void Dispose()
        {
            phaseConfigs?.Clear();
            milestones?.Clear();
            survivalChecks?.Clear();
        }
    }

    /// <summary>
    /// 游戏阶段
    /// </summary>
    public enum GamePhase
    {
        Tutorial,           // 教程阶段
        EarlyStruggle,      // 早期挣扎
        SystemIntegration,  // 系统整合
        Awakening,          // 觉醒时刻
        FinalChoice         // 最终抉择
    }

    /// <summary>
    /// 阶段配置
    /// </summary>
    [Serializable]
    public class PhaseConfig
    {
        public string name;
        public float dailyEnergyCost;
        public float dailyHealthCost;
        public float dailyDependenceIncrease;
        public float dailyEventChance;
        public List<PhaseEffect> dailyEffects;

        public PhaseConfig()
        {
            dailyEffects = new List<PhaseEffect>();
        }
    }

    /// <summary>
    /// 阶段效果
    /// </summary>
    [Serializable]
    public class PhaseEffect
    {
        public PhaseEffectType type;
        public string target;
        public float value;
        public ResourceType? resourceType;

        public PhaseEffect(PhaseEffectType type, string target, float value)
        {
            this.type = type;
            this.target = target;
            this.value = value;
        }

        public PhaseEffect(PhaseEffectType type, ResourceType resourceType, float value)
        {
            this.type = type;
            this.resourceType = resourceType;
            this.value = value;
        }
    }

    /// <summary>
    /// 游戏里程碑
    /// </summary>
    [Serializable]
    public class GameMilestone
    {
        public string id;
        public string name;
        public string description;
        public string condition;
        public bool achieved;
        public int achievedDay;
        public List<MilestoneReward> rewards;

        public GameMilestone()
        {
            rewards = new List<MilestoneReward>();
        }
    }

    /// <summary>
    /// 里程碑奖励
    /// </summary>
    [Serializable]
    public class MilestoneReward
    {
        public string type;     // "resource", "attribute", "talent", "unlock"
        public string target;
        public float value;
    }

    /// <summary>
    /// 生存检查
    /// </summary>
    [Serializable]
    public class SurvivalCheck
    {
        public SurvivalThreatType threatType;
        public string condition;
        public ThreatSeverity severity;
        public string description;
        public int timeLimit;   // 天数
    }

    /// <summary>
    /// 生存威胁
    /// </summary>
    [Serializable]
    public class SurvivalThreat
    {
        public SurvivalThreatType type;
        public ThreatSeverity severity;
        public string description;
        public int timeLimit;
        public DateTime detectedTime;

        public SurvivalThreat()
        {
            detectedTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 阶段效果类型
    /// </summary>
    public enum PhaseEffectType
    {
        AttributeChange,    // 属性变化
        ResourceChange,     // 资源变化
        RandomEvent        // 随机事件
    }

    /// <summary>
    /// 生存威胁类型
    /// </summary>
    public enum SurvivalThreatType
    {
        HealthCrisis,       // 健康危机
        ResourceDepletion,  // 资源枯竭
        SystemPurge,        // 系统清洗
        MentalBreakdown,    // 精神崩溃
        SocialExclusion     // 社会排斥
    }

    /// <summary>
    /// 威胁严重程度
    /// </summary>
    public enum ThreatSeverity
    {
        Low,        // 低
        Medium,     // 中
        High,       // 高
        Critical    // 危急
    }
}
