using UnityEngine;
using BoxOfFate.Game;
using System.Collections.Generic;

/// <summary>
/// 综合测试脚本 - 测试所有游戏系统的集成
/// </summary>
public class ComprehensiveTest : MonoBehaviour
{
    [Header("测试设置")]
    public bool runTestsOnStart = true;
    public bool enableDetailedLogs = true;
    public string testScenarioId = "mid_game";

    private GameLogic gameLogic;

    private void Start()
    {
        if (runTestsOnStart)
        {
            StartCoroutine(RunComprehensiveTests());
        }
    }

    /// <summary>
    /// 运行综合测试
    /// </summary>
    private System.Collections.IEnumerator RunComprehensiveTests()
    {
        Debug.Log("=== 开始综合系统测试 ===");

        // 等待一帧确保所有系统初始化完成
        yield return null;

        // 获取GameLogic实例
        gameLogic = GameLogic.Instance;
        if (gameLogic == null)
        {
            Debug.LogError("GameLogic实例未找到！请确保场景中有GameLogic组件。");
            yield break;
        }

        // 测试基础游戏流程
        TestBasicGameFlow();
        yield return new WaitForSeconds(1f);

        // 测试工作系统
        TestWorkSystem();
        yield return new WaitForSeconds(1f);

        // 测试特权系统
        TestPrivilegeSystem();
        yield return new WaitForSeconds(1f);

        // 测试黑市系统
        TestBlackMarketSystem();
        yield return new WaitForSeconds(1f);

        // 测试成就系统
        TestAchievementSystem();
        yield return new WaitForSeconds(1f);

        // 测试数据系统
        TestDataSystem();
        yield return new WaitForSeconds(1f);

        // 测试完整游戏循环
        TestCompleteGameLoop();

        Debug.Log("=== 综合系统测试完成 ===");
    }

    /// <summary>
    /// 测试基础游戏流程
    /// </summary>
    private void TestBasicGameFlow()
    {
        Debug.Log("--- 测试基础游戏流程 ---");

        // 开始新游戏
        gameLogic.StartNewGame();
        LogPlayerStatus("新游戏开始后");

        // 开启几个盲盒
        for (int i = 0; i < 3; i++)
        {
            var result = gameLogic.OpenBlindBox(BlindBoxType.Basic);
            if (result != null)
            {
                Debug.Log($"开盒结果: {result.item.name} (价值: {result.item.value})");
            }
        }

        // 尝试晋升
        var promotionResult = gameLogic.AttemptPromotion(SocialClass.Worker);
        if (promotionResult != null)
        {
            Debug.Log($"晋升结果: {(promotionResult.success ? "成功" : "失败")} - {(promotionResult.success ? promotionResult.successMessage : promotionResult.failureReason)}");
        }

        LogPlayerStatus("基础流程测试后");
    }

    /// <summary>
    /// 测试工作系统
    /// </summary>
    private void TestWorkSystem()
    {
        Debug.Log("--- 测试工作系统 ---");

        // 测试不同类型的工作
        var jobTypes = new[] { JobType.DataEntry, JobType.BoxSorting, JobType.SystemMaintenance };

        foreach (var jobType in jobTypes)
        {
            var workResult = gameLogic.StartWork(jobType, 4f); // 工作4小时
            if (workResult != null)
            {
                Debug.Log($"工作结果 ({jobType}): {(workResult.success ? "成功" : "失败")}");
                if (workResult.success)
                {
                    Debug.Log($"  获得信用点: {workResult.creditsEarned:F0}");
                    Debug.Log($"  工作效率: {workResult.efficiency:F2}");
                    Debug.Log($"  消耗精力: {workResult.energyConsumed:F0}");
                }
            }
        }

        LogPlayerStatus("工作系统测试后");
    }

    /// <summary>
    /// 测试特权系统
    /// </summary>
    private void TestPrivilegeSystem()
    {
        Debug.Log("--- 测试特权系统 ---");

        // 测试不同特权
        var privileges = new[] { "skip_queue", "probability_hint", "resource_bonus" };

        foreach (var privilege in privileges)
        {
            var result = gameLogic.UsePrivilege(privilege);
            if (result != null)
            {
                Debug.Log($"特权使用 ({privilege}): {(result.success ? "成功" : "失败")}");
                if (result.success)
                {
                    Debug.Log($"  效果: {result.message}");
                }
                else
                {
                    Debug.Log($"  失败原因: {result.errorMessage}");
                }
            }
        }

        LogPlayerStatus("特权系统测试后");
    }

    /// <summary>
    /// 测试黑市系统
    /// </summary>
    private void TestBlackMarketSystem()
    {
        Debug.Log("--- 测试黑市系统 ---");

        var blackMarketAccess = gameLogic.AccessBlackMarket();
        if (blackMarketAccess != null)
        {
            Debug.Log($"黑市访问: {(blackMarketAccess.canAccess ? "成功" : "失败")}");
            if (blackMarketAccess.canAccess)
            {
                Debug.Log($"  可用交易数量: {blackMarketAccess.availableListings.Count}");
                Debug.Log($"  当前风险等级: {blackMarketAccess.currentRiskLevel:F2}");
                Debug.Log($"  玩家声誉: {blackMarketAccess.playerReputation:F0}");
            }
            else
            {
                Debug.Log($"  访问被拒原因: {blackMarketAccess.reason}");
            }
        }

        LogPlayerStatus("黑市系统测试后");
    }

    /// <summary>
    /// 测试成就系统
    /// </summary>
    private void TestAchievementSystem()
    {
        Debug.Log("--- 测试成就系统 ---");

        var achievements = gameLogic.GetAchievements();
        Debug.Log($"总成就数量: {achievements.Count}");

        int unlockedCount = 0;
        foreach (var achievement in achievements)
        {
            if (achievement.isUnlocked)
            {
                unlockedCount++;
                Debug.Log($"已解锁成就: {achievement.name} - {achievement.description}");
            }
        }

        Debug.Log($"已解锁成就数量: {unlockedCount}/{achievements.Count}");
        LogPlayerStatus("成就系统测试后");
    }

    /// <summary>
    /// 测试数据系统
    /// </summary>
    private void TestDataSystem()
    {
        Debug.Log("--- 测试数据系统 ---");

        // 保存和加载测试
        gameLogic.SaveGame();
        Debug.Log("游戏已保存");

        bool loadSuccess = gameLogic.LoadGame();
        Debug.Log($"游戏加载: {(loadSuccess ? "成功" : "失败")}");

        LogPlayerStatus("数据系统测试后");
    }

    /// <summary>
    /// 测试完整游戏循环
    /// </summary>
    private void TestCompleteGameLoop()
    {
        Debug.Log("--- 测试完整游戏循环 ---");

        // 模拟几天的游戏进程
        for (int day = 0; day < 3; day++)
        {
            Debug.Log($"模拟第{day + 1}天:");

            // 每日更新
            gameLogic.ProcessDailyUpdate();

            // 随机活动
            if (Random.Range(0f, 1f) < 0.5f)
            {
                gameLogic.OpenBlindBox(BlindBoxType.Basic);
            }

            if (Random.Range(0f, 1f) < 0.3f)
            {
                gameLogic.StartWork(JobType.DataEntry, 8f);
            }

            LogPlayerStatus($"第{day + 1}天结束后");
        }

        // 获取游戏统计
        var stats = gameLogic.GetGameStatistics();
        Debug.Log($"游戏统计:");
        Debug.Log($"  生存天数: {stats.daysSurvived}");
        Debug.Log($"  开盒总数: {stats.totalBoxesOpened}");
        Debug.Log($"  当前阶层: {stats.currentSocialClass}");
        Debug.Log($"  总信用点: {stats.totalCreditsEarned:F0}");
        Debug.Log($"  获得天赋: {stats.talentsAcquired}");
        Debug.Log($"  解锁成就: {stats.achievementsUnlocked}");
    }

    /// <summary>
    /// 记录玩家状态
    /// </summary>
    private void LogPlayerStatus(string context)
    {
        if (!enableDetailedLogs) return;

        var playerData = gameLogic.playerData;
        if (playerData == null) return;

        Debug.Log($"[{context}] 玩家状态:");
        Debug.Log($"  生命值: {playerData.attributes.health:F0}/100");
        Debug.Log($"  精力值: {playerData.attributes.energy:F0}/100");
        Debug.Log($"  幸运值: {playerData.attributes.luck:F1}/10");
        Debug.Log($"  社交值: {playerData.attributes.social:F0}/100");
        Debug.Log($"  依存度: {playerData.attributes.dependence:F0}/100");
        Debug.Log($"  认知值: {playerData.attributes.cognition:F0}/100");
        Debug.Log($"  污染度: {playerData.attributes.pollution:F0}/100");
        Debug.Log($"  人性值: {playerData.attributes.humanity:F0}/100");
        Debug.Log($"  道德值: {playerData.attributes.morality:F0}/100");
        Debug.Log($"  信用点: {playerData.resources.credits:F0}");
        Debug.Log($"  时间: {playerData.resources.time:F0}");
        Debug.Log($"  钻石: {playerData.resources.diamond:F0}");
        Debug.Log($"  社会积分: {playerData.resources.socialScore:F0}");
        Debug.Log($"  社会阶层: {playerData.socialClass}");
        Debug.Log($"  生存天数: {playerData.daysSurvived}");
        Debug.Log($"  开盒总数: {playerData.totalBoxesOpened}");
        Debug.Log($"  天赋数量: {playerData.talents.Count}");
        Debug.Log($"  状态效果数量: {playerData.statusEffects.Count}");
    }

    /// <summary>
    /// 在Inspector中显示测试按钮
    /// </summary>
    [ContextMenu("运行综合测试")]
    public void RunTestsFromInspector()
    {
        StartCoroutine(RunComprehensiveTests());
    }

    /// <summary>
    /// 重置游戏状态
    /// </summary>
    [ContextMenu("重置游戏状态")]
    public void ResetGameState()
    {
        if (gameLogic != null)
        {
            gameLogic.ResetGame();
            Debug.Log("游戏状态已重置");
        }
    }
}
