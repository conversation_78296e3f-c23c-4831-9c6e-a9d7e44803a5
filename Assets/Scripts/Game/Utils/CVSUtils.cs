using System.Collections.Generic;
using System.Text;
using BoxOfFate.Game.Core;
using UnityEngine;

/// <summary>
/// CSV数据导入工具
/// </summary>
public static class CVSUtils
{
    public static string[] ParseLine(string line, char spliter = ',')
    {
        return line.Split(spliter);
    }

    public static List<GameCondition> ParseContions(string conditionsStr)
    {
        var conditions = new List<GameCondition>();
        if (string.IsNullOrEmpty(conditionsStr)) return conditions;

        var parts = conditionsStr.Split(';');
        foreach (var part in parts)
        {
            var keyValue = part.Split(':');
            if (keyValue.Length >= 2)
            {
                var conditionType = keyValue[0];
                var value = keyValue[1];
                conditions.Add(new GameCondition(ParseConditionType(conditionType), float.Parse(value)));
            }
        }
        return conditions;
    }

    public static GameConditionType ParseConditionType(string conditionType)
    {
        switch (conditionType)
        {
            case "DaysSurvived": return GameConditionType.DaysSurvived;
            case "BoxesOpened": return GameConditionType.BoxesOpened;
            case "AttributeValue": return GameConditionType.AttributeThreshold;
            case "ResourceValue": return GameConditionType.ResourceThreshold;
            case "SocialClass": return GameConditionType.SocialClass;
            default: return GameConditionType.DaysSurvived;
        }
    }

    public static List<GameEffect> ParseEffects(string effectsStr)
    {
        var effects = new List<GameEffect>();
        if (string.IsNullOrEmpty(effectsStr)) return effects;

        var parts = effectsStr.Split(';');
        foreach (var part in parts)
        {
            var keyValue = part.Split(':');
            if (keyValue.Length >= 2)
            {
                var effectType = keyValue[0];
                var value = keyValue[1];
                effects.Add(new GameEffect(ParseEffectType(effectType), value, float.Parse(value)));
            }
        }
        return effects;
    }

    public static GameEffectType ParseEffectType(string effectType)
    {
        switch (effectType)
        {
            case "Resource": return GameEffectType.ResourceChange;
            case "Attribute": return GameEffectType.AttributeChange;
            case "Title": return GameEffectType.Title;
            case "UnlockEvent": return GameEffectType.UnlockEvent;
            default: return GameEffectType.ResourceChange;
        }
    }
}