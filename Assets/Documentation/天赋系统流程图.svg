<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2410.968994140625 1850.75" style="max-width: 2410.968994140625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92"><style>#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .error-icon{fill:#a44141;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edge-thickness-normal{stroke-width:1px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .marker.cross{stroke:lightgrey;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 p{margin:0;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .cluster-label text{fill:#F9FFFE;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .cluster-label span{color:#F9FFFE;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .cluster-label span p{background-color:transparent;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .label text,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 span{fill:#ccc;color:#ccc;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node rect,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node circle,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node ellipse,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node polygon,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .rough-node .label text,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node .label text,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .image-shape .label,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .icon-shape .label{text-anchor:middle;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .rough-node .label,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node .label,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .image-shape .label,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .icon-shape .label{text-align:center;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .node.clickable{cursor:pointer;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .arrowheadPath{fill:lightgrey;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .cluster text{fill:#F9FFFE;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .cluster span{color:#F9FFFE;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 rect.text{fill:none;stroke-width:0;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .icon-shape,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .icon-shape p,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .icon-shape rect,#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .startClass&gt;*{fill:#c8e6c9!important;stroke:#4caf50!important;stroke-width:3px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .startClass span{fill:#c8e6c9!important;stroke:#4caf50!important;stroke-width:3px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .processClass&gt;*{fill:#e3f2fd!important;stroke:#2196f3!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .processClass span{fill:#e3f2fd!important;stroke:#2196f3!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .decisionClass&gt;*{fill:#fff3e0!important;stroke:#ff9800!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .decisionClass span{fill:#fff3e0!important;stroke:#ff9800!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .effectClass&gt;*{fill:#f3e5f5!important;stroke:#9c27b0!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .effectClass span{fill:#f3e5f5!important;stroke:#9c27b0!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .uiClass&gt;*{fill:#ffebee!important;stroke:#f44336!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .uiClass span{fill:#ffebee!important;stroke:#f44336!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .dataClass&gt;*{fill:#e8f5e8!important;stroke:#4caf50!important;stroke-width:2px!important;}#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92 .dataClass span{fill:#e8f5e8!important;stroke:#4caf50!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Start_LoadData_0" d="M785.969,47L785.969,51.167C785.969,55.333,785.969,63.667,785.969,71.333C785.969,79,785.969,86,785.969,89.5L785.969,93"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LoadData_InitTalents_1" d="M785.969,151L785.969,155.167C785.969,159.333,785.969,167.667,785.969,175.333C785.969,183,785.969,190,785.969,193.5L785.969,197"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InitTalents_CheckLoop_2" d="M785.969,255L785.969,259.167C785.969,263.333,785.969,271.667,786.039,279.417C786.109,287.167,786.25,294.334,786.32,297.917L786.39,301.501"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckLoop_CheckAttr_3" d="M710.925,411.956L628.937,428.63C546.95,445.304,382.975,478.652,300.987,498.826C219,519,219,526,219,529.5L219,533"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckLoop_CheckResource_4" d="M719.407,420.438L676.339,435.699C633.271,450.959,547.136,481.479,504.068,500.24C461,519,461,526,461,529.5L461,533"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckLoop_CheckDays_5" d="M748.522,449.554L740.935,459.961C733.348,470.369,718.174,491.185,710.587,505.092C703,519,703,526,703,529.5L703,533"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckLoop_CheckBoxes_6" d="M850.881,423.087L886.901,437.906C922.921,452.725,994.96,482.362,1030.98,500.681C1067,519,1067,526,1067,529.5L1067,533"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckLoop_CheckEvents_7" d="M860.95,413.019L935.625,429.516C1010.3,446.013,1159.65,479.006,1234.325,499.003C1309,519,1309,526,1309,529.5L1309,533"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckAttr_AttrMet_8" d="M219,591L219,595.167C219,599.333,219,607.667,224.019,620.318C229.038,632.969,239.075,649.938,244.094,658.423L249.113,666.908"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckResource_ResMet_9" d="M461,591L461,595.167C461,599.333,461,607.667,466.019,620.318C471.038,632.969,481.075,649.938,486.094,658.423L491.113,666.908"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckDays_DaysMet_10" d="M703,591L703,595.167C703,599.333,703,607.667,708.019,620.318C713.038,632.969,723.075,649.938,728.094,658.423L733.113,666.908"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckBoxes_BoxesMet_11" d="M1067,591L1067,595.167C1067,599.333,1067,607.667,1072.019,620.318C1077.038,632.969,1087.075,649.938,1092.094,658.423L1097.113,666.908"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckEvents_EventMet_12" d="M1309,591L1309,595.167C1309,599.333,1309,607.667,1314.019,620.318C1319.038,632.969,1329.075,649.938,1334.094,658.423L1339.113,666.908"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AttrMet_UnlockTalent_13" d="M280,797.375L279.917,803.458C279.833,809.542,279.667,821.708,338.086,836.771C396.505,851.834,513.51,869.793,572.013,878.772L630.515,887.752"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ResMet_UnlockTalent_14" d="M522,797.375L521.917,803.458C521.833,809.542,521.667,821.708,539.785,834.45C557.904,847.191,594.308,860.507,612.51,867.165L630.712,873.823"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DaysMet_UnlockTalent_15" d="M764,797.375L763.917,803.458C763.833,809.542,763.667,821.708,757.607,833.498C751.547,845.288,739.594,856.7,733.617,862.406L727.641,868.113"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BoxesMet_UnlockTalent_16" d="M1128,797.375L1127.917,803.458C1127.833,809.542,1127.667,821.708,1066.738,836.826C1005.808,851.944,884.117,870.013,823.271,879.047L762.425,888.082"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EventMet_UnlockTalent_17" d="M1370,797.375L1369.917,803.458C1369.833,809.542,1369.667,821.708,1268.408,837.413C1167.15,853.117,964.801,872.359,863.626,881.98L762.451,891.601"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AttrMet_CheckLoop_18" d="M308.85,670.35L314.042,661.292C319.234,652.234,329.617,634.117,334.808,616.392C340,598.667,340,581.333,340,564C340,546.667,340,529.333,401.73,504.714C463.46,480.095,586.92,448.19,648.65,432.237L710.38,416.285"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ResMet_CheckLoop_19" d="M550.85,670.35L556.042,661.292C561.234,652.234,571.617,634.117,576.808,616.392C582,598.667,582,581.333,582,564C582,546.667,582,529.333,605.829,507.242C629.658,485.151,677.316,458.303,701.145,444.878L724.974,431.454"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DaysMet_CheckLoop_20" d="M813.831,691.331L835.859,678.776C857.887,666.221,901.944,641.11,923.972,619.888C946,598.667,946,581.333,946,564C946,546.667,946,529.333,928.745,508.181C911.489,487.029,876.978,462.058,859.723,449.572L842.467,437.087"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BoxesMet_CheckLoop_21" d="M1156.85,670.35L1162.042,661.292C1167.234,652.234,1177.617,634.117,1182.808,616.392C1188,598.667,1188,581.333,1188,564C1188,546.667,1188,529.333,1133.489,504.997C1078.979,480.661,969.957,449.321,915.447,433.652L860.936,417.982"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EventMet_CheckLoop_22" d="M1398.85,670.35L1404.042,661.292C1409.234,652.234,1419.617,634.117,1424.808,616.392C1430,598.667,1430,581.333,1430,564C1430,546.667,1430,529.333,1336.253,503.849C1242.506,478.365,1055.011,444.73,961.264,427.913L867.517,411.095"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UnlockTalent_ApplyEffects_23" d="M696.469,924.875L696.469,929.042C696.469,933.208,696.469,941.542,696.469,949.208C696.469,956.875,696.469,963.875,696.469,967.375L696.469,970.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyEffects_AttrChange_24" d="M618.469,1010.909L556.641,1018.07C494.813,1025.231,371.156,1039.553,309.328,1050.214C247.5,1060.875,247.5,1067.875,247.5,1071.375L247.5,1074.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyEffects_ResChange_25" d="M618.469,1016.626L585.641,1022.834C552.813,1029.042,487.156,1041.459,454.328,1051.167C421.5,1060.875,421.5,1067.875,421.5,1071.375L421.5,1074.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyEffects_StatusAdd_26" d="M774.469,1013.018L822.135,1019.827C869.802,1026.637,965.135,1040.256,1012.802,1050.565C1060.469,1060.875,1060.469,1067.875,1060.469,1071.375L1060.469,1074.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyEffects_SpecialAbility_27" d="M680.113,1028.875L677.589,1033.042C675.065,1037.208,670.017,1045.542,667.493,1053.208C664.969,1060.875,664.969,1067.875,664.969,1071.375L664.969,1074.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyEffects_ProbMod_28" d="M774.469,1006.377L911.635,1014.293C1048.802,1022.209,1323.135,1038.042,1460.302,1049.459C1597.469,1060.875,1597.469,1067.875,1597.469,1071.375L1597.469,1074.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AttrChange_UpdatePlayer_29" d="M247.5,1132.875L247.5,1137.042C247.5,1141.208,247.5,1149.542,297.105,1166.78C346.711,1184.017,445.921,1210.16,495.527,1223.231L545.132,1236.302"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ResChange_UpdatePlayer_30" d="M421.5,1132.875L421.5,1137.042C421.5,1141.208,421.5,1149.542,445.903,1165.583C470.306,1181.625,519.112,1205.375,543.515,1217.25L567.918,1229.125"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StatusAdd_UpdatePlayer_31" d="M982.469,1118.938L943.719,1125.427C904.969,1131.917,827.469,1144.896,774.275,1163.132C721.081,1181.367,692.193,1204.859,677.749,1216.605L663.305,1228.351"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SpecialAbility_SetFlags_32" d="M726.969,1122.158L749.635,1128.111C772.302,1134.064,817.635,1145.969,840.302,1163.422C862.969,1180.875,862.969,1203.875,862.969,1215.375L862.969,1226.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ProbMod_ModifySystem_33" d="M1535.469,1120.664L1509.469,1126.866C1483.469,1133.068,1431.469,1145.471,1405.469,1163.173C1379.469,1180.875,1379.469,1203.875,1379.469,1215.375L1379.469,1226.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UpdatePlayer_TriggerEvent_34" d="M627,1284.875L627,1297.042C627,1309.208,627,1333.542,659.843,1351.498C692.687,1369.454,758.374,1381.032,791.217,1386.822L824.061,1392.611"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SetFlags_TriggerEvent_35" d="M862.969,1284.875L862.969,1297.042C862.969,1309.208,862.969,1333.542,867.199,1349.434C871.428,1365.327,879.888,1372.779,884.118,1376.505L888.348,1380.231"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ModifySystem_TriggerEvent_36" d="M1379.469,1284.875L1379.469,1297.042C1379.469,1309.208,1379.469,1333.542,1319.553,1352.519C1259.637,1371.496,1139.806,1385.117,1079.89,1391.928L1019.974,1398.738"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TriggerEvent_UpdateUI_37" d="M922,1436.875L922,1441.042C922,1445.208,922,1453.542,922,1467.031C922,1480.521,922,1499.167,922,1508.49L922,1517.813"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UpdateUI_ShowNotification_38" d="M852.586,1557.027L724.822,1572.148C597.057,1587.268,341.529,1617.509,213.764,1638.13C86,1658.75,86,1669.75,86,1675.25L86,1680.75"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UpdateUI_UpdateTalentPanel_39" d="M922,1575.813L922,1587.802C922,1599.792,922,1623.771,978.169,1644.341C1034.338,1664.91,1146.676,1682.071,1202.846,1690.651L1259.015,1699.231"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UpdateUI_PlayEffect_40" d="M991.414,1559.428L1087.673,1574.148C1183.932,1588.868,1376.451,1618.309,1480.555,1638.801C1584.66,1659.293,1600.352,1670.836,1608.198,1676.608L1616.044,1682.38"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StatusAdd_StatusLoop_41" d="M1138.469,1110.923L1259.385,1118.748C1380.302,1126.574,1622.135,1142.224,1743.122,1153.633C1864.109,1165.042,1864.25,1172.209,1864.32,1175.792L1864.39,1179.376"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StatusLoop_CheckDuration_42" d="M1837.392,1306.298L1832.405,1314.894C1827.418,1323.491,1817.443,1340.683,1812.456,1352.779C1807.469,1364.875,1807.469,1371.875,1807.469,1375.375L1807.469,1378.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CheckDuration_Expired_43" d="M1807.469,1436.875L1807.469,1441.042C1807.469,1445.208,1807.469,1453.542,1807.539,1461.292C1807.609,1469.042,1807.75,1476.209,1807.82,1479.792L1807.89,1483.376"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Expired_RemoveStatus_44" d="M1807.969,1611.25L1807.885,1617.333C1807.802,1623.417,1807.635,1635.583,1812.555,1647.334C1817.474,1659.084,1827.48,1670.418,1832.483,1676.084L1837.486,1681.751"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Expired_ApplyStatusEffect_45" d="M1852.952,1566.267L1889.121,1579.847C1925.291,1593.428,1997.63,1620.589,2033.799,1639.669C2069.969,1658.75,2069.969,1669.75,2069.969,1675.25L2069.969,1680.75"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyStatusEffect_StatusAttrChange_46" d="M2069.969,1738.75L2069.969,1742.917C2069.969,1747.083,2069.969,1755.417,2097.65,1765.387C2125.33,1775.358,2180.692,1786.966,2208.373,1792.77L2236.054,1798.574"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StatusAttrChange_StatusLoop_47" d="M2362.103,1788.75L2368.914,1784.583C2375.725,1780.417,2389.347,1772.083,2396.158,1759.25C2402.969,1746.417,2402.969,1729.083,2402.969,1709.75C2402.969,1690.417,2402.969,1669.083,2402.969,1641.927C2402.969,1614.771,2402.969,1581.792,2402.969,1550.813C2402.969,1519.833,2402.969,1490.854,2402.969,1467.698C2402.969,1444.542,2402.969,1427.208,2402.969,1409.875C2402.969,1392.542,2402.969,1375.208,2324.418,1352.036C2245.868,1328.863,2088.766,1299.851,2010.216,1285.345L1931.665,1270.838"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RemoveStatus_StatusLoop_48" d="M1901.516,1684.75L1910.091,1678.583C1918.667,1672.417,1935.818,1660.083,1944.393,1637.427C1952.969,1614.771,1952.969,1581.792,1952.969,1550.813C1952.969,1519.833,1952.969,1490.854,1952.969,1467.698C1952.969,1444.542,1952.969,1427.208,1952.969,1409.875C1952.969,1392.542,1952.969,1375.208,1944.548,1357.07C1936.127,1338.932,1919.286,1319.99,1910.865,1310.518L1902.444,1301.047"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ProbMod_BlindBoxOpen_49" d="M1659.469,1111.344L1747.385,1119.099C1835.302,1126.854,2011.135,1142.365,2099.129,1157.703C2187.122,1173.042,2187.275,1188.208,2187.352,1195.792L2187.428,1203.375"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BlindBoxOpen_CalcModifier_50" d="M2187.469,1309.375L2187.385,1317.458C2187.302,1325.542,2187.135,1341.708,2187.052,1353.292C2186.969,1364.875,2186.969,1371.875,2186.969,1375.375L2186.969,1378.875"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CalcModifier_ApplyModifier_51" d="M2186.969,1436.875L2186.969,1441.042C2186.969,1445.208,2186.969,1453.542,2186.969,1467.031C2186.969,1480.521,2186.969,1499.167,2186.969,1508.49L2186.969,1517.813"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ApplyModifier_BoxResult_52" d="M2186.969,1575.813L2186.969,1587.802C2186.969,1599.792,2186.969,1623.771,2195.003,1641.538C2203.037,1659.305,2219.106,1670.86,2227.14,1676.637L2235.174,1682.415"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ShowNotification_CheckLoop_53" d="M94.438,1684.75L96.365,1678.583C98.292,1672.417,102.146,1660.083,104.073,1637.427C106,1614.771,106,1581.792,106,1550.813C106,1519.833,106,1490.854,106,1467.698C106,1444.542,106,1427.208,106,1409.875C106,1392.542,106,1375.208,106,1349.875C106,1324.542,106,1291.208,106,1257.875C106,1224.542,106,1191.208,106,1165.875C106,1140.542,106,1123.208,106,1105.875C106,1088.542,106,1071.208,106,1053.875C106,1036.542,106,1019.208,106,1001.875C106,984.542,106,967.208,106,949.875C106,932.542,106,915.208,106,895.875C106,876.542,106,855.208,106,825.385C106,795.563,106,757.25,106,720.938C106,684.625,106,650.313,106,624.49C106,598.667,106,581.333,106,564C106,546.667,106,529.333,205.798,503.738C305.596,478.144,505.191,444.287,604.989,427.359L704.787,410.431"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UpdateTalentPanel_CheckLoop_54" d="M1408.258,1684.75L1423.626,1678.583C1438.995,1672.417,1469.732,1660.083,1485.1,1637.427C1500.469,1614.771,1500.469,1581.792,1500.469,1550.813C1500.469,1519.833,1500.469,1490.854,1500.469,1467.698C1500.469,1444.542,1500.469,1427.208,1500.469,1409.875C1500.469,1392.542,1500.469,1375.208,1500.469,1349.875C1500.469,1324.542,1500.469,1291.208,1500.469,1257.875C1500.469,1224.542,1500.469,1191.208,1500.469,1165.875C1500.469,1140.542,1500.469,1123.208,1500.469,1105.875C1500.469,1088.542,1500.469,1071.208,1500.469,1053.875C1500.469,1036.542,1500.469,1019.208,1500.469,1001.875C1500.469,984.542,1500.469,967.208,1500.469,949.875C1500.469,932.542,1500.469,915.208,1500.469,895.875C1500.469,876.542,1500.469,855.208,1500.469,825.385C1500.469,795.563,1500.469,757.25,1500.469,720.938C1500.469,684.625,1500.469,650.313,1500.469,624.49C1500.469,598.667,1500.469,581.333,1500.469,564C1500.469,546.667,1500.469,529.333,1395.175,503.641C1289.882,477.95,1079.294,443.899,974.001,426.874L868.707,409.849"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PlayEffect_CheckLoop_55" d="M1672.211,1684.75L1675.921,1678.583C1679.63,1672.417,1687.049,1660.083,1690.759,1637.427C1694.469,1614.771,1694.469,1581.792,1694.469,1550.813C1694.469,1519.833,1694.469,1490.854,1694.469,1467.698C1694.469,1444.542,1694.469,1427.208,1694.469,1409.875C1694.469,1392.542,1694.469,1375.208,1694.469,1349.875C1694.469,1324.542,1694.469,1291.208,1694.469,1257.875C1694.469,1224.542,1694.469,1191.208,1694.469,1165.875C1694.469,1140.542,1694.469,1123.208,1694.469,1105.875C1694.469,1088.542,1694.469,1071.208,1694.469,1053.875C1694.469,1036.542,1694.469,1019.208,1694.469,1001.875C1694.469,984.542,1694.469,967.208,1694.469,949.875C1694.469,932.542,1694.469,915.208,1694.469,895.875C1694.469,876.542,1694.469,855.208,1694.469,825.385C1694.469,795.563,1694.469,757.25,1694.469,720.938C1694.469,684.625,1694.469,650.313,1694.469,624.49C1694.469,598.667,1694.469,581.333,1694.469,564C1694.469,546.667,1694.469,529.333,1557.246,503.218C1420.024,477.103,1145.578,442.205,1008.356,424.757L871.133,407.308"></path><path marker-end="url(#mermaid-77283e66-7183-41aa-bc8b-b5d6e9950d92_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BoxResult_CheckLoop_56" d="M2301.492,1684.75L2307.322,1678.583C2313.151,1672.417,2324.81,1660.083,2330.639,1637.427C2336.469,1614.771,2336.469,1581.792,2336.469,1550.813C2336.469,1519.833,2336.469,1490.854,2336.469,1467.698C2336.469,1444.542,2336.469,1427.208,2336.469,1409.875C2336.469,1392.542,2336.469,1375.208,2336.469,1349.875C2336.469,1324.542,2336.469,1291.208,2336.469,1257.875C2336.469,1224.542,2336.469,1191.208,2336.469,1165.875C2336.469,1140.542,2336.469,1123.208,2336.469,1105.875C2336.469,1088.542,2336.469,1071.208,2336.469,1053.875C2336.469,1036.542,2336.469,1019.208,2336.469,1001.875C2336.469,984.542,2336.469,967.208,2336.469,949.875C2336.469,932.542,2336.469,915.208,2336.469,895.875C2336.469,876.542,2336.469,855.208,2336.469,825.385C2336.469,795.563,2336.469,757.25,2336.469,720.938C2336.469,684.625,2336.469,650.313,2336.469,624.49C2336.469,598.667,2336.469,581.333,2336.469,564C2336.469,546.667,2336.469,529.333,2092.911,502.522C1849.354,475.71,1362.239,439.421,1118.681,421.276L875.123,403.131"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(279.5, 833.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(521.5, 833.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(763.5, 833.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1127.5, 833.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1369.5, 833.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(340, 564)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(582, 564)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(946, 564)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(1188, 564)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(1430, 564)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1807.46875, 1647.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(2069.96875, 1647.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(785.96875, 27.5)" id="flowchart-Start-995" class="node default startClass"><rect height="39" width="88.75" y="-19.5" x="-44.375" ry="19.5" rx="19.5" style="fill:#c8e6c9 !important;stroke:#4caf50 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>游戏开始</p></span></div></foreignObject></g></g><g transform="translate(785.96875, 124)" id="flowchart-LoadData-996" class="node default startClass"><rect height="54" width="182.6640625" y="-27" x="-91.33203125" style="fill:#c8e6c9 !important;stroke:#4caf50 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-61.33203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.6640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>加载CSV天赋数据</p></span></div></foreignObject></g></g><g transform="translate(785.96875, 228)" id="flowchart-InitTalents-998" class="node default startClass"><rect height="54" width="172" y="-27" x="-86" style="fill:#c8e6c9 !important;stroke:#4caf50 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化天赋配置</p></span></div></foreignObject></g></g><g transform="translate(785.96875, 396)" id="flowchart-CheckLoop-1000" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"></polygon><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>每日检查天赋条件</p></span></div></foreignObject></g></g><g transform="translate(219, 564)" id="flowchart-CheckAttr-1002" class="node default processClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查属性阈值</p></span></div></foreignObject></g></g><g transform="translate(461, 564)" id="flowchart-CheckResource-1004" class="node default processClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查资源阈值</p></span></div></foreignObject></g></g><g transform="translate(703, 564)" id="flowchart-CheckDays-1006" class="node default processClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查生存天数</p></span></div></foreignObject></g></g><g transform="translate(1067, 564)" id="flowchart-CheckBoxes-1008" class="node default processClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查开盒数量</p></span></div></foreignObject></g></g><g transform="translate(1309, 564)" id="flowchart-CheckEvents-1010" class="node default processClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查特殊事件</p></span></div></foreignObject></g></g><g transform="translate(279.5, 718.9375)" id="flowchart-AttrMet-1012" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>属性条件满足?</p></span></div></foreignObject></g></g><g transform="translate(521.5, 718.9375)" id="flowchart-ResMet-1014" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>资源条件满足?</p></span></div></foreignObject></g></g><g transform="translate(763.5, 718.9375)" id="flowchart-DaysMet-1016" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>天数条件满足?</p></span></div></foreignObject></g></g><g transform="translate(1127.5, 718.9375)" id="flowchart-BoxesMet-1018" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开盒条件满足?</p></span></div></foreignObject></g></g><g transform="translate(1369.5, 718.9375)" id="flowchart-EventMet-1020" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>事件条件满足?</p></span></div></foreignObject></g></g><g transform="translate(696.46875, 897.875)" id="flowchart-UnlockTalent-1022" class="node default processClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>解锁天赋</p></span></div></foreignObject></g></g><g transform="translate(696.46875, 1001.875)" id="flowchart-ApplyEffects-1042" class="node default effectClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用天赋效果</p></span></div></foreignObject></g></g><g transform="translate(247.5, 1105.875)" id="flowchart-AttrChange-1044" class="node default effectClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>属性变化</p></span></div></foreignObject></g></g><g transform="translate(421.5, 1105.875)" id="flowchart-ResChange-1046" class="node default effectClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>资源变化</p></span></div></foreignObject></g></g><g transform="translate(1060.46875, 1105.875)" id="flowchart-StatusAdd-1048" class="node default effectClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>添加状态效果</p></span></div></foreignObject></g></g><g transform="translate(664.96875, 1105.875)" id="flowchart-SpecialAbility-1050" class="node default effectClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>特殊能力</p></span></div></foreignObject></g></g><g transform="translate(1597.46875, 1105.875)" id="flowchart-ProbMod-1052" class="node default effectClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>概率修正</p></span></div></foreignObject></g></g><g transform="translate(627, 1257.875)" id="flowchart-UpdatePlayer-1054" class="node default dataClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e8f5e8 !important;stroke:#4caf50 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新玩家数据</p></span></div></foreignObject></g></g><g transform="translate(862.96875, 1257.875)" id="flowchart-SetFlags-1060" class="node default dataClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e8f5e8 !important;stroke:#4caf50 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置特殊标志</p></span></div></foreignObject></g></g><g transform="translate(1379.46875, 1257.875)" id="flowchart-ModifySystem-1062" class="node default dataClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e8f5e8 !important;stroke:#4caf50 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>修改系统概率</p></span></div></foreignObject></g></g><g transform="translate(922, 1409.875)" id="flowchart-TriggerEvent-1064" class="node default dataClass"><rect height="54" width="188" y="-27" x="-94" style="fill:#e8f5e8 !important;stroke:#4caf50 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>触发天赋获得事件</p></span></div></foreignObject></g></g><g transform="translate(922, 1548.8125)" id="flowchart-UpdateUI-1070" class="node default uiClass"><rect height="54" width="138.828125" y="-27" x="-69.4140625" style="fill:#ffebee !important;stroke:#f44336 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-39.4140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="78.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新UI显示</p></span></div></foreignObject></g></g><g transform="translate(86, 1711.75)" id="flowchart-ShowNotification-1072" class="node default uiClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#ffebee !important;stroke:#f44336 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>显示获得通知</p></span></div></foreignObject></g></g><g transform="translate(1340.96875, 1711.75)" id="flowchart-UpdateTalentPanel-1074" class="node default uiClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#ffebee !important;stroke:#f44336 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新天赋面板</p></span></div></foreignObject></g></g><g transform="translate(1655.96875, 1711.75)" id="flowchart-PlayEffect-1076" class="node default uiClass"><rect height="54" width="124" y="-27" x="-62" style="fill:#ffebee !important;stroke:#f44336 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>播放特效</p></span></div></foreignObject></g></g><g transform="translate(1863.96875, 1257.875)" id="flowchart-StatusLoop-1078" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>状态效果循环</p></span></div></foreignObject></g></g><g transform="translate(1807.46875, 1409.875)" id="flowchart-CheckDuration-1080" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查持续时间</p></span></div></foreignObject></g></g><g transform="translate(1807.46875, 1548.8125)" id="flowchart-Expired-1082" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-61.9375,61.9375)" class="label-container" points="61.9375,0 123.875,-61.9375 61.9375,-123.875 0,-61.9375"></polygon><g transform="translate(-34.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="69.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否过期?</p></span></div></foreignObject></g></g><g transform="translate(1863.96875, 1711.75)" id="flowchart-RemoveStatus-1084" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>移除状态效果</p></span></div></foreignObject></g></g><g transform="translate(2069.96875, 1711.75)" id="flowchart-ApplyStatusEffect-1086" class="node default effectClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用状态效果</p></span></div></foreignObject></g></g><g transform="translate(2317.96875, 1815.75)" id="flowchart-StatusAttrChange-1088" class="node default effectClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important;stroke:#9c27b0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>状态属性变化</p></span></div></foreignObject></g></g><g transform="translate(2186.96875, 1257.875)" id="flowchart-BlindBoxOpen-1094" class="node default decisionClass"><polygon style="fill:#fff3e0 !important;stroke:#ff9800 !important;stroke-width:2px !important" transform="translate(-51,51)" class="label-container" points="51,0 102,-51 51,-102 0,-51"></polygon><g transform="translate(-24, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开盒时</p></span></div></foreignObject></g></g><g transform="translate(2186.96875, 1409.875)" id="flowchart-CalcModifier-1096" class="node default processClass"><rect height="54" width="172" y="-27" x="-86" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>计算天赋修正值</p></span></div></foreignObject></g></g><g transform="translate(2186.96875, 1548.8125)" id="flowchart-ApplyModifier-1098" class="node default processClass"><rect height="54" width="172" y="-27" x="-86" style="fill:#e3f2fd !important;stroke:#2196f3 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用到概率计算</p></span></div></foreignObject></g></g><g transform="translate(2275.96875, 1711.75)" id="flowchart-BoxResult-1100" class="node default dataClass"><rect height="54" width="156" y="-27" x="-78" style="fill:#e8f5e8 !important;stroke:#4caf50 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>影响开盒结果</p></span></div></foreignObject></g></g></g></g></g></svg>