# 盲盒纪元 - 技术设计文档

## 1. 架构概述

### 1.1 整体架构
```
BoxOfFate/
├── Engine/           # 引擎层 - 基础系统和工具
├── Game/            # 游戏层 - 核心游戏逻辑
│   ├── Core/        # 核心系统 - 数据结构和处理器
│   ├── System/      # 游戏系统 - 各功能模块
│   ├── Data/        # 数据层 - 数据加载和管理
│   └── Database/    # 数据库 - 静态配置数据
└── UI/              # 界面层 - 用户界面系统
```

### 1.2 设计原则
- **模块化**: 各系统相对独立，低耦合高内聚
- **数据驱动**: 游戏内容通过外部数据文件配置
- **事件驱动**: 系统间通过事件进行通信
- **可扩展性**: 易于添加新功能和内容

## 2. 核心系统架构

### 2.1 数据结构层 (Core)

#### GameEffectSystem.cs
统一的游戏效果和条件系统，包含：

**GameEffectType 枚举**
```csharp
public enum GameEffectType
{
    // 属性相关
    AttributeChange,     // 属性变化
    AttributeSet,        // 属性设置
    
    // 资源相关  
    ResourceChange,      // 资源变化
    ResourceSet,         // 资源设置
    
    // 状态相关
    AddStatusEffect,     // 添加状态效果
    RemoveStatusEffect,  // 移除状态效果
    
    // 天赋相关
    GrantTalent,         // 授予天赋
    RemoveTalent,        // 移除天赋
    
    // 特殊效果
    StatusImmunity,      // 状态免疫
    SpecialAbility,      // 特殊能力
    ProbabilityModifier, // 概率修正
    
    // 其他效果...
}
```

**GameEffect 类**
```csharp
public class GameEffect
{
    public GameEffectType type;
    public AttributeType? attributeType;
    public ResourceType? resourceType;
    public StatusEffectType? statusEffectType;
    public TalentType? talentType;
    public float value;
    public int duration;
    public string stringValue;
    public bool boolValue;
    public string description;
    
    // 多个构造函数重载支持不同类型的效果
}
```

**GameCondition 类**
```csharp
public class GameCondition
{
    public GameConditionType type;
    public AttributeType? attributeType;
    public ResourceType? resourceType;
    public TalentType? talentType;
    public StatusEffectType? statusEffectType;
    public float value;
    public float minValue;
    public float maxValue;
    public string stringValue;
    public bool boolValue;
    public string description;
    
    // 条件检查方法
    public bool Check(PlayerData playerData);
}
```

#### GameEffectProcessor.cs
统一的效果处理器，负责应用各种游戏效果：

```csharp
public static class GameEffectProcessor
{
    public static void ApplyEffect(GameEffect effect, PlayerData playerData, float intensity = 1f)
    {
        switch (effect.type)
        {
            case GameEffectType.AttributeChange:
                ApplyAttributeChange(effect, playerData, intensity);
                break;
            case GameEffectType.ResourceChange:
                ApplyResourceChange(effect, playerData, intensity);
                break;
            // ... 其他效果类型处理
        }
    }
}
```

#### GameConditionProcessor.cs
统一的条件检查器：

```csharp
public static class GameConditionProcessor
{
    public static bool CheckCondition(GameCondition condition, PlayerData playerData)
    {
        switch (condition.type)
        {
            case GameConditionType.AttributeThreshold:
                return CheckAttributeThreshold(condition, playerData);
            case GameConditionType.ResourceThreshold:
                return CheckResourceThreshold(condition, playerData);
            // ... 其他条件类型检查
        }
    }
}
```

### 2.2 数据管理层

#### PlayerData.cs
玩家数据的核心容器：

```csharp
public class PlayerData
{
    public AttributeManager attributes;
    public ResourceManager resources;
    public List<TalentType> talents;
    public List<StatusEffect> statusEffects;
    public Dictionary<string, bool> flags;
    public Dictionary<string, float> counters;
    public int daysSurvived;
    public int boxesOpened;
    
    // 数据操作方法
    public void AddTalent(TalentType talent);
    public bool HasTalent(TalentType talent);
    public void AddStatusEffect(StatusEffect effect);
    public void SetSpecialFlag(string key, bool value);
    // ...
}
```

#### AttributeManager.cs
属性管理系统：

```csharp
public class AttributeManager
{
    private Dictionary<AttributeType, float> attributes;
    
    public float GetAttributeValue(AttributeType type);
    public void SetAttributeValue(AttributeType type, float value);
    public void ModifyAttribute(AttributeType type, float delta);
    public void ClampAttribute(AttributeType type, float min, float max);
}
```

#### ResourceManager.cs
资源管理系统：

```csharp
public class ResourceManager
{
    private Dictionary<ResourceType, float> resources;
    
    public float GetResource(ResourceType type);
    public bool HasResource(ResourceType type, float amount);
    public bool ConsumeResource(ResourceType type, float amount);
    public void AddResource(ResourceType type, float amount);
}
```

### 2.3 游戏系统层 (System)

#### BlindBoxManager.cs
盲盒系统的核心管理器：

```csharp
public class BlindBoxManager
{
    private Dictionary<BlindBoxType, BlindBoxConfigData> boxConfigs;
    private Dictionary<string, BlindBoxItemData> itemDatabase;
    
    public BlindBoxResult OpenBox(BlindBoxType boxType, PlayerData playerData);
    private BlindBoxItemData SelectRandomItem(BlindBoxConfigData config, PlayerData playerData);
    private float CalculateRealProbability(BlindBoxLootTableData lootData, PlayerData playerData);
}
```

**盲盒配置数据结构**：
```csharp
public class BlindBoxConfigData
{
    public BlindBoxType type;
    public string name;
    public string description;
    public int cost;
    public ResourceType costType;
    public List<BlindBoxLootTableData> lootTables;
}

public class BlindBoxLootTableData
{
    public string itemId;
    public float baseWeight;
    public float displayProbability;  // 显示给玩家的概率
    public float realProbability;     // 实际概率
}
```

#### TalentSystem.cs
天赋系统管理器：

```csharp
public class TalentSystem
{
    private Dictionary<TalentType, TalentConfig> talentConfigs;
    private Dictionary<StatusEffectType, StatusEffectConfig> statusEffectConfigs;
    
    public bool AcquireTalent(PlayerData playerData, TalentType talentType);
    public float GetTalentProbabilityModifier(PlayerData playerData, BlindBoxContentType contentType);
    public void AddStatusEffect(PlayerData playerData, StatusEffect effect);
    public void UpdateStatusEffects(PlayerData playerData);
}
```

**天赋配置数据结构**：
```csharp
public class TalentConfig
{
    public string name;
    public string description;
    public TalentRarity rarity;
    public string iconPath;
    public int unlockLevel;
    public bool isStartingTalent;
    public List<GameCondition> requirements;
    public List<GameEffect> effects;
}
```

#### GameManager.cs
游戏主控制器：

```csharp
public class GameManager : MonoBehaviour
{
    public PlayerData playerData;
    public BlindBoxManager blindBoxManager;
    public TalentSystem talentSystem;
    public EventSystem eventSystem;
    
    public void StartNewGame();
    public void SaveGame();
    public void LoadGame();
    public void ProcessDailyUpdate();
}
```

### 2.4 数据加载层 (Data)

#### TalentDataLoader.cs
从CSV文件加载天赋数据：

```csharp
public static class TalentDataLoader
{
    public static void LoadAllData();
    public static Dictionary<TalentType, TalentConfig> GetTalentConfigs();
    public static Dictionary<StatusEffectType, StatusEffectConfig> GetStatusEffectConfigs();
    
    private static Dictionary<TalentType, TalentConfig> LoadTalentBasicData(string filePath);
    private static Dictionary<TalentType, List<GameCondition>> LoadTalentRequirements(string filePath);
    private static Dictionary<TalentType, List<GameEffect>> LoadTalentEffects(string filePath);
}
```

**CSV数据文件结构**：
- `TalentData.csv`: 天赋基础信息
- `TalentRequirements.csv`: 天赋解锁条件
- `TalentEffects.csv`: 天赋效果
- `StatusEffectData.csv`: 状态效果基础信息
- `StatusEffectEffects.csv`: 状态效果影响

### 2.5 数据库层 (Database)

#### BlindBoxConfigDatabase.cs
静态盲盒配置数据：

```csharp
public static class BlindBoxConfigDatabase
{
    public static List<BlindBoxConfigData> GetAllBoxConfigs();
    private static BlindBoxConfigData GetBasicBoxConfig();
    private static BlindBoxConfigData GetLifeBoxConfig();
    // ... 其他盲盒配置
}
```

#### BlindBoxItemDatabase.cs
静态物品数据库：

```csharp
public static class BlindBoxItemDatabase
{
    public static List<BlindBoxItemData> GetAllItems();
    public static List<BlindBoxItemData> GetEmptyItems();
    public static List<BlindBoxItemData> GetFoodItems();
    // ... 其他物品分类
}
```

## 3. 引擎层架构 (Engine)

### 3.1 UI系统

#### UISystem.cs
UI管理系统：

```csharp
public class UISystem : MonoBehaviour
{
    [Header("开发设置")]
    public bool useCanvasToggle = true; // 开发开关
    
    private Dictionary<string, GameObject> uiPanels;
    private Dictionary<string, GameObject> loadedPrefabs;
    
    public void ShowPanel(string panelName);
    public void HidePanel(string panelName);
    private GameObject LoadUIPrefab(string prefabName);
}
```

#### AudioSystem.cs
音频管理系统：

```csharp
public class AudioSystem : MonoBehaviour
{
    [Header("音频源")]
    public AudioSource musicSource;
    public AudioSource sfxSource;
    
    public void PlayMusic(AudioClip clip, bool loop = true);
    public void PlaySFX(AudioClip clip);
    public void SetMusicVolume(float volume);
    public void SetSFXVolume(float volume);
}
```

### 3.2 本地化系统

#### LocalizationSystem.cs
多语言支持系统：

```csharp
public class LocalizationSystem : MonoBehaviour
{
    private Dictionary<int, Dictionary<SystemLanguage, string>> textDatabase;
    
    public string GetText(int textId, SystemLanguage language = SystemLanguage.Chinese);
    public void LoadTextData(string csvPath);
    public void ChangeLanguage(SystemLanguage newLanguage);
}
```

## 4. 数据流架构

### 4.1 数据流向
```
CSV Files → DataLoader → ConfigData → GameSystems → PlayerData → UI Display
    ↑                                      ↓
External Config                    Game Events & Effects
```

### 4.2 事件系统
```csharp
// 事件定义
public static class GameEvents
{
    public static event Action<TalentType> OnTalentAcquired;
    public static event Action<StatusEffectType> OnStatusEffectAdded;
    public static event Action<BlindBoxResult> OnBoxOpened;
    // ...
}

// 事件触发
GameEvents.OnTalentAcquired?.Invoke(talentType);

// 事件监听
GameEvents.OnTalentAcquired += HandleTalentAcquired;
```

### 4.3 保存系统
```csharp
public class SaveSystem
{
    public static void SavePlayerData(PlayerData data, string fileName);
    public static PlayerData LoadPlayerData(string fileName);
    public static bool HasSaveFile(string fileName);
    public static void DeleteSaveFile(string fileName);
}
```

## 5. 性能优化

### 5.1 内存管理
- 对象池用于频繁创建/销毁的对象
- 及时释放不需要的资源引用
- 使用结构体代替小对象类

### 5.2 计算优化
- 缓存频繁计算的结果
- 使用查找表代替复杂计算
- 异步加载大量数据

### 5.3 UI优化
- UI对象的动态加载和卸载
- 避免频繁的UI重建
- 使用对象池管理UI元素

## 6. 扩展性设计

### 6.1 新系统添加
1. 在Core层定义新的数据结构
2. 在System层实现系统逻辑
3. 在Data层添加数据加载
4. 在UI层添加界面支持

### 6.2 新内容添加
1. 在CSV文件中添加配置数据
2. 在Database层添加静态数据
3. 系统自动加载新内容

### 6.3 平台适配
- 核心逻辑保持平台无关
- 平台特定功能通过接口抽象
- 使用条件编译处理平台差异

---

*本文档描述了当前的技术架构，将随开发进程更新*
