# 盲盒纪元 (Box of Fate) - 项目总览

## 🎮 项目简介

**盲盒纪元**是一款反乌托邦生存模拟游戏，结合了Roguelike元素和盲盒机制。玩家在一个被"完美系统"控制的未来社会中求生，通过开启盲盒获取资源，逐步发现系统的虚伪本质，最终选择顺从或反抗。

### 核心特色
- 🎲 **概率欺骗机制**: 显示概率与真实概率的差异
- 🧠 **多维属性系统**: 9种属性影响游戏进程
- 🌟 **天赋系统**: 15种天赋改变游戏体验
- 📊 **数据驱动**: 所有内容通过CSV配置
- 🎭 **多重结局**: 根据选择达成不同结局

## 📁 项目结构

```
BoxOfFate/
├── Assets/
│   ├── Scripts/
│   │   ├── Game/              # 游戏核心逻辑
│   │   │   ├── Core/          # 数据结构和处理器
│   │   │   ├── System/        # 功能系统
│   │   │   ├── Data/          # 数据加载器
│   │   │   └── Database/      # 静态配置
│   │   ├── Engine/            # 引擎层系统
│   │   │   ├── UI/            # UI管理
│   │   │   ├── Audio/         # 音频系统
│   │   │   └── Localization/  # 本地化
│   │   └── Documentation/     # 项目文档
│   ├── StreamingAssets/
│   │   └── Data/              # CSV配置文件
│   ├── Resources/             # 资源文件
│   └── Prefabs/               # 预制体
└── Documentation/             # 外部文档
```

## 🛠️ 技术架构

### 核心系统
- **GameEffectSystem**: 统一的效果和条件系统
- **BlindBoxManager**: 盲盒开启和概率计算
- **TalentSystem**: 天赋管理和状态效果
- **PlayerData**: 玩家数据管理
- **UISystem**: 界面管理系统

### 数据流
```
CSV Files → DataLoader → ConfigData → GameSystems → PlayerData → UI
```

### 设计模式
- **单例模式**: 全局管理器
- **观察者模式**: 事件系统
- **策略模式**: 概率计算
- **工厂模式**: 对象创建

## 📊 游戏系统详解

### 属性系统
| 属性 | 范围 | 作用 |
|------|------|------|
| 生命值 | 0-100 | 影响生存能力 |
| 精力值 | 0-100 | 影响行动能力 |
| 幸运值 | 0-100 | 影响盲盒结果 |
| 社交值 | 0-100 | 影响职业发展 |
| 认知值 | 0-100 | 影响真相发现 |
| 人性值 | 0-100 | 影响道德选择 |
| 道德值 | 0-100 | 影响系统评价 |
| 依存度 | 0-100 | 对系统依赖程度 |
| 污染度 | 0-100 | 受负面影响程度 |

### 盲盒类型
| 盲盒 | 价格 | 特点 |
|------|------|------|
| 基础生存盲盒 | 50信用点 | 基础物品，高空盒率 |
| 生活时间盲盒 | 100信用点 | 时间和生活品质 |
| 高级神谕盲盒 | 200信用点 | 高价值，高陷阱率 |
| 神秘盲盒 | 150信用点 | 极端结果 |
| 黑市盲盒 | 300信用点 | 高风险高回报 |

### 天赋分类
- **普通 (Common)**: 基础能力提升
- **稀有 (Rare)**: 特殊抗性和能力
- **史诗 (Epic)**: 强大的特殊能力
- **传说 (Legendary)**: 改变游戏规则的能力

## 🔧 开发环境

### 技术栈
- **引擎**: Unity 2022.3 LTS
- **语言**: C#
- **数据格式**: CSV
- **版本控制**: Git
- **平台**: iOS/Android, 微信小程序

### 开发工具
- **IDE**: Visual Studio / Rider
- **版本控制**: Git
- **文档**: Markdown
- **数据编辑**: Excel/CSV编辑器

## 📋 开发规范

### 命名规范
- **类名**: PascalCase (BlindBoxManager)
- **方法**: PascalCase (OpenBlindBox)
- **字段**: camelCase (currentHealth)
- **常量**: UPPER_CASE (MAX_HEALTH)

### 代码组织
- 使用命名空间分层
- 单一职责原则
- 依赖注入
- 事件驱动通信

### 性能要求
- 内存使用优化
- 对象池管理
- 异步加载
- UI动态管理

## 📈 项目状态

### 已完成功能 ✅
- [x] 核心数据结构设计
- [x] 盲盒系统实现
- [x] 天赋系统实现
- [x] 属性管理系统
- [x] 状态效果系统
- [x] CSV数据加载
- [x] 基础UI框架
- [x] 音频系统
- [x] 本地化系统

### 开发中功能 🚧
- [ ] 事件系统完善
- [ ] 游戏主循环
- [ ] 保存系统
- [ ] UI界面实现
- [ ] 音效和音乐

### 计划功能 📅
- [ ] 多结局系统
- [ ] 成就系统
- [ ] 数据分析
- [ ] 平台适配
- [ ] 性能优化

## 🎯 里程碑

### Alpha版本 (当前)
- 核心系统实现
- 基础功能验证
- 数据结构确定

### Beta版本 (计划)
- 完整游戏循环
- UI界面完成
- 平衡性调整

### 正式版本 (目标)
- 所有功能完成
- 性能优化
- 多平台发布

## 📚 文档索引

### 设计文档
- [游戏策划案](GameDesignDocument.md) - 完整的游戏设计
- [技术设计文档](TechnicalDesignDocument.md) - 技术架构说明
- [开发规范](DevelopmentStandards.md) - 代码和开发规范

### API文档
- [核心系统API](../Scripts/Game/Core/) - 核心数据结构
- [游戏系统API](../Scripts/Game/System/) - 功能系统接口
- [引擎系统API](../Scripts/Engine/) - 引擎层接口

### 配置文档
- [CSV数据格式](../StreamingAssets/Data/) - 配置文件说明
- [盲盒配置](../Scripts/Game/Database/) - 盲盒数据结构

## 🤝 贡献指南

### 开发流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 代码审查
6. 合并到主分支

### 代码提交
```bash
# 克隆项目
git clone [repository-url]

# 创建功能分支
git checkout -b feature/new-feature

# 提交更改
git commit -m "feat(system): 添加新功能"

# 推送分支
git push origin feature/new-feature
```

### 问题报告
- 使用Issue模板
- 提供详细的重现步骤
- 包含相关的日志信息
- 标明优先级和类型

## 📞 联系信息

### 开发团队
- **项目负责人**: [姓名]
- **技术负责人**: [姓名]
- **策划负责人**: [姓名]

### 沟通渠道
- **项目管理**: [工具链接]
- **技术讨论**: [论坛/群组]
- **问题反馈**: [Issue系统]

## 📄 许可证

本项目采用 [许可证类型] 许可证。详见 [LICENSE](LICENSE) 文件。

---

**盲盒纪元** - 在虚假的完美中寻找真实的自由

*最后更新: 2024年*
