# 盲盒纪元 - 开发规范文档

## 1. 代码规范

### 1.1 命名规范

#### 类和接口
```csharp
// 类名使用PascalCase
public class BlindBoxManager { }
public class PlayerData { }

// 接口以I开头
public interface IDataLoader { }
public interface ISaveable { }

// 抽象类以Abstract开头
public abstract class AbstractGameSystem { }
```

#### 方法和属性
```csharp
// 方法名使用PascalCase
public void OpenBlindBox() { }
public bool CheckCondition() { }

// 属性使用PascalCase
public int Health { get; set; }
public string PlayerName { get; set; }

// 私有字段使用camelCase
private int currentHealth;
private string playerName;
```

#### 常量和枚举
```csharp
// 常量使用UPPER_CASE
public const int MAX_HEALTH = 100;
public const string DEFAULT_SAVE_FILE = "save_data.json";

// 枚举使用PascalCase
public enum AttributeType
{
    Health,
    Energy,
    Luck
}
```

### 1.2 文件组织

#### 命名空间结构
```csharp
namespace BoxOfFate.Game.Core        // 核心系统
namespace BoxOfFate.Game.System     // 游戏系统
namespace BoxOfFate.Game.Data       // 数据层
namespace BoxOfFate.Game.Database   // 数据库
namespace BoxOfFate.Engine.UI       // UI系统
namespace BoxOfFate.Engine.Audio    // 音频系统
```

#### 文件夹结构
```
Assets/Scripts/
├── Game/
│   ├── Core/           # 核心数据结构和处理器
│   ├── System/         # 各功能系统
│   ├── Data/           # 数据加载器
│   └── Database/       # 静态数据
├── Engine/
│   ├── UI/             # UI系统
│   ├── Audio/          # 音频系统
│   └── Localization/   # 本地化系统
└── Documentation/      # 文档
```

### 1.3 注释规范

#### 类注释
```csharp
/// <summary>
/// 盲盒管理器 - 负责盲盒开启逻辑和概率计算
/// </summary>
public class BlindBoxManager
{
    // 类实现
}
```

#### 方法注释
```csharp
/// <summary>
/// 开启指定类型的盲盒
/// </summary>
/// <param name="boxType">盲盒类型</param>
/// <param name="playerData">玩家数据</param>
/// <returns>开盒结果</returns>
public BlindBoxResult OpenBox(BlindBoxType boxType, PlayerData playerData)
{
    // 方法实现
}
```

#### 复杂逻辑注释
```csharp
// 计算真实概率：基础概率 + 天赋修正 + 系统调整
float realProbability = baseProbability 
    + talentModifier 
    + systemAdjustment;
```

## 2. 架构规范

### 2.1 分层原则

#### 依赖方向
```
UI Layer (界面层)
    ↓
System Layer (系统层)
    ↓
Core Layer (核心层)
    ↓
Data Layer (数据层)
```

#### 层间通信
- **向下调用**: 上层可以直接调用下层
- **向上通知**: 下层通过事件通知上层
- **同层交互**: 通过接口或事件进行

### 2.2 设计模式

#### 单例模式
```csharp
public class GameManager : MonoBehaviour
{
    public static GameManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
}
```

#### 观察者模式
```csharp
// 事件定义
public static class GameEvents
{
    public static event Action<TalentType> OnTalentAcquired;
}

// 事件触发
GameEvents.OnTalentAcquired?.Invoke(talentType);

// 事件监听
private void OnEnable()
{
    GameEvents.OnTalentAcquired += HandleTalentAcquired;
}

private void OnDisable()
{
    GameEvents.OnTalentAcquired -= HandleTalentAcquired;
}
```

#### 策略模式
```csharp
public interface IProbabilityCalculator
{
    float CalculateProbability(BlindBoxLootTableData lootData, PlayerData playerData);
}

public class BasicProbabilityCalculator : IProbabilityCalculator
{
    public float CalculateProbability(BlindBoxLootTableData lootData, PlayerData playerData)
    {
        // 基础概率计算
    }
}
```

### 2.3 数据驱动设计

#### 配置数据分离
```csharp
// 不要硬编码数据
// ❌ 错误示例
public void CreateTalent()
{
    var talent = new TalentConfig
    {
        name = "赌徒基因",
        description = "盲盒惊喜概率提升"
    };
}

// ✅ 正确示例
public void LoadTalentFromCSV(string csvPath)
{
    var talents = TalentDataLoader.LoadTalentData(csvPath);
}
```

#### 可配置参数
```csharp
[Header("盲盒配置")]
[SerializeField] private float baseProbabilityModifier = 1.0f;
[SerializeField] private bool enableDebugMode = false;
```

## 3. 性能规范

### 3.1 内存管理

#### 对象池使用
```csharp
public class UIEffectPool : MonoBehaviour
{
    private Queue<GameObject> effectPool = new Queue<GameObject>();
    
    public GameObject GetEffect()
    {
        if (effectPool.Count > 0)
            return effectPool.Dequeue();
        else
            return Instantiate(effectPrefab);
    }
    
    public void ReturnEffect(GameObject effect)
    {
        effect.SetActive(false);
        effectPool.Enqueue(effect);
    }
}
```

#### 及时释放资源
```csharp
public class ResourceManager : IDisposable
{
    private Dictionary<string, Texture2D> loadedTextures;
    
    public void Dispose()
    {
        foreach (var texture in loadedTextures.Values)
        {
            if (texture != null)
                DestroyImmediate(texture);
        }
        loadedTextures.Clear();
    }
}
```

### 3.2 计算优化

#### 缓存计算结果
```csharp
private Dictionary<string, float> probabilityCache = new Dictionary<string, float>();

public float GetCachedProbability(string key, Func<float> calculator)
{
    if (!probabilityCache.ContainsKey(key))
    {
        probabilityCache[key] = calculator();
    }
    return probabilityCache[key];
}
```

#### 避免频繁的字符串操作
```csharp
// ❌ 避免在循环中拼接字符串
string result = "";
for (int i = 0; i < items.Count; i++)
{
    result += items[i].ToString();
}

// ✅ 使用StringBuilder
var sb = new StringBuilder();
for (int i = 0; i < items.Count; i++)
{
    sb.Append(items[i].ToString());
}
string result = sb.ToString();
```

### 3.3 UI优化

#### 动态加载UI
```csharp
public class UIManager : MonoBehaviour
{
    private Dictionary<string, GameObject> loadedPanels = new Dictionary<string, GameObject>();
    
    public void ShowPanel(string panelName)
    {
        if (!loadedPanels.ContainsKey(panelName))
        {
            var prefab = Resources.Load<GameObject>($"UI/{panelName}");
            loadedPanels[panelName] = Instantiate(prefab, uiRoot);
        }
        
        loadedPanels[panelName].SetActive(true);
    }
}
```

## 4. 测试规范

### 4.1 单元测试

#### 测试命名
```csharp
[Test]
public void OpenBlindBox_WithValidInput_ReturnsValidResult()
{
    // Arrange
    var boxManager = new BlindBoxManager();
    var playerData = new PlayerData();
    
    // Act
    var result = boxManager.OpenBox(BlindBoxType.Basic, playerData);
    
    // Assert
    Assert.IsNotNull(result);
    Assert.IsTrue(result.IsValid);
}
```

#### 测试覆盖
- 核心逻辑必须有单元测试
- 边界条件测试
- 异常情况测试
- 性能测试

### 4.2 集成测试

#### 系统间交互测试
```csharp
[Test]
public void TalentSystem_Integration_WithBlindBoxSystem()
{
    // 测试天赋系统对盲盒概率的影响
    var talentSystem = new TalentSystem();
    var blindBoxManager = new BlindBoxManager();
    var playerData = new PlayerData();
    
    // 添加影响概率的天赋
    talentSystem.AcquireTalent(playerData, TalentType.GamblerGene);
    
    // 验证概率修正
    var modifier = talentSystem.GetTalentProbabilityModifier(playerData, BlindBoxContentType.Positive);
    Assert.Greater(modifier, 0);
}
```

## 5. 版本控制规范

### 5.1 分支策略
- `main`: 主分支，稳定版本
- `develop`: 开发分支，集成新功能
- `feature/功能名`: 功能分支
- `hotfix/修复名`: 紧急修复分支

### 5.2 提交规范
```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

#### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(blindbox): 添加新的神秘盲盒类型

- 实现神秘盲盒的概率计算逻辑
- 添加相关的物品配置
- 更新UI显示

相关Issue: #45
```

## 6. 文档规范

### 6.1 代码文档
- 所有公共API必须有XML文档注释
- 复杂算法需要详细注释
- 重要的设计决策需要记录

### 6.2 设计文档
- 新功能开发前需要设计文档
- 重大重构需要技术方案文档
- 定期更新架构文档

### 6.3 用户文档
- API使用说明
- 配置文件格式说明
- 故障排除指南

## 7. 代码审查规范

### 7.1 审查清单
- [ ] 代码符合命名规范
- [ ] 有适当的注释和文档
- [ ] 没有明显的性能问题
- [ ] 错误处理完善
- [ ] 测试覆盖充分
- [ ] 符合架构设计

### 7.2 审查流程
1. 开发者自检
2. 同行代码审查
3. 技术负责人审查
4. 合并到主分支

---

*本规范文档将根据项目发展持续更新和完善*
