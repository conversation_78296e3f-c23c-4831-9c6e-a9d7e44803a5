<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2361.4609375 1132" style="max-width: 2361.4609375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9"><style>#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .error-icon{fill:#a44141;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edge-thickness-normal{stroke-width:1px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .marker.cross{stroke:lightgrey;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 p{margin:0;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .cluster-label text{fill:#F9FFFE;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .cluster-label span{color:#F9FFFE;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .cluster-label span p{background-color:transparent;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .label text,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 span{fill:#ccc;color:#ccc;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node rect,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node circle,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node ellipse,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node polygon,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .rough-node .label text,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node .label text,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .image-shape .label,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .icon-shape .label{text-anchor:middle;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .rough-node .label,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node .label,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .image-shape .label,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .icon-shape .label{text-align:center;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .node.clickable{cursor:pointer;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .arrowheadPath{fill:lightgrey;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .cluster text{fill:#F9FFFE;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .cluster span{color:#F9FFFE;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 rect.text{fill:none;stroke-width:0;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .icon-shape,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .icon-shape p,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .icon-shape rect,#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .playerClass&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .playerClass span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .coreClass&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .coreClass span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .gameClass&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .gameClass span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .dataClass&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .dataClass span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .csvClass&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .csvClass span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .uiClass&gt;*{fill:#e0f2f1!important;stroke:#004d40!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .uiClass span{fill:#e0f2f1!important;stroke:#004d40!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .engineClass&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .engineClass span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .managerClass&gt;*{fill:#ffebee!important;stroke:#b71c1c!important;stroke-width:3px!important;}#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9 .managerClass span{fill:#ffebee!important;stroke:#b71c1c!important;stroke-width:3px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="EngineLayer" class="cluster"><rect height="128" width="467.6015625" y="8" x="102.23828125" style=""></rect><g transform="translate(301.62890625, 8)" class="cluster-label"><foreignObject height="24" width="68.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔧 引擎层</p></span></div></foreignObject></g></g><g data-look="classic" id="UILayer" class="cluster"><rect height="306" width="666.9453125" y="186" x="8" style=""></rect><g transform="translate(299.6484375, 186)" class="cluster-label"><foreignObject height="24" width="83.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🖥️ UI系统层</p></span></div></foreignObject></g></g><g data-look="classic" id="CSVData" class="cluster"><rect height="128" width="1281.84375" y="8" x="1071.6171875" style=""></rect><g transform="translate(1656.796875, 8)" class="cluster-label"><foreignObject height="24" width="111.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📄 CSV数据文件</p></span></div></foreignObject></g></g><g data-look="classic" id="DataLayer" class="cluster"><rect height="128" width="1194.689453125" y="186" x="1105.234375" style=""></rect><g transform="translate(1668.1689453125, 186)" class="cluster-label"><foreignObject height="24" width="68.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 数据层</p></span></div></foreignObject></g></g><g data-look="classic" id="GameSystemLayer" class="cluster"><rect height="128" width="1314.58203125" y="364" x="938.396484375" style=""></rect><g transform="translate(1545.27734375, 364)" class="cluster-label"><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎯 游戏系统层</p></span></div></foreignObject></g></g><g data-look="classic" id="CoreLayer" class="cluster"><rect height="256" width="1132.421875" y="868" x="787.599609375" style=""></rect><g transform="translate(1303.400390625, 868)" class="cluster-label"><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ 核心系统层</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GES_GEP_2" d="M1038.732,951.625L1075.482,959.021C1112.232,966.417,1185.732,981.208,1275.514,996.531C1365.295,1011.854,1471.358,1027.709,1524.389,1035.636L1577.421,1043.563"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GES_GCP_3" d="M941.213,971L941.213,975.167C941.213,979.333,941.213,987.667,944.821,995.523C948.429,1003.38,955.644,1010.76,959.252,1014.45L962.86,1018.14"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BBM_GEP_4" d="M1316.76,459.636L1332.235,465.03C1347.71,470.424,1378.661,481.212,1394.136,490.773C1409.611,500.333,1409.611,508.667,1409.611,540C1409.611,571.333,1409.611,625.667,1409.611,680C1409.611,734.333,1409.611,788.667,1409.611,820C1409.611,851.333,1409.611,859.667,1409.611,874.5C1409.611,889.333,1409.611,910.667,1409.611,932C1409.611,953.333,1409.611,974.667,1437.589,991.78C1465.567,1008.893,1521.523,1021.785,1549.501,1028.232L1577.479,1034.678"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TS_GEP_5" d="M1741.916,467L1746.674,471.167C1751.433,475.333,1760.949,483.667,1765.707,492C1770.465,500.333,1770.465,508.667,1770.465,540C1770.465,571.333,1770.465,625.667,1770.465,680C1770.465,734.333,1770.465,788.667,1770.465,820C1770.465,851.333,1770.465,859.667,1770.465,874.5C1770.465,889.333,1770.465,910.667,1770.465,932C1770.465,953.333,1770.465,974.667,1765.584,989.093C1760.703,1003.52,1750.941,1011.039,1746.06,1014.799L1741.179,1018.559"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TS_GCP_6" d="M1620.057,446.481L1588.316,454.068C1556.575,461.654,1493.093,476.827,1461.352,488.58C1429.611,500.333,1429.611,508.667,1429.611,540C1429.611,571.333,1429.611,625.667,1429.611,680C1429.611,734.333,1429.611,788.667,1429.611,820C1429.611,851.333,1429.611,859.667,1429.611,874.5C1429.611,889.333,1429.611,910.667,1429.611,932C1429.611,953.333,1429.611,974.667,1379.069,992.93C1328.527,1011.193,1227.442,1026.385,1176.9,1033.982L1126.358,1041.578"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ES_GEP_7" d="M1900.021,467L1900.021,471.167C1900.021,475.333,1900.021,483.667,1900.021,492C1900.021,500.333,1900.021,508.667,1900.021,540C1900.021,571.333,1900.021,625.667,1900.021,680C1900.021,734.333,1900.021,788.667,1900.021,820C1900.021,851.333,1900.021,859.667,1900.021,874.5C1900.021,889.333,1900.021,910.667,1900.021,932C1900.021,953.333,1900.021,974.667,1882.887,990.49C1865.753,1006.314,1831.484,1016.628,1814.349,1021.785L1797.215,1026.942"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TDL_TS_8" d="M1441.389,289L1441.389,293.167C1441.389,297.333,1441.389,305.667,1441.389,314C1441.389,322.333,1441.389,330.667,1441.389,339C1441.389,347.333,1441.389,355.667,1470.52,367.116C1499.651,378.566,1557.914,393.132,1587.045,400.415L1616.176,407.698"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BBCD_BBM_9" d="M1700.74,289L1700.74,293.167C1700.74,297.333,1700.74,305.667,1700.74,314C1700.74,322.333,1700.74,330.667,1700.74,339C1700.74,347.333,1700.74,355.667,1637.404,368.372C1574.068,381.077,1447.396,398.153,1384.06,406.691L1320.724,415.23"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BBID_BBM_10" d="M1976.986,289L1976.986,293.167C1976.986,297.333,1976.986,305.667,1976.986,314C1976.986,322.333,1976.986,330.667,1976.986,339C1976.986,347.333,1976.986,355.667,1867.613,369.154C1758.239,382.642,1539.492,401.283,1430.119,410.604L1320.745,419.925"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TD_TDL_11" d="M1189.273,111L1189.273,115.167C1189.273,119.333,1189.273,127.667,1189.273,136C1189.273,144.333,1189.273,152.667,1189.273,161C1189.273,169.333,1189.273,177.667,1215.133,188.398C1240.993,199.129,1292.713,212.258,1318.574,218.823L1344.434,225.388"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TR_TDL_12" d="M1437.164,111L1437.164,115.167C1437.164,119.333,1437.164,127.667,1437.164,136C1437.164,144.333,1437.164,152.667,1437.164,161C1437.164,169.333,1437.164,177.667,1437.395,185.335C1437.626,193.003,1438.089,200.006,1438.32,203.507L1438.551,207.009"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TE_TDL_13" d="M1693.504,111L1693.504,115.167C1693.504,119.333,1693.504,127.667,1693.504,136C1693.504,144.333,1693.504,152.667,1693.504,161C1693.504,169.333,1693.504,177.667,1667.644,188.398C1641.784,199.129,1590.064,212.258,1564.204,218.823L1538.344,225.388"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SED_TDL_14" d="M1938.848,111L1938.848,115.167C1938.848,119.333,1938.848,127.667,1938.848,136C1938.848,144.333,1938.848,152.667,1938.848,161C1938.848,169.333,1938.848,177.667,1872.112,190.419C1805.376,203.172,1671.905,220.343,1605.17,228.929L1538.434,237.515"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEE_TDL_15" d="M2205.773,111L2205.773,115.167C2205.773,119.333,2205.773,127.667,2205.773,136C2205.773,144.333,2205.773,152.667,2205.773,161C2205.773,169.333,2205.773,177.667,2094.553,191.146C1983.333,204.624,1760.893,223.249,1649.673,232.561L1538.453,241.873"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UIS_BBP_16" d="M390.523,269.772L413.87,277.143C437.217,284.514,483.911,299.257,507.258,310.795C530.605,322.333,530.605,330.667,530.605,339C530.605,347.333,530.605,355.667,530.605,363.333C530.605,371,530.605,378,530.605,381.5L530.605,385"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UIS_TP_17" d="M328.509,289L328.574,293.167C328.639,297.333,328.769,305.667,328.834,314C328.898,322.333,328.898,330.667,328.898,339C328.898,347.333,328.898,355.667,328.898,363.333C328.898,371,328.898,378,328.898,381.5L328.898,385"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UIS_AP_18" d="M265.281,269.772L241.934,277.143C218.587,284.514,171.893,299.257,148.546,310.795C125.199,322.333,125.199,330.667,125.199,339C125.199,347.333,125.199,355.667,125.199,363.333C125.199,371,125.199,378,125.199,381.5L125.199,385"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AS_UIS_19" d="M212.566,111L212.566,115.167C212.566,119.333,212.566,127.667,212.566,136C212.566,144.333,212.566,152.667,212.566,161C212.566,169.333,212.566,177.667,220.769,186.385C228.972,195.104,245.378,204.207,253.581,208.759L261.784,213.311"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LS_UIS_20" d="M436.367,111L436.367,115.167C436.367,119.333,436.367,127.667,436.367,136C436.367,144.333,436.367,152.667,436.367,161C436.367,169.333,436.367,177.667,429.301,186.003C422.234,194.339,408.101,202.678,401.035,206.848L393.968,211.018"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GM_PlayerLayer_21" d="M976.539,111L979.566,115.167C982.593,119.333,988.647,127.667,991.674,136C994.701,144.333,994.701,152.667,994.701,161C994.701,169.333,994.701,177.667,994.701,192.5C994.701,207.333,994.701,228.667,994.701,250C994.701,271.333,994.701,292.667,994.701,307.5C994.701,322.333,994.701,330.667,944.742,339C894.783,347.333,794.864,355.667,744.905,370.5C694.945,385.333,694.945,406.667,694.945,428C694.945,449.333,694.945,470.667,694.945,485.5C694.945,500.333,694.945,508.667,700.685,516.632C706.424,524.597,717.902,532.195,723.642,535.994L729.381,539.792"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GM_GameSystemLayer_22" d="M942.113,111L941.462,115.167C940.811,119.333,939.509,127.667,938.858,136C938.207,144.333,938.207,152.667,938.207,161C938.207,169.333,938.207,177.667,938.207,192.5C938.207,207.333,938.207,228.667,938.207,250C938.207,271.333,938.207,292.667,938.207,307.5C938.207,322.333,938.207,330.667,951.414,338.808C964.621,346.949,991.035,354.898,1004.242,358.873L1017.449,362.847"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GM_UILayer_23" d="M859.797,91.294L825.655,98.745C791.513,106.196,723.229,121.098,689.087,132.716C654.945,144.333,654.945,152.667,654.945,160.333C654.945,168,654.945,175,654.945,178.5L654.945,182"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PlayerLayer_CoreLayer_24" d="M941.213,818L941.213,822.167C941.213,826.333,941.213,834.667,941.213,842.333C941.213,850,941.213,857,941.213,860.5L941.213,864"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GameSystemLayer_PlayerLayer_25" d="M1158.699,492L1158.699,496.167C1158.699,500.333,1158.699,508.667,1153.673,516.6C1148.647,524.534,1138.595,532.067,1133.569,535.834L1128.543,539.601"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UILayer_GameSystemLayer_26" d="M674.945,367.283L934.398,374.75"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(687.455078125, 534)" class="root"><g class="clusters"><g data-look="classic" id="PlayerLayer" class="cluster"><rect height="276" width="491.515625" y="8" x="8" style=""></rect><g transform="translate(203.34765625, 8)" class="cluster-label"><foreignObject height="24" width="100.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎮 玩家数据层</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PD_AM_0" d="M193.883,107L201.402,102.833C208.922,98.667,223.961,90.333,237.064,86.167C250.167,82,261.333,82,266.917,82L272.5,82"></path><path marker-end="url(#mermaid-8fd9b39d-5b1c-4d6d-b0ce-25f8aca46cc9_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PD_RM_1" d="M193.883,185L201.402,189.167C208.922,193.333,223.961,201.667,237.195,205.833C250.428,210,261.857,210,267.571,210L273.285,210"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(123.5, 146)" id="flowchart-PD-1967" class="node default playerClass"><rect height="78" width="156" y="-39" x="-78" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PlayerData<br />玩家数据中心</p></span></div></foreignObject></g></g><g transform="translate(369.2578125, 82)" id="flowchart-AM-1968" class="node default playerClass"><rect height="78" width="185.515625" y="-39" x="-92.7578125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-62.7578125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="125.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AttributeManager<br />属性管理器</p></span></div></foreignObject></g></g><g transform="translate(369.2578125, 210)" id="flowchart-RM-1969" class="node default playerClass"><rect height="78" width="183.9453125" y="-39" x="-91.97265625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.97265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="123.9453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ResourceManager<br />资源管理器</p></span></div></foreignObject></g></g></g></g><g transform="translate(941.212890625, 932)" id="flowchart-GES-1974" class="node default coreClass"><rect height="78" width="195.0390625" y="-39" x="-97.51953125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-67.51953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="135.0390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GameEffectSystem<br />效果系统</p></span></div></foreignObject></g></g><g transform="translate(1687.380859375, 1060)" id="flowchart-GEP-1975" class="node default coreClass"><rect height="78" width="212.0078125" y="-39" x="-106.00390625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.00390625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="152.0078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GameEffectProcessor<br />效果处理器</p></span></div></foreignObject></g></g><g transform="translate(1003.7890625, 1060)" id="flowchart-GCP-1976" class="node default coreClass"><rect height="78" width="237.2265625" y="-39" x="-118.61328125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-88.61328125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="177.2265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GameConditionProcessor<br />条件处理器</p></span></div></foreignObject></g></g><g transform="translate(1225.994140625, 428)" id="flowchart-BBM-1981" class="node default gameClass"><rect height="78" width="181.53125" y="-39" x="-90.765625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-60.765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="121.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BlindBoxManager<br />盲盒管理器</p></span></div></foreignObject></g></g><g transform="translate(1697.380859375, 428)" id="flowchart-TS-1982" class="node default gameClass"><rect height="78" width="154.6484375" y="-39" x="-77.32421875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-47.32421875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="94.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TalentSystem<br />天赋系统</p></span></div></foreignObject></g></g><g transform="translate(1900.021484375, 428)" id="flowchart-ES-1983" class="node default gameClass"><rect height="78" width="150.6328125" y="-39" x="-75.31640625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.31640625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="90.6328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EventSystem<br />事件系统</p></span></div></foreignObject></g></g><g transform="translate(2121.658203125, 428)" id="flowchart-SS-1984" class="node default gameClass"><rect height="78" width="143.078125" y="-39" x="-71.5390625" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.5390625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="83.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SaveSystem<br />存档系统</p></span></div></foreignObject></g></g><g transform="translate(1441.388671875, 250)" id="flowchart-TDL-1993" class="node default dataClass"><rect height="78" width="186.15625" y="-39" x="-93.078125" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-63.078125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="126.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TalentDataLoader<br />天赋数据加载器</p></span></div></foreignObject></g></g><g transform="translate(1700.740234375, 250)" id="flowchart-BBCD-1994" class="node default dataClass"><rect height="78" width="232.546875" y="-39" x="-116.2734375" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-86.2734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="172.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BlindBoxConfigDatabase<br />盲盒配置数据库</p></span></div></foreignObject></g></g><g transform="translate(1976.986328125, 250)" id="flowchart-BBID-1995" class="node default dataClass"><rect height="78" width="219.9453125" y="-39" x="-109.97265625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-79.97265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="159.9453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BlindBoxItemDatabase<br />物品数据库</p></span></div></foreignObject></g></g><g transform="translate(1189.2734375, 72)" id="flowchart-TD-2002" class="node default csvClass"><rect height="78" width="165.3125" y="-39" x="-82.65625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.65625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="105.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TalentData.csv<br />天赋基础数据</p></span></div></foreignObject></g></g><g transform="translate(1437.1640625, 72)" id="flowchart-TR-2003" class="node default csvClass"><rect height="78" width="230.46875" y="-39" x="-115.234375" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-85.234375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="170.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TalentRequirements.csv<br />天赋条件</p></span></div></foreignObject></g></g><g transform="translate(1693.50390625, 72)" id="flowchart-TE-2004" class="node default csvClass"><rect height="78" width="182.2109375" y="-39" x="-91.10546875" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.10546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="122.2109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TalentEffects.csv<br />天赋效果</p></span></div></foreignObject></g></g><g transform="translate(1938.84765625, 72)" id="flowchart-SED-2005" class="node default csvClass"><rect height="78" width="208.4765625" y="-39" x="-104.23828125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-74.23828125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="148.4765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>StatusEffectData.csv<br />状态效果数据</p></span></div></foreignObject></g></g><g transform="translate(2205.7734375, 72)" id="flowchart-SEE-2006" class="node default csvClass"><rect height="78" width="225.375" y="-39" x="-112.6875" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-82.6875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="165.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>StatusEffectEffects.csv<br />状态效果影响</p></span></div></foreignObject></g></g><g transform="translate(327.90234375, 250)" id="flowchart-UIS-2017" class="node default uiClass"><rect height="78" width="125.2421875" y="-39" x="-62.62109375" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-32.62109375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="65.2421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>UISystem<br />UI管理器</p></span></div></foreignObject></g></g><g transform="translate(530.60546875, 428)" id="flowchart-BBP-2018" class="node default uiClass"><rect height="78" width="160.4140625" y="-39" x="-80.20703125" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-50.20703125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="100.4140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BlindBoxPanel<br />盲盒界面</p></span></div></foreignObject></g></g><g transform="translate(328.8984375, 428)" id="flowchart-TP-2019" class="node default uiClass"><rect height="78" width="143" y="-39" x="-71.5" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.5, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="83"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TalentPanel<br />天赋界面</p></span></div></foreignObject></g></g><g transform="translate(125.19921875, 428)" id="flowchart-AP-2020" class="node default uiClass"><rect height="78" width="164.3984375" y="-39" x="-82.19921875" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.19921875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="104.3984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AttributePanel<br />属性界面</p></span></div></foreignObject></g></g><g transform="translate(212.56640625, 72)" id="flowchart-AS-2027" class="node default engineClass"><rect height="78" width="150.65625" y="-39" x="-75.328125" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.328125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="90.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AudioSystem<br />音频系统</p></span></div></foreignObject></g></g><g transform="translate(436.3671875, 72)" id="flowchart-LS-2028" class="node default engineClass"><rect height="78" width="196.9453125" y="-39" x="-98.47265625" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.47265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.9453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LocalizationSystem<br />本地化系统</p></span></div></foreignObject></g></g><g transform="translate(948.20703125, 72)" id="flowchart-GM-2033" class="node default managerClass"><rect height="78" width="176.8203125" y="-39" x="-88.41015625" style="fill:#ffebee !important;stroke:#b71c1c !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-58.41015625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="116.8203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GameManager<br />🎮 游戏主控制器</p></span></div></foreignObject></g></g></g></g></g></svg>