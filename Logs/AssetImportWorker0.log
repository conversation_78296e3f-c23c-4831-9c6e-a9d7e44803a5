Tuanjie Editor version:  2022.3.55t4 (a707b1712c03)
Branch:                  tuanjie/1.5/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.3.2 (Build 24D81)
Darwin version:          24.3.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/MacOS/Tuanjie
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Documents/TuanjieProject/RyanPlayEngine
-logFile
Logs/AssetImportWorker0.log
-srvPort
61113
Successfully changed project path to: /Users/<USER>/Documents/TuanjieProject/RyanPlayEngine
/Users/<USER>/Documents/TuanjieProject/RyanPlayEngine
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8708950080]  Target information:

Player connection [8708950080]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3693390572 [EditorId] 3693390572 [Version] 1048832 [Id] OSXEditor(0,LiyanhaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8708950080]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3693390572 [EditorId] 3693390572 [Version] 1048832 [Id] OSXEditor(0,LiyanhaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8708950080]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 3693390572 [EditorId] 3693390572 [Version] 1048832 [Id] OSXEditor(0,LiyanhaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8708950080] Host joined multi-casting on [***********:54997]...
Player connection [8708950080] Host joined alternative multi-casting on [***********:34997]...
AS: AutoStreaming module initializing.
[PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
Refreshing native plugins compatible for Editor in 56.35 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.55t4 (a707b1712c03)
[Subsystems] Discovering subsystems at path /Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/TuanjieProject/RyanPlayEngine/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M1 Pro (high power)
Metal devices available: 1
0: Apple M1 Pro (high power)
Using device Apple M1 Pro (high power)
Initializing Metal device caps: Apple M1 Pro
Initialize mono
Mono path[0] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed'
Mono path[1] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56415
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.005894 seconds.
- Loaded All Assemblies, in  0.274 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 131 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.368 seconds
Domain Reload Profiling: 642ms
	BeginReloadAssembly (88ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (120ms)
		LoadAssemblies (90ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (117ms)
			TypeCache.Refresh (116ms)
				TypeCache.ScanAssembly (102ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (369ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (243ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (81ms)
			ProcessInitializeOnLoadMethodAttributes (9ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.461 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.568 seconds
Domain Reload Profiling: 1025ms
	BeginReloadAssembly (72ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (12ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (334ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (124ms)
			TypeCache.Refresh (110ms)
				TypeCache.ScanAssembly (91ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (568ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (464ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (40ms)
			ProcessInitializeOnLoadAttributes (384ms)
			ProcessInitializeOnLoadMethodAttributes (16ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/TuanjieShaderCompiler
Launched and connected shader compiler TuanjieShaderCompiler after 0.05 seconds
Refreshing native plugins compatible for Editor in 2.92 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4081 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (196.3 KB). Loaded Objects now: 4576.
Memory consumption went from 157.8 MB to 157.6 MB.
Total: 3.524541 ms (FindLiveObjects: 0.133500 ms CreateObjectMapping: 0.052500 ms MarkObjects: 3.161125 ms  DeleteObjects: 0.176791 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 5752820.295242 seconds.
  path: Assets/Scripts/Engine/Localization/LocalizedText.cs
  artifactKey: Guid(ab5b3e41d8f8f4ab3a88b36ca672300a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Engine/Localization/LocalizedText.cs using Guid(ab5b3e41d8f8f4ab3a88b36ca672300a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: 'ca0465f2c34907fbd7c6d21620dc778c') in 0.001237 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.813 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.467 seconds
Domain Reload Profiling: 1280ms
	BeginReloadAssembly (550ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (445ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (467ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (353ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.48 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4058 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (175.0 KB). Loaded Objects now: 4580.
Memory consumption went from 161.8 MB to 161.6 MB.
Total: 3.189292 ms (FindLiveObjects: 0.130583 ms CreateObjectMapping: 0.052250 ms MarkObjects: 2.867000 ms  DeleteObjects: 0.139000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 099df77615f8aa8bd937a4771ea8edb3 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.653 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.958 seconds
Domain Reload Profiling: 1611ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (495ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (958ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (367ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (281ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 5.57 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4058 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (174.8 KB). Loaded Objects now: 4583.
Memory consumption went from 170.1 MB to 170.0 MB.
Total: 2.982417 ms (FindLiveObjects: 0.143875 ms CreateObjectMapping: 0.052334 ms MarkObjects: 2.644208 ms  DeleteObjects: 0.141250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 25f08916e5b3c4df19d9d36a06b61ba0 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.610 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.911 seconds
Domain Reload Profiling: 1521ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (411ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (911ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (358ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (42ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.53 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (175.0 KB). Loaded Objects now: 4586.
Memory consumption went from 179.2 MB to 179.0 MB.
Total: 3.331375 ms (FindLiveObjects: 0.132292 ms CreateObjectMapping: 0.052708 ms MarkObjects: 3.002333 ms  DeleteObjects: 0.143167 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 2e9d7eb23b08efbd93e99b57c9a78ff6 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16dcaf000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.305 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.659 seconds
Domain Reload Profiling: 964ms
	BeginReloadAssembly (101ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (157ms)
		LoadAssemblies (181ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (659ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (396ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.21 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (175.4 KB). Loaded Objects now: 4590.
Memory consumption went from 188.4 MB to 188.2 MB.
Total: 3.229250 ms (FindLiveObjects: 0.144083 ms CreateObjectMapping: 0.052375 ms MarkObjects: 2.891667 ms  DeleteObjects: 0.140584 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 2e9d7eb23b08efbd93e99b57c9a78ff6 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16dcaf000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.409 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.898 seconds
Domain Reload Profiling: 1308ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (233ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (24ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (899ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (352ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (45ms)
			ProcessInitializeOnLoadAttributes (267ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Refreshing native plugins compatible for Editor in 2.88 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4594.
Memory consumption went from 197.6 MB to 197.4 MB.
Total: 3.322625 ms (FindLiveObjects: 0.131583 ms CreateObjectMapping: 0.052292 ms MarkObjects: 2.992166 ms  DeleteObjects: 0.146125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 71c8bc9eecf4c5dce45040b459da581b -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.395 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.912 seconds
Domain Reload Profiling: 1307ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (221ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (912ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (366ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (44ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (175.3 KB). Loaded Objects now: 4598.
Memory consumption went from 206.8 MB to 206.6 MB.
Total: 3.010417 ms (FindLiveObjects: 0.128958 ms CreateObjectMapping: 0.051458 ms MarkObjects: 2.686708 ms  DeleteObjects: 0.142667 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 4c4991a387f9d89b344c251ea6e7a4bc -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.497 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.878 seconds
Domain Reload Profiling: 1376ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (306ms)
		LoadAssemblies (315ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (879ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (47ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.99 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.4 KB). Loaded Objects now: 4602.
Memory consumption went from 216.0 MB to 215.9 MB.
Total: 3.204667 ms (FindLiveObjects: 0.147375 ms CreateObjectMapping: 0.052000 ms MarkObjects: 2.846041 ms  DeleteObjects: 0.158417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 518ce1c73511bcbbe3cef39ef3edf754 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.462 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.852 seconds
Domain Reload Profiling: 1315ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (280ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (853ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (376ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (289ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.38 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.5 KB). Loaded Objects now: 4606.
Memory consumption went from 225.3 MB to 225.1 MB.
Total: 3.336000 ms (FindLiveObjects: 0.141417 ms CreateObjectMapping: 0.052917 ms MarkObjects: 2.952375 ms  DeleteObjects: 0.188625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d0439d8e15fbfe834bcf8d193c7e4935 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.481 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.984 seconds
Domain Reload Profiling: 1466ms
	BeginReloadAssembly (133ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (295ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (984ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (393ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (294ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.89 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (176.4 KB). Loaded Objects now: 4610.
Memory consumption went from 234.5 MB to 234.3 MB.
Total: 3.181167 ms (FindLiveObjects: 0.139583 ms CreateObjectMapping: 0.050209 ms MarkObjects: 2.835125 ms  DeleteObjects: 0.155709 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 76acdd4d03803357291172f7be8ac35b -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.560 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.955 seconds
Domain Reload Profiling: 1515ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (305ms)
		LoadAssemblies (308ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (48ms)
			TypeCache.Refresh (32ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (956ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (295ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 7.83 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.8 KB). Loaded Objects now: 4614.
Memory consumption went from 243.7 MB to 243.6 MB.
Total: 8.880667 ms (FindLiveObjects: 0.182333 ms CreateObjectMapping: 0.085917 ms MarkObjects: 8.401125 ms  DeleteObjects: 0.210417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 38db7fee6049f3116a928c153be14ab0 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0c40f75e89d4c5cfe73a1714d03fff19 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fdab000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.445 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.968 seconds
Domain Reload Profiling: 1413ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (281ms)
		LoadAssemblies (296ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (968ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (370ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (47ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.33 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.5 KB). Loaded Objects now: 4618.
Memory consumption went from 252.9 MB to 252.8 MB.
Total: 3.851958 ms (FindLiveObjects: 0.397708 ms CreateObjectMapping: 0.060875 ms MarkObjects: 3.136250 ms  DeleteObjects: 0.256292 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 465e5c28e48e624fe7da24058c0a6945 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16dcaf000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.427 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.843 seconds
Domain Reload Profiling: 1270ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (236ms)
		LoadAssemblies (260ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (843ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (298ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.40 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4622.
Memory consumption went from 262.2 MB to 262.0 MB.
Total: 4.349333 ms (FindLiveObjects: 0.165458 ms CreateObjectMapping: 0.051500 ms MarkObjects: 3.966417 ms  DeleteObjects: 0.165500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: a008d84bb555b08e7a9693bd7149db59 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.493 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 1429ms
	BeginReloadAssembly (137ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (296ms)
		LoadAssemblies (313ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (936ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (400ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (299ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.35 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (175.7 KB). Loaded Objects now: 4626.
Memory consumption went from 271.4 MB to 271.2 MB.
Total: 2.975625 ms (FindLiveObjects: 0.167458 ms CreateObjectMapping: 0.053333 ms MarkObjects: 2.613916 ms  DeleteObjects: 0.140417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: 29ae314c7fc78d6c56fed877ea85a8b0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d36d5d50d15adc8f32c2303b6d7d71f9 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.491 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.975 seconds
Domain Reload Profiling: 1466ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (282ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (975ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (391ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.34 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.4 KB). Loaded Objects now: 4630.
Memory consumption went from 280.6 MB to 280.4 MB.
Total: 3.496041 ms (FindLiveObjects: 0.144417 ms CreateObjectMapping: 0.052125 ms MarkObjects: 3.137542 ms  DeleteObjects: 0.161209 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: aaf6161e13402cff295483f18fc94a7d -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 2b77972713708c106c8b65961667afc6 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ff47000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.406 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.077 seconds
Domain Reload Profiling: 1483ms
	BeginReloadAssembly (127ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (231ms)
		LoadAssemblies (243ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1077ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (376ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 5.19 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4634.
Memory consumption went from 289.8 MB to 289.7 MB.
Total: 3.508333 ms (FindLiveObjects: 0.148875 ms CreateObjectMapping: 0.055083 ms MarkObjects: 3.144125 ms  DeleteObjects: 0.159000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ae6edccd2e8219ded2ec6f6060f8dcc8 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ff47000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.450 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.886 seconds
Domain Reload Profiling: 1336ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (269ms)
		LoadAssemblies (289ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (886ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (356ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (277ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.66 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (175.2 KB). Loaded Objects now: 4637.
Memory consumption went from 299.0 MB to 298.9 MB.
Total: 3.226417 ms (FindLiveObjects: 0.128875 ms CreateObjectMapping: 0.050666 ms MarkObjects: 2.904209 ms  DeleteObjects: 0.142000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: a238e41afab5ffdd90e454681d815a22 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ff47000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.533 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.939 seconds
Domain Reload Profiling: 1476ms
	BeginReloadAssembly (141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (341ms)
		LoadAssemblies (355ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (40ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (940ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (371ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (45ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.84 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4641.
Memory consumption went from 308.2 MB to 308.1 MB.
Total: 3.405583 ms (FindLiveObjects: 0.166708 ms CreateObjectMapping: 0.057208 ms MarkObjects: 3.032750 ms  DeleteObjects: 0.148000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: a40e9bd5bfc14b89c07e764369a98398 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd9f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.527 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.865 seconds
Domain Reload Profiling: 1392ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (349ms)
		LoadAssemblies (391ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (865ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (381ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.30 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4645.
Memory consumption went from 317.4 MB to 317.3 MB.
Total: 3.252833 ms (FindLiveObjects: 0.142792 ms CreateObjectMapping: 0.049125 ms MarkObjects: 2.947416 ms  DeleteObjects: 0.112917 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 08dd9789ad5792df51b3932ccee87d42 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17feab000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.411 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.872 seconds
Domain Reload Profiling: 1284ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (251ms)
		LoadAssemblies (273ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (872ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (364ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (273ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.94 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.5 KB). Loaded Objects now: 4649.
Memory consumption went from 326.7 MB to 326.5 MB.
Total: 3.128458 ms (FindLiveObjects: 0.136500 ms CreateObjectMapping: 0.050000 ms MarkObjects: 2.809834 ms  DeleteObjects: 0.131459 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 1418dcdb4256ad54be5c0bf4d4e2a878 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0c784000360f81ddcf99dc9a90352248 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ffb7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.591 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.990 seconds
Domain Reload Profiling: 1582ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (398ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (990ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (380ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (290ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.47 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4653.
Memory consumption went from 335.8 MB to 335.6 MB.
Total: 3.289125 ms (FindLiveObjects: 0.150750 ms CreateObjectMapping: 0.057542 ms MarkObjects: 2.955000 ms  DeleteObjects: 0.125208 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3380c7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.708 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.285 seconds
Domain Reload Profiling: 1993ms
	BeginReloadAssembly (354ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (43ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (126ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (302ms)
		LoadAssemblies (371ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (40ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (2ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1285ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (90ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (316ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 7.95 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4657.
Memory consumption went from 338.5 MB to 338.3 MB.
Total: 3.652541 ms (FindLiveObjects: 0.155667 ms CreateObjectMapping: 0.053375 ms MarkObjects: 3.291917 ms  DeleteObjects: 0.151125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d593000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.356 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.978 seconds
Domain Reload Profiling: 1335ms
	BeginReloadAssembly (107ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (204ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (978ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (393ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.01 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.8 KB). Loaded Objects now: 4661.
Memory consumption went from 347.6 MB to 347.4 MB.
Total: 3.176167 ms (FindLiveObjects: 0.126000 ms CreateObjectMapping: 0.051166 ms MarkObjects: 2.845000 ms  DeleteObjects: 0.153291 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16dcaf000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.359 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.926 seconds
Domain Reload Profiling: 1286ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (189ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (927ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (401ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (309ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.25 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (175.7 KB). Loaded Objects now: 4665.
Memory consumption went from 356.8 MB to 356.6 MB.
Total: 3.251084 ms (FindLiveObjects: 0.144125 ms CreateObjectMapping: 0.050750 ms MarkObjects: 2.908166 ms  DeleteObjects: 0.147250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16dcaf000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.526 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.134 seconds
Domain Reload Profiling: 1664ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (61ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (13ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1134ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (448ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (75ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4669.
Memory consumption went from 366.0 MB to 365.9 MB.
Total: 4.071167 ms (FindLiveObjects: 0.155542 ms CreateObjectMapping: 0.054000 ms MarkObjects: 3.644375 ms  DeleteObjects: 0.216375 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.565 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.946 seconds
Domain Reload Profiling: 1512ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (400ms)
		LoadAssemblies (411ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (383ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (291ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.62 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4673.
Memory consumption went from 375.2 MB to 375.1 MB.
Total: 3.298458 ms (FindLiveObjects: 0.138042 ms CreateObjectMapping: 0.052667 ms MarkObjects: 2.963041 ms  DeleteObjects: 0.143916 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.277 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.595 seconds
Domain Reload Profiling: 871ms
	BeginReloadAssembly (81ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (22ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (153ms)
		LoadAssemblies (174ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (595ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (391ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.20 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4677.
Memory consumption went from 384.5 MB to 384.3 MB.
Total: 2.887583 ms (FindLiveObjects: 0.132833 ms CreateObjectMapping: 0.059792 ms MarkObjects: 2.540458 ms  DeleteObjects: 0.154041 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.943 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.550 seconds
Domain Reload Profiling: 2493ms
	BeginReloadAssembly (448ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (167ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (437ms)
		LoadAssemblies (587ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (48ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1550ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (88ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (332ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 8.55 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4059 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4684.
Memory consumption went from 389.3 MB to 389.2 MB.
Total: 3.957584 ms (FindLiveObjects: 0.158125 ms CreateObjectMapping: 0.054250 ms MarkObjects: 3.535209 ms  DeleteObjects: 0.209334 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 280b5eb9c228c0924f43fc62e40ea8be -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Import Request.
  Time since last request: 75377.962388 seconds.
  path: Assets/Data/CSV
  artifactKey: Guid(285967bab3e65408c988167f5874ca8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Data/CSV using Guid(285967bab3e65408c988167f5874ca8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: '36f74226ff8b9a54eb40ebc3e3d9f0c8') in 0.039369 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.110280 seconds.
  path: Assets/Data/CSV/BlindBoxes.csv
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Data/CSV/BlindBoxes.csv using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: 'ab44feb9a3d8e20be36e33ed2f528671') in 0.035986 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 3.656649 seconds.
  path: Assets/Data/CSV/Items.csv
  artifactKey: Guid(03d234fd604254c2ca02c5827c9e6509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Data/CSV/Items.csv using Guid(03d234fd604254c2ca02c5827c9e6509) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: '2e431c517a162894bc2ec8058fef6e78') in 0.000794 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 665.229135 seconds.
  path: Assets/Data/CSV/Items.csv
  artifactKey: Guid(03d234fd604254c2ca02c5827c9e6509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Data/CSV/Items.csv using Guid(03d234fd604254c2ca02c5827c9e6509) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: 'f7e92715964d35dce01cec808cbe6976') in 0.030735 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f96f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.709 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.045 seconds
Domain Reload Profiling: 1755ms
	BeginReloadAssembly (293ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (458ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1045ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (394ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4059 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.8 KB). Loaded Objects now: 4690.
Memory consumption went from 396.8 MB to 396.7 MB.
Total: 3.386542 ms (FindLiveObjects: 0.147000 ms CreateObjectMapping: 0.054291 ms MarkObjects: 2.995334 ms  DeleteObjects: 0.189583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.321 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.657 seconds
Domain Reload Profiling: 979ms
	BeginReloadAssembly (96ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (179ms)
		LoadAssemblies (202ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (430ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (44ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (315ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.83 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4694.
Memory consumption went from 406.3 MB to 406.1 MB.
Total: 3.242167 ms (FindLiveObjects: 0.165875 ms CreateObjectMapping: 0.053625 ms MarkObjects: 2.891416 ms  DeleteObjects: 0.130750 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.433 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.043 seconds
Domain Reload Profiling: 1476ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (255ms)
		LoadAssemblies (302ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (1043ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (399ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 5.92 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4698.
Memory consumption went from 415.4 MB to 415.2 MB.
Total: 3.083208 ms (FindLiveObjects: 0.149875 ms CreateObjectMapping: 0.051166 ms MarkObjects: 2.729625 ms  DeleteObjects: 0.152000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Import Request.
  Time since last request: 11359.786846 seconds.
  path: Assets/Data/CSV/BlindBoxes.csv
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Data/CSV/BlindBoxes.csv using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: 'c4ac138cb1bb1fd2deeed6fac9f5c194') in 0.031281 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 668.865481 seconds.
  path: Assets/Data/ScriptableObjects/BlindBoxDataConfig.asset
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Data/ScriptableObjects/BlindBoxDataConfig.asset using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
 -> (artifact id: 'bce8ffbba58170fa787bb5d43c7d4b88') in 0.025799 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17fd1f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.550 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.175 seconds
Domain Reload Profiling: 1726ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (348ms)
		LoadAssemblies (392ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1176ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (536ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (174ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.16 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4059 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4702.
Memory consumption went from 419.7 MB to 419.6 MB.
Total: 3.357125 ms (FindLiveObjects: 0.153791 ms CreateObjectMapping: 0.056750 ms MarkObjects: 2.960792 ms  DeleteObjects: 0.185209 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ee3b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.343 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.021 seconds
Domain Reload Profiling: 1364ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (190ms)
		LoadAssemblies (217ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1021ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (402ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.58 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.4 KB). Loaded Objects now: 4706.
Memory consumption went from 429.2 MB to 429.0 MB.
Total: 3.435292 ms (FindLiveObjects: 0.133334 ms CreateObjectMapping: 0.054125 ms MarkObjects: 3.097916 ms  DeleteObjects: 0.149459 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ee3b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.372 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.007 seconds
Domain Reload Profiling: 1379ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (210ms)
		LoadAssemblies (239ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1007ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (396ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.14 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.7 KB). Loaded Objects now: 4710.
Memory consumption went from 437.8 MB to 437.6 MB.
Total: 3.454542 ms (FindLiveObjects: 0.144125 ms CreateObjectMapping: 0.055375 ms MarkObjects: 3.117833 ms  DeleteObjects: 0.136583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ee3b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.094 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.096 seconds
Domain Reload Profiling: 2190ms
	BeginReloadAssembly (683ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (148ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (228ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (362ms)
		LoadAssemblies (487ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1096ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (404ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (295ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 7.51 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.6 KB). Loaded Objects now: 4714.
Memory consumption went from 436.2 MB to 436.0 MB.
Total: 3.853500 ms (FindLiveObjects: 0.142875 ms CreateObjectMapping: 0.049958 ms MarkObjects: 3.493875 ms  DeleteObjects: 0.166167 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
