Tuanjie Editor version:  2022.3.55t4 (a707b1712c03)
Branch:                  tuanjie/1.5/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.3.2 (Build 24D81)
Darwin version:          24.3.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/MacOS/Tuanjie
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Documents/TuanjieProject/RyanPlayEngine
-logFile
Logs/AssetImportWorker1.log
-srvPort
61113
Successfully changed project path to: /Users/<USER>/Documents/TuanjieProject/RyanPlayEngine
/Users/<USER>/Documents/TuanjieProject/RyanPlayEngine
[Memory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8708950080]  Target information:

Player connection [8708950080]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 878991481 [EditorId] 878991481 [Version] 1048832 [Id] OSXEditor(0,LiyanhaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8708950080]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 878991481 [EditorId] 878991481 [Version] 1048832 [Id] OSXEditor(0,LiyanhaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8708950080]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 878991481 [EditorId] 878991481 [Version] 1048832 [Id] OSXEditor(0,LiyanhaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8708950080] Host joined multi-casting on [***********:54997]...
Player connection [8708950080] Host joined alternative multi-casting on [***********:34997]...
AS: AutoStreaming module initializing.
[PhysX] Initialized MultithreadedTaskDispatcher with 10 workers.
Refreshing native plugins compatible for Editor in 56.35 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.55t4 (a707b1712c03)
[Subsystems] Discovering subsystems at path /Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/TuanjieProject/RyanPlayEngine/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M1 Pro (high power)
Metal devices available: 1
0: Apple M1 Pro (high power)
Using device Apple M1 Pro (high power)
Initializing Metal device caps: Apple M1 Pro
Initialize mono
Mono path[0] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Managed'
Mono path[1] = '/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=0.0.0.0:56417
Begin MonoManager ReloadAssembly
Registering precompiled tuanjie dll's ...
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/WeixinMiniGameSupport/UnityEditor.WeixinMiniGame.Extensions.dll
Register platform support module: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.001710 seconds.
- Loaded All Assemblies, in  0.276 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 122 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.373 seconds
Domain Reload Profiling: 650ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (120ms)
		LoadAssemblies (89ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (119ms)
			TypeCache.Refresh (118ms)
				TypeCache.ScanAssembly (107ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (374ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (337ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (236ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (86ms)
			ProcessInitializeOnLoadMethodAttributes (10ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.471 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.570 seconds
Domain Reload Profiling: 1041ms
	BeginReloadAssembly (81ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (19ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (332ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (126ms)
				TypeCache.ScanAssembly (108ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (4ms)
	FinalizeReload (570ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (447ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (48ms)
			ProcessInitializeOnLoadAttributes (357ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Tuanjie/Hub/Editor/2022.3.55t4/Tuanjie.app/Contents/Tools/TuanjieShaderCompiler
Launched and connected shader compiler TuanjieShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.92 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4081 Unused Serialized files (Serialized files now loaded: 0)
Unloading 45 unused Assets / (195.6 KB). Loaded Objects now: 4576.
Memory consumption went from 158.0 MB to 157.8 MB.
Total: 3.276333 ms (FindLiveObjects: 0.156458 ms CreateObjectMapping: 0.052334 ms MarkObjects: 2.898166 ms  DeleteObjects: 0.168791 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x34216f000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.801 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.515 seconds
Domain Reload Profiling: 1317ms
	BeginReloadAssembly (537ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (431ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (212ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (515ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (392ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (306ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.47 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4059 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (174.5 KB). Loaded Objects now: 4580.
Memory consumption went from 162.1 MB to 161.9 MB.
Total: 3.178458 ms (FindLiveObjects: 0.128416 ms CreateObjectMapping: 0.052709 ms MarkObjects: 2.856833 ms  DeleteObjects: 0.139583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 099df77615f8aa8bd937a4771ea8edb3 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x323587000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.642 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.989 seconds
Domain Reload Profiling: 1631ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (447ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (990ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (393ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (304ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 5.58 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4058 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (174.3 KB). Loaded Objects now: 4583.
Memory consumption went from 170.2 MB to 170.0 MB.
Total: 2.907125 ms (FindLiveObjects: 0.123750 ms CreateObjectMapping: 0.052875 ms MarkObjects: 2.595875 ms  DeleteObjects: 0.133958 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 25f08916e5b3c4df19d9d36a06b61ba0 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x322fcb000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.603 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.940 seconds
Domain Reload Profiling: 1543ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (384ms)
		LoadAssemblies (401ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (940ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (393ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (300ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.52 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (174.2 KB). Loaded Objects now: 4586.
Memory consumption went from 179.2 MB to 179.1 MB.
Total: 3.272333 ms (FindLiveObjects: 0.128916 ms CreateObjectMapping: 0.053792 ms MarkObjects: 2.969625 ms  DeleteObjects: 0.119333 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 2e9d7eb23b08efbd93e99b57c9a78ff6 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d87b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.327 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.694 seconds
Domain Reload Profiling: 1022ms
	BeginReloadAssembly (107ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (165ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (695ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (432ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (331ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.54 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4590.
Memory consumption went from 188.4 MB to 188.3 MB.
Total: 3.119584 ms (FindLiveObjects: 0.147834 ms CreateObjectMapping: 0.051084 ms MarkObjects: 2.757084 ms  DeleteObjects: 0.162833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 2e9d7eb23b08efbd93e99b57c9a78ff6 -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d87b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.401 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 1331ms
	BeginReloadAssembly (139ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (200ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (930ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (289ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.88 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4594.
Memory consumption went from 197.7 MB to 197.5 MB.
Total: 3.241500 ms (FindLiveObjects: 0.138584 ms CreateObjectMapping: 0.051500 ms MarkObjects: 2.903042 ms  DeleteObjects: 0.147708 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 71c8bc9eecf4c5dce45040b459da581b -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d87b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.401 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.915 seconds
Domain Reload Profiling: 1316ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (915ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (385ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (295ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.8 KB). Loaded Objects now: 4598.
Memory consumption went from 206.9 MB to 206.7 MB.
Total: 2.893666 ms (FindLiveObjects: 0.130333 ms CreateObjectMapping: 0.049959 ms MarkObjects: 2.559750 ms  DeleteObjects: 0.152875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 4c4991a387f9d89b344c251ea6e7a4bc -> eb97b279ab4abd04b3f229ecff36cf94
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16df0b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.484 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.912 seconds
Domain Reload Profiling: 1397ms
	BeginReloadAssembly (132ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (267ms)
		LoadAssemblies (275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (913ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.02 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.1 KB). Loaded Objects now: 4602.
Memory consumption went from 216.1 MB to 215.9 MB.
Total: 2.920709 ms (FindLiveObjects: 0.124917 ms CreateObjectMapping: 0.048500 ms MarkObjects: 2.619709 ms  DeleteObjects: 0.123833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 518ce1c73511bcbbe3cef39ef3edf754 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321e87000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.448 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.888 seconds
Domain Reload Profiling: 1336ms
	BeginReloadAssembly (127ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (260ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (888ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (411ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.39 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4606.
Memory consumption went from 225.3 MB to 225.2 MB.
Total: 3.525958 ms (FindLiveObjects: 0.146875 ms CreateObjectMapping: 0.053916 ms MarkObjects: 3.147084 ms  DeleteObjects: 0.177417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d0439d8e15fbfe834bcf8d193c7e4935 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.474 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.004 seconds
Domain Reload Profiling: 1478ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (300ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1005ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (420ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (315ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (176.2 KB). Loaded Objects now: 4610.
Memory consumption went from 234.5 MB to 234.4 MB.
Total: 3.007625 ms (FindLiveObjects: 0.151875 ms CreateObjectMapping: 0.054750 ms MarkObjects: 2.677083 ms  DeleteObjects: 0.123333 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 76acdd4d03803357291172f7be8ac35b -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.557 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.996 seconds
Domain Reload Profiling: 1553ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (115ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (282ms)
		LoadAssemblies (290ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (996ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (421ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (17ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 6.97 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4614.
Memory consumption went from 243.8 MB to 243.6 MB.
Total: 12.926833 ms (FindLiveObjects: 0.171042 ms CreateObjectMapping: 0.053000 ms MarkObjects: 12.455125 ms  DeleteObjects: 0.246666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 38db7fee6049f3116a928c153be14ab0 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0c40f75e89d4c5cfe73a1714d03fff19 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.432 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.008 seconds
Domain Reload Profiling: 1440ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (256ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1008ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (406ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.47 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4618.
Memory consumption went from 253.0 MB to 252.9 MB.
Total: 3.603709 ms (FindLiveObjects: 0.339208 ms CreateObjectMapping: 0.055250 ms MarkObjects: 2.992292 ms  DeleteObjects: 0.216084 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 6376ba1bfd2a3d761671595b48af72a9 -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 465e5c28e48e624fe7da24058c0a6945 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.421 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.873 seconds
Domain Reload Profiling: 1294ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (241ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (27ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (8ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (873ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (406ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.44 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4622.
Memory consumption went from 262.2 MB to 262.1 MB.
Total: 4.463625 ms (FindLiveObjects: 0.154500 ms CreateObjectMapping: 0.052375 ms MarkObjects: 4.069792 ms  DeleteObjects: 0.185625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: a008d84bb555b08e7a9693bd7149db59 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.481 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.969 seconds
Domain Reload Profiling: 1450ms
	BeginReloadAssembly (134ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (280ms)
		LoadAssemblies (275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (970ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (428ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.32 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4626.
Memory consumption went from 271.5 MB to 271.3 MB.
Total: 3.120542 ms (FindLiveObjects: 0.149750 ms CreateObjectMapping: 0.050084 ms MarkObjects: 2.772833 ms  DeleteObjects: 0.147167 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: 29ae314c7fc78d6c56fed877ea85a8b0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: d36d5d50d15adc8f32c2303b6d7d71f9 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.483 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.995 seconds
Domain Reload Profiling: 1478ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (259ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (996ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (306ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.35 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4630.
Memory consumption went from 280.7 MB to 280.5 MB.
Total: 3.287958 ms (FindLiveObjects: 0.157125 ms CreateObjectMapping: 0.055667 ms MarkObjects: 2.945000 ms  DeleteObjects: 0.129458 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: aaf6161e13402cff295483f18fc94a7d -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 2b77972713708c106c8b65961667afc6 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.397 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.093 seconds
Domain Reload Profiling: 1490ms
	BeginReloadAssembly (132ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (208ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1093ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (392ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (292ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 5.19 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4057 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4634.
Memory consumption went from 289.9 MB to 289.7 MB.
Total: 3.395125 ms (FindLiveObjects: 0.146708 ms CreateObjectMapping: 0.053167 ms MarkObjects: 3.040416 ms  DeleteObjects: 0.154334 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ae6edccd2e8219ded2ec6f6060f8dcc8 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.442 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.911 seconds
Domain Reload Profiling: 1353ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (33ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (911ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (381ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (294ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.67 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 33 unused Assets / (174.6 KB). Loaded Objects now: 4637.
Memory consumption went from 299.1 MB to 298.9 MB.
Total: 3.226250 ms (FindLiveObjects: 0.124583 ms CreateObjectMapping: 0.049333 ms MarkObjects: 2.939000 ms  DeleteObjects: 0.112709 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: a238e41afab5ffdd90e454681d815a22 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.533 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.947 seconds
Domain Reload Profiling: 1480ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (332ms)
		LoadAssemblies (334ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (51ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (16ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (948ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (389ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (291ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.88 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.8 KB). Loaded Objects now: 4641.
Memory consumption went from 308.3 MB to 308.1 MB.
Total: 3.293166 ms (FindLiveObjects: 0.176875 ms CreateObjectMapping: 0.052792 ms MarkObjects: 2.909167 ms  DeleteObjects: 0.153208 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: a40e9bd5bfc14b89c07e764369a98398 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.506 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.910 seconds
Domain Reload Profiling: 1417ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (290ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (911ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (415ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (319ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.46 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (175.9 KB). Loaded Objects now: 4645.
Memory consumption went from 317.5 MB to 317.3 MB.
Total: 3.094333 ms (FindLiveObjects: 0.143167 ms CreateObjectMapping: 0.085000 ms MarkObjects: 2.712166 ms  DeleteObjects: 0.152833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 08dd9789ad5792df51b3932ccee87d42 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.410 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.883 seconds
Domain Reload Profiling: 1294ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (227ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (29ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (883ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (383ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (290ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 2.94 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4649.
Memory consumption went from 326.8 MB to 326.6 MB.
Total: 3.108958 ms (FindLiveObjects: 0.135542 ms CreateObjectMapping: 0.051041 ms MarkObjects: 2.797750 ms  DeleteObjects: 0.124042 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 1418dcdb4256ad54be5c0bf4d4e2a878 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 0c784000360f81ddcf99dc9a90352248 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.601 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.998 seconds
Domain Reload Profiling: 1599ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (345ms)
		LoadAssemblies (389ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (998ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (412ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (312ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.47 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4653.
Memory consumption went from 335.9 MB to 335.7 MB.
Total: 3.149542 ms (FindLiveObjects: 0.154959 ms CreateObjectMapping: 0.051792 ms MarkObjects: 2.796833 ms  DeleteObjects: 0.145125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321e87000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.716 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.282 seconds
Domain Reload Profiling: 1999ms
	BeginReloadAssembly (372ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (134ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (387ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1282ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (86ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 7.90 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4657.
Memory consumption went from 338.6 MB to 338.4 MB.
Total: 3.128084 ms (FindLiveObjects: 0.160750 ms CreateObjectMapping: 0.054459 ms MarkObjects: 2.751416 ms  DeleteObjects: 0.160417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.350 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.013 seconds
Domain Reload Profiling: 1363ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (193ms)
		LoadAssemblies (218ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1013ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (421ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.01 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4661.
Memory consumption went from 347.7 MB to 347.5 MB.
Total: 3.222917 ms (FindLiveObjects: 0.134833 ms CreateObjectMapping: 0.054792 ms MarkObjects: 2.901458 ms  DeleteObjects: 0.131250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.356 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.952 seconds
Domain Reload Profiling: 1308ms
	BeginReloadAssembly (126ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (208ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (952ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (426ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.28 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4665.
Memory consumption went from 356.9 MB to 356.7 MB.
Total: 2.937791 ms (FindLiveObjects: 0.154833 ms CreateObjectMapping: 0.052708 ms MarkObjects: 2.607000 ms  DeleteObjects: 0.122666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.539 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.127 seconds
Domain Reload Profiling: 1666ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (356ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (55ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (12ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1127ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (465ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (71ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.93 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.1 KB). Loaded Objects now: 4669.
Memory consumption went from 366.1 MB to 365.9 MB.
Total: 3.913084 ms (FindLiveObjects: 0.143125 ms CreateObjectMapping: 0.052791 ms MarkObjects: 3.572000 ms  DeleteObjects: 0.144500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.548 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.984 seconds
Domain Reload Profiling: 1533ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (379ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (38ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (985ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (311ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 3.63 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4673.
Memory consumption went from 375.3 MB to 375.2 MB.
Total: 3.250417 ms (FindLiveObjects: 0.133000 ms CreateObjectMapping: 0.049750 ms MarkObjects: 2.910583 ms  DeleteObjects: 0.156334 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.291 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.616 seconds
Domain Reload Profiling: 907ms
	BeginReloadAssembly (86ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (149ms)
		LoadAssemblies (172ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (617ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (411ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (317ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.59 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4056 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4677.
Memory consumption went from 384.5 MB to 384.4 MB.
Total: 2.807291 ms (FindLiveObjects: 0.131250 ms CreateObjectMapping: 0.050375 ms MarkObjects: 2.479334 ms  DeleteObjects: 0.145666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 61e3305c95921f4fb7648055a2694815 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.938 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.548 seconds
Domain Reload Profiling: 2486ms
	BeginReloadAssembly (480ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (159ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (58ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1548ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (85ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (338ms)
			ProcessInitializeOnLoadMethodAttributes (16ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 8.49 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4059 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.0 KB). Loaded Objects now: 4684.
Memory consumption went from 389.4 MB to 389.2 MB.
Total: 3.922459 ms (FindLiveObjects: 0.190291 ms CreateObjectMapping: 0.054875 ms MarkObjects: 3.477500 ms  DeleteObjects: 0.199167 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 280b5eb9c228c0924f43fc62e40ea8be -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321f13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.708 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.042 seconds
Domain Reload Profiling: 1749ms
	BeginReloadAssembly (291ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (356ms)
		LoadAssemblies (457ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (34ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1042ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (403ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.22 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (175.9 KB). Loaded Objects now: 4689.
Memory consumption went from 397.0 MB to 396.8 MB.
Total: 3.385667 ms (FindLiveObjects: 0.134500 ms CreateObjectMapping: 0.052083 ms MarkObjects: 3.053875 ms  DeleteObjects: 0.144625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321e87000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.333 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.682 seconds
Domain Reload Profiling: 1015ms
	BeginReloadAssembly (98ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (181ms)
		LoadAssemblies (207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (682ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (425ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (329ms)
			ProcessInitializeOnLoadMethodAttributes (16ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 2.71 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4693.
Memory consumption went from 406.2 MB to 406.0 MB.
Total: 3.329792 ms (FindLiveObjects: 0.156834 ms CreateObjectMapping: 0.052750 ms MarkObjects: 2.982709 ms  DeleteObjects: 0.136709 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321e87000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.424 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.065 seconds
Domain Reload Profiling: 1489ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (247ms)
		LoadAssemblies (286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1065ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (418ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 5.91 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.8 KB). Loaded Objects now: 4697.
Memory consumption went from 415.4 MB to 415.2 MB.
Total: 3.212334 ms (FindLiveObjects: 0.144750 ms CreateObjectMapping: 0.055041 ms MarkObjects: 2.866459 ms  DeleteObjects: 0.145334 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321e87000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.548 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.182 seconds
Domain Reload Profiling: 1731ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (390ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1182ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (546ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (171ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (301ms)
			ProcessInitializeOnLoadMethodAttributes (13ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.16 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.9 KB). Loaded Objects now: 4701.
Memory consumption went from 419.8 MB to 419.6 MB.
Total: 3.372750 ms (FindLiveObjects: 0.137875 ms CreateObjectMapping: 0.049917 ms MarkObjects: 3.028333 ms  DeleteObjects: 0.155791 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d87b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.340 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.050 seconds
Domain Reload Profiling: 1391ms
	BeginReloadAssembly (102ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (187ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1050ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (425ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (328ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.57 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (174.1 KB). Loaded Objects now: 4705.
Memory consumption went from 429.0 MB to 428.8 MB.
Total: 3.387041 ms (FindLiveObjects: 0.155000 ms CreateObjectMapping: 0.054958 ms MarkObjects: 3.025709 ms  DeleteObjects: 0.150667 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d87b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.364 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.037 seconds
Domain Reload Profiling: 1402ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (202ms)
		LoadAssemblies (228ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1037ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (417ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (321ms)
			ProcessInitializeOnLoadMethodAttributes (15ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 3.14 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.8 KB). Loaded Objects now: 4709.
Memory consumption went from 437.5 MB to 437.4 MB.
Total: 3.249666 ms (FindLiveObjects: 0.143625 ms CreateObjectMapping: 0.053208 ms MarkObjects: 2.914167 ms  DeleteObjects: 0.138166 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16d87b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.093 seconds
Native extension for iOS target not found
Native extension for WeixinMiniGame target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.127 seconds
Domain Reload Profiling: 2221ms
	BeginReloadAssembly (682ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (162ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (215ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (489ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1128ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (434ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (21ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 7.52 ms, found 5 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4060 Unused Serialized files (Serialized files now loaded: 0)
Unloading 32 unused Assets / (173.8 KB). Loaded Objects now: 4713.
Memory consumption went from 436.0 MB to 435.8 MB.
Total: 3.564625 ms (FindLiveObjects: 0.147583 ms CreateObjectMapping: 0.055833 ms MarkObjects: 3.218917 ms  DeleteObjects: 0.141625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/SimpleCSVExample.cs: e59940da347281859aff0a15824aed2a -> 
  custom:scripting/monoscript/fileName/MetaGameSystem.cs: 3febab050e113519ff91bf4b89ec0539 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/JsonDataStructures.cs: 8578af31297c99cec3b9c4726bf3a123 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/TalentSystem.cs: 7d19fa26882137a5675ec6e0e6a128fa -> 
  custom:scripting/monoscript/fileName/BlindBoxSystem.cs: ******************************** -> 
  custom:scripting/monoscript/fileName/SocialClassSystem.cs: cac8df840c33f1288114806c747ed0c0 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/GameDataSystem.cs: 98157c0c223568a70336e148ccb75eab -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:scripting/monoscript/fileName/BlindBoxConfigDatabase.cs: ******************************** -> 4f272ce54995a1e1ed5dda8f9fa47d03
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:scripting/monoscript/fileName/CSVDataImporter.cs: a77f156ef656287e6627e111524060dc -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:scripting/monoscript/fileName/TestDataSystem.cs: ccf750f45578ee1aee99c79d7d6ff81c -> ae8fa0dfe5feb7dc051df3e6efde93c7
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/EventSystem.cs: 0d1249d08fef948624cc1470e1e8f477 -> 9893ce071eb20d5c73342946ea902daa
  custom:scripting/monoscript/fileName/BlackMarketSystem.cs: 5870715a77f79eb42d8bc9c7577e8cd0 -> 0af528cc7c6e27b7d3bb3a95b7b81ede
  custom:scripting/monoscript/fileName/BlindBoxItemDatabase.cs: ******************************** -> 67251212d88637eaa5e753a527300b33
  custom:scripting/monoscript/fileName/PrivilegeSystem.cs: b7ec2e008bd4adcf9b139680336aa207 -> 7f3563700114c999a57784d7e7233d95
  custom:scripting/monoscript/fileName/WorkSystem.cs: 50337e0a47f6c7729acd2dd73d5518a1 -> 0dc5010399ba5eb56d7995323420b3cb
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp-Editor: 55f98b992836c76f6379b9577ddbbeb8 -> d9553cd15660e385839158023f0a37b4
  custom:scripting/monoscript/fileName/GameLauncher.cs: a4e614dd9d480973b16a553ae9dbc62e -> e6722ac77b17ba87ed1d68b1a87611ec
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: 879a992ab67958d352d5d99bd2eab3e0 -> eb97b279ab4abd04b3f229ecff36cf94
  custom:scripting/monoscript/fileName/AchievementSystem.cs: df04417de7e6e6edd1ffb40d51f9233c -> 743fef87ed4b561436f863688efa4bf0
